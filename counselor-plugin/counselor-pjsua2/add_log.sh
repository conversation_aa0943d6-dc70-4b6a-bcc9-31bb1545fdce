#!/bin/bash

for file in $(grep -l 'public synchronized void delete()' src/main/java/org/pjsip/pjsua2/*.java); do
    echo "Add log to file [$file]"
    sed -i '' $'s/^public class/@lombok.extern.slf4j.Slf4j\rpublic class/g' $file
    sed -i '' $'s/protected void finalize() {/protected void finalize() {\r    log.debug("object [{}] finalize called.", this);/g' $file
    sed -i '' $'s/public synchronized void delete() {/public synchronized void delete() {\r    log.debug("object [{}] delete called.", this);/g' $file
done
