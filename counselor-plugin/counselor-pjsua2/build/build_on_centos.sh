#!/bin/bash

yum install -y gcc gcc-c++ make autoconf automake libtool pcre-devel alsa-lib-devel alsa-utils pulseaudio alsa-plugins-pulseaudio
yum install -y patch

# CMake
# curl -Lk http://api.genesys.wacai.info/voice/cmake.tgz | tar -xzv -C /usr/local

## JDK
#mkdir -p /usr/local/java \
#&& curl -Lk http://api.genesys.wacai.info/voice/jdk-8u231-linux-x64.tar.gz | tar -xzv -C /usr/local/java \
#&& ln -s /usr/local/java/jdk1.8.0_231 /usr/local/java/current \
#&& echo 'export JAVA_HOME=/usr/local/java/current' >> /etc/profile \
#&& echo 'export PATH=$JAVA_HOME/bin:$PATH' >> /etc/profile \
#&& source /etc/profile \
#&& sed -i '<EMAIL>=file:/dev/<EMAIL>=file:/dev/urandom@' $JAVA_HOME/jre/lib/security/java.security

# SWIG
#./build_swig.sh

# BCG729
./build_bcg729.sh

# PJProject
./build_pjproject.sh

