#!/bin/bash

# get real path of softlink
get_real_path() {
    local f="$1"
    while [ -h "$f" ]; do
        ls=`ls -ld "$f"`
        link=`expr "$ls" : '.*-> \(.*\)$'`
            if expr "$link" : '/.*' > /dev/null; then
            f="$link"
        else
            f=`dirname "$f"`/"$link"
        fi
    done
    echo "$f"
}

prg_path=$(get_real_path "$0")
echo "Script path [$prg_path]"
pushd $(dirname $prg_path)
WORK_DIR=$(pwd)
echo "Work dir [$WORK_DIR]"

item="swig-4.0.1"

tar xzvf $item.tar.gz \
&& cd $item \
&& ./configure \
&& make && make install \
&& cd .. \
&& rm -rf $item

popd > /dev/null

