### Compile PJSIP With BCG729

> NOTE:
>
> [CMake 3.x](https://cmake.org/download/) required
>
> [SWIG](http://www.swig.org/download.html) required
>
> `fflush((FILE*) fd);` add to function `pjlib/src/pj/file_io_ansi.c#pj_file_write` before returning.

### Build On MacOS

```sh
$ ./build_on_centos.sh
```

### Build On CentOS 7

```sh
$ ./build_on_centos.sh
```

### Hard Way

#### Build Essentials

```
yum install -y gcc gcc-c++ make autoconf automake libtool pcre-devel alsa-lib-devel alsa-utils pulseaudio alsa-plugins-pulseaudio
yum install -y patch
```

#### Install CMake

```sh
$ curl -Lk http://api.genesys.wacai.info/voice/cmake.tgz | tar -xzv -C /usr/local
```

#### Install JDK

```sh
# mkdir -p /usr/local/java \
&& curl -Lk http://api.genesys.wacai.info/voice/jdk-8u231-linux-x64.tar.gz | tar -xzv -C /usr/local/java \
&& ln -s /usr/local/java/jdk1.8.0_231 /usr/local/java/current \
&& echo 'export JAVA_HOME=/usr/local/java/current' >> /etc/profile \
&& echo 'export PATH=$JAVA_HOME/bin:$PATH' >> /etc/profile \
&& source /etc/profile \
&& sed -i '<EMAIL>=file:/dev/<EMAIL>=file:/dev/urandom@' $JAVA_HOME/jre/lib/security/java.security
```

#### Install SWG

```sh
$ tar xzf swig-XXX.tar.gz
$ cd swig-XXX
$ ./configure
$ make && make install
```

#### Compile BCG729 Standalone
```sh
$ git clone git://git.linphone.org/bcg729.git
$ cd bcg729
$ cmake -DCMAKE_INSTALL_PREFIX=/data/program/pjsip \
    -DCMAKE_INSTALL_LIBDIR=/data/program/pjsip/lib \
    -DCMAKE_POSITION_INDEPENDENT_CODE:BOOL=true \
    -DENABLE_SHARED=NO
$ make && make install
```

#### Compile PJSIP

```sh
$ git clone https://github.com/pjsip/pjproject.git
$ patch -d pjproject -p1 < ./pjproject.patch
$ cd pjproject
$ echo '#define PJMEDIA_HAS_VIDEO 0
#define PJMEDIA_HAS_BCG729 1
#define PJSUA_MAX_CALLS 256
#define PJSUA_MAX_ACC 256
#define PJ_IOQUEUE_MAX_HANDLES 512
#define PJSUA_MAX_PLAYERS 512' > pjlib/include/pj/config_site.h
$ ./configure CFLAGS='-O2 -fPIC' \
    --prefix=/data/program/pjsip \
    --with-bcg729=/data/program/pjsip \
    --disable-video \
    --disable-opus \
    --disable-ssl \
    --disable-darwin-ssl \
    --disable-libyuv \
    --disable-libwebrtc \
    --disable-opencore-amr \
    --disable-sdl \
    --disable-ffmpeg \
    --disable-v4l2 \
    --disable-openh264 \
    --disable-vpx
$ make -j 16 dep && make -j 16
$ cd pjsip-apps/src/swig/java
$ make
```

#### Run PJSUA

```sh
$ pjsip-apps/bin/pjsua
```

