/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjsip_transport_state {
  public final static int PJSIP_TP_STATE_CONNECTED = 0;
  public final static int PJSIP_TP_STATE_DISCONNECTED = PJSIP_TP_STATE_CONNECTED + 1;
  public final static int PJSIP_TP_STATE_SHUTDOWN = PJSIP_TP_STATE_DISCONNECTED + 1;
  public final static int PJSIP_TP_STATE_DESTROY = PJSIP_TP_STATE_SHUTDOWN + 1;
}

