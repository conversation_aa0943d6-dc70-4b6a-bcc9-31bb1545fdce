/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class VidConfPortInfo {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected VidConfPortInfo(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(VidConfPortInfo obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_VidConfPortInfo(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setPortId(int value) {
    pjsua2JNI.VidConfPortInfo_portId_set(swigCPtr, this, value);
  }

  public int getPortId() {
    return pjsua2JNI.VidConfPortInfo_portId_get(swigCPtr, this);
  }

  public void setName(String value) {
    pjsua2JNI.VidConfPortInfo_name_set(swigCPtr, this, value);
  }

  public String getName() {
    return pjsua2JNI.VidConfPortInfo_name_get(swigCPtr, this);
  }

  public void setFormat(MediaFormatVideo value) {
    pjsua2JNI.VidConfPortInfo_format_set(swigCPtr, this, MediaFormatVideo.getCPtr(value), value);
  }

  public MediaFormatVideo getFormat() {
    long cPtr = pjsua2JNI.VidConfPortInfo_format_get(swigCPtr, this);
    return (cPtr == 0) ? null : new MediaFormatVideo(cPtr, false);
  }

  public void setListeners(IntVector value) {
    pjsua2JNI.VidConfPortInfo_listeners_set(swigCPtr, this, IntVector.getCPtr(value), value);
  }

  public IntVector getListeners() {
    long cPtr = pjsua2JNI.VidConfPortInfo_listeners_get(swigCPtr, this);
    return (cPtr == 0) ? null : new IntVector(cPtr, false);
  }

  public void setTransmitters(IntVector value) {
    pjsua2JNI.VidConfPortInfo_transmitters_set(swigCPtr, this, IntVector.getCPtr(value), value);
  }

  public IntVector getTransmitters() {
    long cPtr = pjsua2JNI.VidConfPortInfo_transmitters_get(swigCPtr, this);
    return (cPtr == 0) ? null : new IntVector(cPtr, false);
  }

  public VidConfPortInfo() {
    this(pjsua2JNI.new_VidConfPortInfo(), true);
  }

}
