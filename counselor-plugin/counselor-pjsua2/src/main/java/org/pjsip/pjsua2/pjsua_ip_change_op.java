/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjsua_ip_change_op {
  public final static int PJSUA_IP_CHANGE_OP_NULL = 0;
  public final static int PJSUA_IP_CHANGE_OP_RESTART_LIS = PJSUA_IP_CHANGE_OP_NULL + 1;
  public final static int PJSUA_IP_CHANGE_OP_ACC_SHUTDOWN_TP = PJSUA_IP_CHANGE_OP_RESTART_LIS + 1;
  public final static int PJSUA_IP_CHANGE_OP_ACC_UPDATE_CONTACT = PJSUA_IP_CHANGE_OP_ACC_SHUTDOWN_TP + 1;
  public final static int PJSUA_IP_CHANGE_OP_ACC_HANGUP_CALLS = PJSUA_IP_CHANGE_OP_ACC_UPDATE_CONTACT + 1;
  public final static int PJSUA_IP_CHANGE_OP_ACC_REINVITE_CALLS = PJSUA_IP_CHANGE_OP_ACC_HANGUP_CALLS + 1;
  public final static int PJSUA_IP_CHANGE_OP_COMPLETED = PJSUA_IP_CHANGE_OP_ACC_REINVITE_CALLS + 1;
}

