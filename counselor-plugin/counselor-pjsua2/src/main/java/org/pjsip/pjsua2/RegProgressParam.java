/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class RegProgressParam {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected RegProgressParam(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(RegProgressParam obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_RegProgressParam(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setIsRegister(boolean value) {
    pjsua2JNI.RegProgressParam_isRegister_set(swigCPtr, this, value);
  }

  public boolean getIsRegister() {
    return pjsua2JNI.RegProgressParam_isRegister_get(swigCPtr, this);
  }

  public void setCode(int value) {
    pjsua2JNI.RegProgressParam_code_set(swigCPtr, this, value);
  }

  public int getCode() {
    return pjsua2JNI.RegProgressParam_code_get(swigCPtr, this);
  }

  public RegProgressParam() {
    this(pjsua2JNI.new_RegProgressParam(), true);
  }

}
