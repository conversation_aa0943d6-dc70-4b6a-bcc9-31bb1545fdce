/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class SipHeader {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected SipHeader(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(SipHeader obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_SipHeader(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setHName(String value) {
    pjsua2JNI.SipHeader_hName_set(swigCPtr, this, value);
  }

  public String getHName() {
    return pjsua2JNI.SipHeader_hName_get(swigCPtr, this);
  }

  public void setHValue(String value) {
    pjsua2JNI.SipHeader_hValue_set(swigCPtr, this, value);
  }

  public String getHValue() {
    return pjsua2JNI.SipHeader_hValue_get(swigCPtr, this);
  }

  public SipHeader() {
    this(pjsua2JNI.new_SipHeader(), true);
  }

}
