/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjsua_call_flag {
  public final static int PJSUA_CALL_UNHOLD = 1;
  public final static int PJSUA_CALL_UPDATE_CONTACT = 2;
  public final static int PJSUA_CALL_INCLUDE_DISABLED_MEDIA = 4;
  public final static int PJSUA_CALL_NO_SDP_OFFER = 8;
  public final static int PJSUA_CALL_REINIT_MEDIA = 16;
  public final static int PJSUA_CALL_UPDATE_VIA = 32;
  public final static int PJSUA_CALL_UPDATE_TARGET = 64;
  public final static int PJSUA_CALL_SET_MEDIA_DIR = 128;
}

