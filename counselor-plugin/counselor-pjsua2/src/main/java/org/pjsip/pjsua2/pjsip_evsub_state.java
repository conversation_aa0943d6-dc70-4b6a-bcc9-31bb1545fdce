/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjsip_evsub_state {
  public final static int PJSIP_EVSUB_STATE_NULL = 0;
  public final static int PJSIP_EVSUB_STATE_SENT = PJSIP_EVSUB_STATE_NULL + 1;
  public final static int PJSIP_EVSUB_STATE_ACCEPTED = PJSIP_EVSUB_STATE_SENT + 1;
  public final static int PJSIP_EVSUB_STATE_PENDING = PJSIP_EVSUB_STATE_ACCEPTED + 1;
  public final static int PJSIP_EVSUB_STATE_ACTIVE = PJSIP_EVSUB_STATE_PENDING + 1;
  public final static int PJSIP_EVSUB_STATE_TERMINATED = PJSIP_EVSUB_STATE_ACTIVE + 1;
  public final static int PJSIP_EVSUB_STATE_UNKNOWN = PJSIP_EVSUB_STATE_TERMINATED + 1;
}

