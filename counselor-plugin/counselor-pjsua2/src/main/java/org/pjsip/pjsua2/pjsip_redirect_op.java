/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjsip_redirect_op {
  public final static int PJSIP_REDIRECT_REJECT = 0;
  public final static int PJSIP_REDIRECT_ACCEPT = PJSIP_REDIRECT_REJECT + 1;
  public final static int PJSIP_REDIRECT_ACCEPT_REPLACE = PJSIP_REDIRECT_ACCEPT + 1;
  public final static int PJSIP_REDIRECT_PENDING = PJSIP_REDIRECT_ACCEPT_REPLACE + 1;
  public final static int PJSIP_REDIRECT_STOP = PJSIP_REDIRECT_PENDING + 1;
}

