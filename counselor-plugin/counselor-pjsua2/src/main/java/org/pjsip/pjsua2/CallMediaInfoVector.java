/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class CallMediaInfoVector extends java.util.AbstractList<CallMediaInfo> implements java.util.RandomAccess {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected CallMediaInfoVector(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(CallMediaInfoVector obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_CallMediaInfoVector(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public CallMediaInfoVector(CallMediaInfo[] initialElements) {
    this();
    reserve(initialElements.length);

    for (CallMediaInfo element : initialElements) {
      add(element);
    }
  }

  public CallMediaInfoVector(Iterable<CallMediaInfo> initialElements) {
    this();
    for (CallMediaInfo element : initialElements) {
      add(element);
    }
  }

  public CallMediaInfo get(int index) {
    return doGet(index);
  }

  public CallMediaInfo set(int index, CallMediaInfo e) {
    return doSet(index, e);
  }

  public boolean add(CallMediaInfo e) {
    modCount++;
    doAdd(e);
    return true;
  }

  public void add(int index, CallMediaInfo e) {
    modCount++;
    doAdd(index, e);
  }

  public CallMediaInfo remove(int index) {
    modCount++;
    return doRemove(index);
  }

  protected void removeRange(int fromIndex, int toIndex) {
    modCount++;
    doRemoveRange(fromIndex, toIndex);
  }

  public int size() {
    return doSize();
  }

  public CallMediaInfoVector() {
    this(pjsua2JNI.new_CallMediaInfoVector__SWIG_0(), true);
  }

  public CallMediaInfoVector(CallMediaInfoVector other) {
    this(pjsua2JNI.new_CallMediaInfoVector__SWIG_1(CallMediaInfoVector.getCPtr(other), other), true);
  }

  public long capacity() {
    return pjsua2JNI.CallMediaInfoVector_capacity(swigCPtr, this);
  }

  public void reserve(long n) {
    pjsua2JNI.CallMediaInfoVector_reserve(swigCPtr, this, n);
  }

  public boolean isEmpty() {
    return pjsua2JNI.CallMediaInfoVector_isEmpty(swigCPtr, this);
  }

  public void clear() {
    pjsua2JNI.CallMediaInfoVector_clear(swigCPtr, this);
  }

  public CallMediaInfoVector(int count, CallMediaInfo value) {
    this(pjsua2JNI.new_CallMediaInfoVector__SWIG_2(count, CallMediaInfo.getCPtr(value), value), true);
  }

  private int doSize() {
    return pjsua2JNI.CallMediaInfoVector_doSize(swigCPtr, this);
  }

  private void doAdd(CallMediaInfo x) {
    pjsua2JNI.CallMediaInfoVector_doAdd__SWIG_0(swigCPtr, this, CallMediaInfo.getCPtr(x), x);
  }

  private void doAdd(int index, CallMediaInfo x) {
    pjsua2JNI.CallMediaInfoVector_doAdd__SWIG_1(swigCPtr, this, index, CallMediaInfo.getCPtr(x), x);
  }

  private CallMediaInfo doRemove(int index) {
    return new CallMediaInfo(pjsua2JNI.CallMediaInfoVector_doRemove(swigCPtr, this, index), true);
  }

  private CallMediaInfo doGet(int index) {
    return new CallMediaInfo(pjsua2JNI.CallMediaInfoVector_doGet(swigCPtr, this, index), false);
  }

  private CallMediaInfo doSet(int index, CallMediaInfo val) {
    return new CallMediaInfo(pjsua2JNI.CallMediaInfoVector_doSet(swigCPtr, this, index, CallMediaInfo.getCPtr(val), val), true);
  }

  private void doRemoveRange(int fromIndex, int toIndex) {
    pjsua2JNI.CallMediaInfoVector_doRemoveRange(swigCPtr, this, fromIndex, toIndex);
  }

}
