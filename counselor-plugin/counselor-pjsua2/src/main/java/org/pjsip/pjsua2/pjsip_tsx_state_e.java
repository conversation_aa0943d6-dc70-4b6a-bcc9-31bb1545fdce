/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjsip_tsx_state_e {
  public final static int PJSIP_TSX_STATE_NULL = 0;
  public final static int PJSIP_TSX_STATE_CALLING = PJSIP_TSX_STATE_NULL + 1;
  public final static int PJSIP_TSX_STATE_TRYING = PJSIP_TSX_STATE_CALLING + 1;
  public final static int PJSIP_TSX_STATE_PROCEEDING = PJSIP_TSX_STATE_TRYING + 1;
  public final static int PJSIP_TSX_STATE_COMPLETED = PJSIP_TSX_STATE_PROCEEDING + 1;
  public final static int PJSIP_TSX_STATE_CONFIRMED = PJSIP_TSX_STATE_COMPLETED + 1;
  public final static int PJSIP_TSX_STATE_TERMINATED = PJSIP_TSX_STATE_CONFIRMED + 1;
  public final static int PJSIP_TSX_STATE_DESTROYED = PJSIP_TSX_STATE_TERMINATED + 1;
  public final static int PJSIP_TSX_STATE_MAX = PJSIP_TSX_STATE_DESTROYED + 1;
}

