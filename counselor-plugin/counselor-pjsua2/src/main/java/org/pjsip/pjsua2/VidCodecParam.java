/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class VidCodecParam {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected VidCodecParam(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(VidCodecParam obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_VidCodecParam(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setDir(int value) {
    pjsua2JNI.VidCodecParam_dir_set(swigCPtr, this, value);
  }

  public int getDir() {
    return pjsua2JNI.VidCodecParam_dir_get(swigCPtr, this);
  }

  public void setPacking(int value) {
    pjsua2JNI.VidCodecParam_packing_set(swigCPtr, this, value);
  }

  public int getPacking() {
    return pjsua2JNI.VidCodecParam_packing_get(swigCPtr, this);
  }

  public void setEncFmt(MediaFormatVideo value) {
    pjsua2JNI.VidCodecParam_encFmt_set(swigCPtr, this, MediaFormatVideo.getCPtr(value), value);
  }

  public MediaFormatVideo getEncFmt() {
    long cPtr = pjsua2JNI.VidCodecParam_encFmt_get(swigCPtr, this);
    return (cPtr == 0) ? null : new MediaFormatVideo(cPtr, false);
  }

  public void setEncFmtp(CodecFmtpVector value) {
    pjsua2JNI.VidCodecParam_encFmtp_set(swigCPtr, this, CodecFmtpVector.getCPtr(value), value);
  }

  public CodecFmtpVector getEncFmtp() {
    long cPtr = pjsua2JNI.VidCodecParam_encFmtp_get(swigCPtr, this);
    return (cPtr == 0) ? null : new CodecFmtpVector(cPtr, false);
  }

  public void setEncMtu(long value) {
    pjsua2JNI.VidCodecParam_encMtu_set(swigCPtr, this, value);
  }

  public long getEncMtu() {
    return pjsua2JNI.VidCodecParam_encMtu_get(swigCPtr, this);
  }

  public void setDecFmt(MediaFormatVideo value) {
    pjsua2JNI.VidCodecParam_decFmt_set(swigCPtr, this, MediaFormatVideo.getCPtr(value), value);
  }

  public MediaFormatVideo getDecFmt() {
    long cPtr = pjsua2JNI.VidCodecParam_decFmt_get(swigCPtr, this);
    return (cPtr == 0) ? null : new MediaFormatVideo(cPtr, false);
  }

  public void setDecFmtp(CodecFmtpVector value) {
    pjsua2JNI.VidCodecParam_decFmtp_set(swigCPtr, this, CodecFmtpVector.getCPtr(value), value);
  }

  public CodecFmtpVector getDecFmtp() {
    long cPtr = pjsua2JNI.VidCodecParam_decFmtp_get(swigCPtr, this);
    return (cPtr == 0) ? null : new CodecFmtpVector(cPtr, false);
  }

  public void setIgnoreFmtp(boolean value) {
    pjsua2JNI.VidCodecParam_ignoreFmtp_set(swigCPtr, this, value);
  }

  public boolean getIgnoreFmtp() {
    return pjsua2JNI.VidCodecParam_ignoreFmtp_get(swigCPtr, this);
  }

  public VidCodecParam() {
    this(pjsua2JNI.new_VidCodecParam(), true);
  }

}
