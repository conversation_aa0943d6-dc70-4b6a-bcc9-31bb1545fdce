/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class MediaTransportInfo {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected MediaTransportInfo(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(MediaTransportInfo obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_MediaTransportInfo(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setLocalRtpName(String value) {
    pjsua2JNI.MediaTransportInfo_localRtpName_set(swigCPtr, this, value);
  }

  public String getLocalRtpName() {
    return pjsua2JNI.MediaTransportInfo_localRtpName_get(swigCPtr, this);
  }

  public void setLocalRtcpName(String value) {
    pjsua2JNI.MediaTransportInfo_localRtcpName_set(swigCPtr, this, value);
  }

  public String getLocalRtcpName() {
    return pjsua2JNI.MediaTransportInfo_localRtcpName_get(swigCPtr, this);
  }

  public void setSrcRtpName(String value) {
    pjsua2JNI.MediaTransportInfo_srcRtpName_set(swigCPtr, this, value);
  }

  public String getSrcRtpName() {
    return pjsua2JNI.MediaTransportInfo_srcRtpName_get(swigCPtr, this);
  }

  public void setSrcRtcpName(String value) {
    pjsua2JNI.MediaTransportInfo_srcRtcpName_set(swigCPtr, this, value);
  }

  public String getSrcRtcpName() {
    return pjsua2JNI.MediaTransportInfo_srcRtcpName_get(swigCPtr, this);
  }

  public MediaTransportInfo() {
    this(pjsua2JNI.new_MediaTransportInfo(), true);
  }

}
