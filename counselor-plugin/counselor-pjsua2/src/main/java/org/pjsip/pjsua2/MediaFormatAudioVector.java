/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class MediaFormatAudioVector extends java.util.AbstractList<MediaFormatAudio> implements java.util.RandomAccess {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected MediaFormatAudioVector(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(MediaFormatAudioVector obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_MediaFormatAudioVector(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public MediaFormatAudioVector(MediaFormatAudio[] initialElements) {
    this();
    reserve(initialElements.length);

    for (MediaFormatAudio element : initialElements) {
      add(element);
    }
  }

  public MediaFormatAudioVector(Iterable<MediaFormatAudio> initialElements) {
    this();
    for (MediaFormatAudio element : initialElements) {
      add(element);
    }
  }

  public MediaFormatAudio get(int index) {
    return doGet(index);
  }

  public MediaFormatAudio set(int index, MediaFormatAudio e) {
    return doSet(index, e);
  }

  public boolean add(MediaFormatAudio e) {
    modCount++;
    doAdd(e);
    return true;
  }

  public void add(int index, MediaFormatAudio e) {
    modCount++;
    doAdd(index, e);
  }

  public MediaFormatAudio remove(int index) {
    modCount++;
    return doRemove(index);
  }

  protected void removeRange(int fromIndex, int toIndex) {
    modCount++;
    doRemoveRange(fromIndex, toIndex);
  }

  public int size() {
    return doSize();
  }

  public MediaFormatAudioVector() {
    this(pjsua2JNI.new_MediaFormatAudioVector__SWIG_0(), true);
  }

  public MediaFormatAudioVector(MediaFormatAudioVector other) {
    this(pjsua2JNI.new_MediaFormatAudioVector__SWIG_1(MediaFormatAudioVector.getCPtr(other), other), true);
  }

  public long capacity() {
    return pjsua2JNI.MediaFormatAudioVector_capacity(swigCPtr, this);
  }

  public void reserve(long n) {
    pjsua2JNI.MediaFormatAudioVector_reserve(swigCPtr, this, n);
  }

  public boolean isEmpty() {
    return pjsua2JNI.MediaFormatAudioVector_isEmpty(swigCPtr, this);
  }

  public void clear() {
    pjsua2JNI.MediaFormatAudioVector_clear(swigCPtr, this);
  }

  public MediaFormatAudioVector(int count, MediaFormatAudio value) {
    this(pjsua2JNI.new_MediaFormatAudioVector__SWIG_2(count, MediaFormatAudio.getCPtr(value), value), true);
  }

  private int doSize() {
    return pjsua2JNI.MediaFormatAudioVector_doSize(swigCPtr, this);
  }

  private void doAdd(MediaFormatAudio x) {
    pjsua2JNI.MediaFormatAudioVector_doAdd__SWIG_0(swigCPtr, this, MediaFormatAudio.getCPtr(x), x);
  }

  private void doAdd(int index, MediaFormatAudio x) {
    pjsua2JNI.MediaFormatAudioVector_doAdd__SWIG_1(swigCPtr, this, index, MediaFormatAudio.getCPtr(x), x);
  }

  private MediaFormatAudio doRemove(int index) {
    return new MediaFormatAudio(pjsua2JNI.MediaFormatAudioVector_doRemove(swigCPtr, this, index), true);
  }

  private MediaFormatAudio doGet(int index) {
    return new MediaFormatAudio(pjsua2JNI.MediaFormatAudioVector_doGet(swigCPtr, this, index), false);
  }

  private MediaFormatAudio doSet(int index, MediaFormatAudio val) {
    return new MediaFormatAudio(pjsua2JNI.MediaFormatAudioVector_doSet(swigCPtr, this, index, MediaFormatAudio.getCPtr(val), val), true);
  }

  private void doRemoveRange(int fromIndex, int toIndex) {
    pjsua2JNI.MediaFormatAudioVector_doRemoveRange(swigCPtr, this, fromIndex, toIndex);
  }

}
