/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjsip_inv_state {
  public final static int PJSIP_INV_STATE_NULL = 0;
  public final static int PJSIP_INV_STATE_CALLING = PJSIP_INV_STATE_NULL + 1;
  public final static int PJSIP_INV_STATE_INCOMING = PJSIP_INV_STATE_CALLING + 1;
  public final static int PJSIP_INV_STATE_EARLY = PJSIP_INV_STATE_INCOMING + 1;
  public final static int PJSIP_INV_STATE_CONNECTING = PJSIP_INV_STATE_EARLY + 1;
  public final static int PJSIP_INV_STATE_CONFIRMED = PJSIP_INV_STATE_CONNECTING + 1;
  public final static int PJSIP_INV_STATE_DISCONNECTED = PJSIP_INV_STATE_CONFIRMED + 1;
}

