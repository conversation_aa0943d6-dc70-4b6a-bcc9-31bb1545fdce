/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class OnNatDetectionCompleteParam {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected OnNatDetectionCompleteParam(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(OnNatDetectionCompleteParam obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_OnNatDetectionCompleteParam(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setStatus(int value) {
    pjsua2JNI.OnNatDetectionCompleteParam_status_set(swigCPtr, this, value);
  }

  public int getStatus() {
    return pjsua2JNI.OnNatDetectionCompleteParam_status_get(swigCPtr, this);
  }

  public void setReason(String value) {
    pjsua2JNI.OnNatDetectionCompleteParam_reason_set(swigCPtr, this, value);
  }

  public String getReason() {
    return pjsua2JNI.OnNatDetectionCompleteParam_reason_get(swigCPtr, this);
  }

  public void setNatType(int value) {
    pjsua2JNI.OnNatDetectionCompleteParam_natType_set(swigCPtr, this, value);
  }

  public int getNatType() {
    return pjsua2JNI.OnNatDetectionCompleteParam_natType_get(swigCPtr, this);
  }

  public void setNatTypeName(String value) {
    pjsua2JNI.OnNatDetectionCompleteParam_natTypeName_set(swigCPtr, this, value);
  }

  public String getNatTypeName() {
    return pjsua2JNI.OnNatDetectionCompleteParam_natTypeName_get(swigCPtr, this);
  }

  public OnNatDetectionCompleteParam() {
    this(pjsua2JNI.new_OnNatDetectionCompleteParam(), true);
  }

}
