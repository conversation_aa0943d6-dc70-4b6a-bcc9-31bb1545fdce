/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class DigestCredential {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected DigestCredential(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(DigestCredential obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_DigestCredential(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setRealm(String value) {
    pjsua2JNI.DigestCredential_realm_set(swigCPtr, this, value);
  }

  public String getRealm() {
    return pjsua2JNI.DigestCredential_realm_get(swigCPtr, this);
  }

  public void setOtherParam(StringToStringMap value) {
    pjsua2JNI.DigestCredential_otherParam_set(swigCPtr, this, StringToStringMap.getCPtr(value), value);
  }

  public StringToStringMap getOtherParam() {
    long cPtr = pjsua2JNI.DigestCredential_otherParam_get(swigCPtr, this);
    return (cPtr == 0) ? null : new StringToStringMap(cPtr, false);
  }

  public void setUsername(String value) {
    pjsua2JNI.DigestCredential_username_set(swigCPtr, this, value);
  }

  public String getUsername() {
    return pjsua2JNI.DigestCredential_username_get(swigCPtr, this);
  }

  public void setNonce(String value) {
    pjsua2JNI.DigestCredential_nonce_set(swigCPtr, this, value);
  }

  public String getNonce() {
    return pjsua2JNI.DigestCredential_nonce_get(swigCPtr, this);
  }

  public void setUri(String value) {
    pjsua2JNI.DigestCredential_uri_set(swigCPtr, this, value);
  }

  public String getUri() {
    return pjsua2JNI.DigestCredential_uri_get(swigCPtr, this);
  }

  public void setResponse(String value) {
    pjsua2JNI.DigestCredential_response_set(swigCPtr, this, value);
  }

  public String getResponse() {
    return pjsua2JNI.DigestCredential_response_get(swigCPtr, this);
  }

  public void setAlgorithm(String value) {
    pjsua2JNI.DigestCredential_algorithm_set(swigCPtr, this, value);
  }

  public String getAlgorithm() {
    return pjsua2JNI.DigestCredential_algorithm_get(swigCPtr, this);
  }

  public void setCnonce(String value) {
    pjsua2JNI.DigestCredential_cnonce_set(swigCPtr, this, value);
  }

  public String getCnonce() {
    return pjsua2JNI.DigestCredential_cnonce_get(swigCPtr, this);
  }

  public void setOpaque(String value) {
    pjsua2JNI.DigestCredential_opaque_set(swigCPtr, this, value);
  }

  public String getOpaque() {
    return pjsua2JNI.DigestCredential_opaque_get(swigCPtr, this);
  }

  public void setQop(String value) {
    pjsua2JNI.DigestCredential_qop_set(swigCPtr, this, value);
  }

  public String getQop() {
    return pjsua2JNI.DigestCredential_qop_get(swigCPtr, this);
  }

  public void setNc(String value) {
    pjsua2JNI.DigestCredential_nc_set(swigCPtr, this, value);
  }

  public String getNc() {
    return pjsua2JNI.DigestCredential_nc_get(swigCPtr, this);
  }

  public DigestCredential() {
    this(pjsua2JNI.new_DigestCredential(), true);
  }

}
