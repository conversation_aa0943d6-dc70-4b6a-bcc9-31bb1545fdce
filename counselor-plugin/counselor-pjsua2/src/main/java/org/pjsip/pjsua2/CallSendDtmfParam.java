/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class CallSendDtmfParam {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected CallSendDtmfParam(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(CallSendDtmfParam obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_CallSendDtmfParam(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setMethod(int value) {
    pjsua2JNI.CallSendDtmfParam_method_set(swigCPtr, this, value);
  }

  public int getMethod() {
    return pjsua2JNI.CallSendDtmfParam_method_get(swigCPtr, this);
  }

  public void setDuration(long value) {
    pjsua2JNI.CallSendDtmfParam_duration_set(swigCPtr, this, value);
  }

  public long getDuration() {
    return pjsua2JNI.CallSendDtmfParam_duration_get(swigCPtr, this);
  }

  public void setDigits(String value) {
    pjsua2JNI.CallSendDtmfParam_digits_set(swigCPtr, this, value);
  }

  public String getDigits() {
    return pjsua2JNI.CallSendDtmfParam_digits_get(swigCPtr, this);
  }

  public CallSendDtmfParam() {
    this(pjsua2JNI.new_CallSendDtmfParam(), true);
  }

}
