/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class SslCertName {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected SslCertName(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(SslCertName obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_SslCertName(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setType(int value) {
    pjsua2JNI.SslCertName_type_set(swigCPtr, this, value);
  }

  public int getType() {
    return pjsua2JNI.SslCertName_type_get(swigCPtr, this);
  }

  public void setName(String value) {
    pjsua2JNI.SslCertName_name_set(swigCPtr, this, value);
  }

  public String getName() {
    return pjsua2JNI.SslCertName_name_get(swigCPtr, this);
  }

  public SslCertName() {
    this(pjsua2JNI.new_SslCertName(), true);
  }

}
