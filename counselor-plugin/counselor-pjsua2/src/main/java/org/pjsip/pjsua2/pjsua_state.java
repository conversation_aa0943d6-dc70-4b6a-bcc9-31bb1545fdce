/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjsua_state {
  public final static int PJSUA_STATE_NULL = 0;
  public final static int PJSUA_STATE_CREATED = PJSUA_STATE_NULL + 1;
  public final static int PJSUA_STATE_INIT = PJSUA_STATE_CREATED + 1;
  public final static int PJSUA_STATE_STARTING = PJSUA_STATE_INIT + 1;
  public final static int PJSUA_STATE_RUNNING = PJSUA_STATE_STARTING + 1;
  public final static int PJSUA_STATE_CLOSING = PJSUA_STATE_RUNNING + 1;
}

