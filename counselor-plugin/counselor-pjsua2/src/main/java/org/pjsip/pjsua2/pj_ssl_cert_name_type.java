/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pj_ssl_cert_name_type {
  public final static int PJ_SSL_CERT_NAME_UNKNOWN = 0;
  public final static int PJ_SSL_CERT_NAME_RFC822 = PJ_SSL_CERT_NAME_UNKNOWN + 1;
  public final static int PJ_SSL_CERT_NAME_DNS = PJ_SSL_CERT_NAME_RFC822 + 1;
  public final static int PJ_SSL_CERT_NAME_URI = PJ_SSL_CERT_NAME_DNS + 1;
  public final static int PJ_SSL_CERT_NAME_IP = PJ_SSL_CERT_NAME_URI + 1;
}

