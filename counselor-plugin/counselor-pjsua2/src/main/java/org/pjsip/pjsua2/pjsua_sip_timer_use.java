/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjsua_sip_timer_use {
  public final static int PJSUA_SIP_TIMER_INACTIVE = 0;
  public final static int PJSUA_SIP_TIMER_OPTIONAL = PJSUA_SIP_TIMER_INACTIVE + 1;
  public final static int PJSUA_SIP_TIMER_REQUIRED = PJSUA_SIP_TIMER_OPTIONAL + 1;
  public final static int PJSUA_SIP_TIMER_ALWAYS = PJSUA_SIP_TIMER_REQUIRED + 1;
}

