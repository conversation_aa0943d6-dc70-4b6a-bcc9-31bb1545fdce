/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjmedia_srtp_use {
  public final static int PJMEDIA_SRTP_DISABLED = 0;
  public final static int PJMEDIA_SRTP_UNKNOWN = PJMEDIA_SRTP_DISABLED;
  public final static int PJMEDIA_SRTP_OPTIONAL = PJMEDIA_SRTP_UNKNOWN + 1;
  public final static int PJMEDIA_SRTP_MANDATORY = PJMEDIA_SRTP_OPTIONAL + 1;
}

