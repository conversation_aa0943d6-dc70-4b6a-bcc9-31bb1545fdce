/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pj_ssl_sock_proto {
  public final static int PJ_SSL_SOCK_PROTO_DEFAULT = 0;
  public final static int PJ_SSL_SOCK_PROTO_SSL2 = 1 << 0;
  public final static int PJ_SSL_SOCK_PROTO_SSL3 = 1 << 1;
  public final static int PJ_SSL_SOCK_PROTO_TLS1 = 1 << 2;
  public final static int PJ_SSL_SOCK_PROTO_TLS1_1 = 1 << 3;
  public final static int PJ_SSL_SOCK_PROTO_TLS1_2 = 1 << 4;
  public final static int PJ_SSL_SOCK_PROTO_TLS1_3 = 1 << 5;
  public final static int PJ_SSL_SOCK_PROTO_SSL23 = (1 << 16) -1;
  public final static int PJ_SSL_SOCK_PROTO_ALL = PJ_SSL_SOCK_PROTO_SSL23;
  public final static int PJ_SSL_SOCK_PROTO_DTLS1 = 1 << 16;
}

