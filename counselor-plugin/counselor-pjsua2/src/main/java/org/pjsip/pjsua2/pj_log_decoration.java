/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pj_log_decoration {
  public final static int PJ_LOG_HAS_DAY_NAME = 1;
  public final static int PJ_LOG_HAS_YEAR = 2;
  public final static int PJ_LOG_HAS_MONTH = 4;
  public final static int PJ_LOG_HAS_DAY_OF_MON = 8;
  public final static int PJ_LOG_HAS_TIME = 16;
  public final static int PJ_LOG_HAS_MICRO_SEC = 32;
  public final static int PJ_LOG_HAS_SENDER = 64;
  public final static int PJ_LOG_HAS_NEWLINE = 128;
  public final static int PJ_LOG_HAS_CR = 256;
  public final static int PJ_LOG_HAS_SPACE = 512;
  public final static int PJ_LOG_HAS_COLOR = 1024;
  public final static int PJ_LOG_HAS_LEVEL_TEXT = 2048;
  public final static int PJ_LOG_HAS_THREAD_ID = 4096;
  public final static int PJ_LOG_HAS_THREAD_SWC = 8192;
  public final static int PJ_LOG_HAS_INDENT = 16384;
}

