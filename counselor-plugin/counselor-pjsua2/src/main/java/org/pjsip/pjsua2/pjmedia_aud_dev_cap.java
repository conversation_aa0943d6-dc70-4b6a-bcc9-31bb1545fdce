/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjmedia_aud_dev_cap {
  public final static int PJMEDIA_AUD_DEV_CAP_EXT_FORMAT = 1;
  public final static int PJMEDIA_AUD_DEV_CAP_INPUT_LATENCY = 2;
  public final static int PJMEDIA_AUD_DEV_CAP_OUTPUT_LATENCY = 4;
  public final static int PJMEDIA_AUD_DEV_CAP_INPUT_VOLUME_SETTING = 8;
  public final static int PJMEDIA_AUD_DEV_CAP_OUTPUT_VOLUME_SETTING = 16;
  public final static int PJMEDIA_AUD_DEV_CAP_INPUT_SIGNAL_METER = 32;
  public final static int PJMEDIA_AUD_DEV_CAP_OUTPUT_SIGNAL_METER = 64;
  public final static int PJMEDIA_AUD_DEV_CAP_INPUT_ROUTE = 128;
  public final static int PJMEDIA_AUD_DEV_CAP_INPUT_SOURCE = 128;
  public final static int PJMEDIA_AUD_DEV_CAP_OUTPUT_ROUTE = 256;
  public final static int PJMEDIA_AUD_DEV_CAP_EC = 512;
  public final static int PJMEDIA_AUD_DEV_CAP_EC_TAIL = 1024;
  public final static int PJMEDIA_AUD_DEV_CAP_VAD = 2048;
  public final static int PJMEDIA_AUD_DEV_CAP_CNG = 4096;
  public final static int PJMEDIA_AUD_DEV_CAP_PLC = 8192;
  public final static int PJMEDIA_AUD_DEV_CAP_MAX = 16384;
}

