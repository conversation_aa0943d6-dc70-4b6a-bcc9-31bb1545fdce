/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class MediaFormatVideoVector extends java.util.AbstractList<MediaFormatVideo> implements java.util.RandomAccess {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected MediaFormatVideoVector(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(MediaFormatVideoVector obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_MediaFormatVideoVector(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public MediaFormatVideoVector(MediaFormatVideo[] initialElements) {
    this();
    reserve(initialElements.length);

    for (MediaFormatVideo element : initialElements) {
      add(element);
    }
  }

  public MediaFormatVideoVector(Iterable<MediaFormatVideo> initialElements) {
    this();
    for (MediaFormatVideo element : initialElements) {
      add(element);
    }
  }

  public MediaFormatVideo get(int index) {
    return doGet(index);
  }

  public MediaFormatVideo set(int index, MediaFormatVideo e) {
    return doSet(index, e);
  }

  public boolean add(MediaFormatVideo e) {
    modCount++;
    doAdd(e);
    return true;
  }

  public void add(int index, MediaFormatVideo e) {
    modCount++;
    doAdd(index, e);
  }

  public MediaFormatVideo remove(int index) {
    modCount++;
    return doRemove(index);
  }

  protected void removeRange(int fromIndex, int toIndex) {
    modCount++;
    doRemoveRange(fromIndex, toIndex);
  }

  public int size() {
    return doSize();
  }

  public MediaFormatVideoVector() {
    this(pjsua2JNI.new_MediaFormatVideoVector__SWIG_0(), true);
  }

  public MediaFormatVideoVector(MediaFormatVideoVector other) {
    this(pjsua2JNI.new_MediaFormatVideoVector__SWIG_1(MediaFormatVideoVector.getCPtr(other), other), true);
  }

  public long capacity() {
    return pjsua2JNI.MediaFormatVideoVector_capacity(swigCPtr, this);
  }

  public void reserve(long n) {
    pjsua2JNI.MediaFormatVideoVector_reserve(swigCPtr, this, n);
  }

  public boolean isEmpty() {
    return pjsua2JNI.MediaFormatVideoVector_isEmpty(swigCPtr, this);
  }

  public void clear() {
    pjsua2JNI.MediaFormatVideoVector_clear(swigCPtr, this);
  }

  public MediaFormatVideoVector(int count, MediaFormatVideo value) {
    this(pjsua2JNI.new_MediaFormatVideoVector__SWIG_2(count, MediaFormatVideo.getCPtr(value), value), true);
  }

  private int doSize() {
    return pjsua2JNI.MediaFormatVideoVector_doSize(swigCPtr, this);
  }

  private void doAdd(MediaFormatVideo x) {
    pjsua2JNI.MediaFormatVideoVector_doAdd__SWIG_0(swigCPtr, this, MediaFormatVideo.getCPtr(x), x);
  }

  private void doAdd(int index, MediaFormatVideo x) {
    pjsua2JNI.MediaFormatVideoVector_doAdd__SWIG_1(swigCPtr, this, index, MediaFormatVideo.getCPtr(x), x);
  }

  private MediaFormatVideo doRemove(int index) {
    return new MediaFormatVideo(pjsua2JNI.MediaFormatVideoVector_doRemove(swigCPtr, this, index), true);
  }

  private MediaFormatVideo doGet(int index) {
    return new MediaFormatVideo(pjsua2JNI.MediaFormatVideoVector_doGet(swigCPtr, this, index), false);
  }

  private MediaFormatVideo doSet(int index, MediaFormatVideo val) {
    return new MediaFormatVideo(pjsua2JNI.MediaFormatVideoVector_doSet(swigCPtr, this, index, MediaFormatVideo.getCPtr(val), val), true);
  }

  private void doRemoveRange(int fromIndex, int toIndex) {
    pjsua2JNI.MediaFormatVideoVector_doRemoveRange(swigCPtr, this, fromIndex, toIndex);
  }

}
