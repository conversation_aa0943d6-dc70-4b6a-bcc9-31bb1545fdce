/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class RtcpFbCapVector extends java.util.AbstractList<RtcpFbCap> implements java.util.RandomAccess {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected RtcpFbCapVector(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(RtcpFbCapVector obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_RtcpFbCapVector(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public RtcpFbCapVector(RtcpFbCap[] initialElements) {
    this();
    reserve(initialElements.length);

    for (RtcpFbCap element : initialElements) {
      add(element);
    }
  }

  public RtcpFbCapVector(Iterable<RtcpFbCap> initialElements) {
    this();
    for (RtcpFbCap element : initialElements) {
      add(element);
    }
  }

  public RtcpFbCap get(int index) {
    return doGet(index);
  }

  public RtcpFbCap set(int index, RtcpFbCap e) {
    return doSet(index, e);
  }

  public boolean add(RtcpFbCap e) {
    modCount++;
    doAdd(e);
    return true;
  }

  public void add(int index, RtcpFbCap e) {
    modCount++;
    doAdd(index, e);
  }

  public RtcpFbCap remove(int index) {
    modCount++;
    return doRemove(index);
  }

  protected void removeRange(int fromIndex, int toIndex) {
    modCount++;
    doRemoveRange(fromIndex, toIndex);
  }

  public int size() {
    return doSize();
  }

  public RtcpFbCapVector() {
    this(pjsua2JNI.new_RtcpFbCapVector__SWIG_0(), true);
  }

  public RtcpFbCapVector(RtcpFbCapVector other) {
    this(pjsua2JNI.new_RtcpFbCapVector__SWIG_1(RtcpFbCapVector.getCPtr(other), other), true);
  }

  public long capacity() {
    return pjsua2JNI.RtcpFbCapVector_capacity(swigCPtr, this);
  }

  public void reserve(long n) {
    pjsua2JNI.RtcpFbCapVector_reserve(swigCPtr, this, n);
  }

  public boolean isEmpty() {
    return pjsua2JNI.RtcpFbCapVector_isEmpty(swigCPtr, this);
  }

  public void clear() {
    pjsua2JNI.RtcpFbCapVector_clear(swigCPtr, this);
  }

  public RtcpFbCapVector(int count, RtcpFbCap value) {
    this(pjsua2JNI.new_RtcpFbCapVector__SWIG_2(count, RtcpFbCap.getCPtr(value), value), true);
  }

  private int doSize() {
    return pjsua2JNI.RtcpFbCapVector_doSize(swigCPtr, this);
  }

  private void doAdd(RtcpFbCap x) {
    pjsua2JNI.RtcpFbCapVector_doAdd__SWIG_0(swigCPtr, this, RtcpFbCap.getCPtr(x), x);
  }

  private void doAdd(int index, RtcpFbCap x) {
    pjsua2JNI.RtcpFbCapVector_doAdd__SWIG_1(swigCPtr, this, index, RtcpFbCap.getCPtr(x), x);
  }

  private RtcpFbCap doRemove(int index) {
    return new RtcpFbCap(pjsua2JNI.RtcpFbCapVector_doRemove(swigCPtr, this, index), true);
  }

  private RtcpFbCap doGet(int index) {
    return new RtcpFbCap(pjsua2JNI.RtcpFbCapVector_doGet(swigCPtr, this, index), false);
  }

  private RtcpFbCap doSet(int index, RtcpFbCap val) {
    return new RtcpFbCap(pjsua2JNI.RtcpFbCapVector_doSet(swigCPtr, this, index, RtcpFbCap.getCPtr(val), val), true);
  }

  private void doRemoveRange(int fromIndex, int toIndex) {
    pjsua2JNI.RtcpFbCapVector_doRemoveRange(swigCPtr, this, fromIndex, toIndex);
  }

}
