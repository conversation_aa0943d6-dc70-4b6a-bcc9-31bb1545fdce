/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class BuddyInfo {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected BuddyInfo(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(BuddyInfo obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_BuddyInfo(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setUri(String value) {
    pjsua2JNI.BuddyInfo_uri_set(swigCPtr, this, value);
  }

  public String getUri() {
    return pjsua2JNI.BuddyInfo_uri_get(swigCPtr, this);
  }

  public void setContact(String value) {
    pjsua2JNI.BuddyInfo_contact_set(swigCPtr, this, value);
  }

  public String getContact() {
    return pjsua2JNI.BuddyInfo_contact_get(swigCPtr, this);
  }

  public void setPresMonitorEnabled(boolean value) {
    pjsua2JNI.BuddyInfo_presMonitorEnabled_set(swigCPtr, this, value);
  }

  public boolean getPresMonitorEnabled() {
    return pjsua2JNI.BuddyInfo_presMonitorEnabled_get(swigCPtr, this);
  }

  public void setSubState(int value) {
    pjsua2JNI.BuddyInfo_subState_set(swigCPtr, this, value);
  }

  public int getSubState() {
    return pjsua2JNI.BuddyInfo_subState_get(swigCPtr, this);
  }

  public void setSubStateName(String value) {
    pjsua2JNI.BuddyInfo_subStateName_set(swigCPtr, this, value);
  }

  public String getSubStateName() {
    return pjsua2JNI.BuddyInfo_subStateName_get(swigCPtr, this);
  }

  public void setSubTermCode(int value) {
    pjsua2JNI.BuddyInfo_subTermCode_set(swigCPtr, this, value);
  }

  public int getSubTermCode() {
    return pjsua2JNI.BuddyInfo_subTermCode_get(swigCPtr, this);
  }

  public void setSubTermReason(String value) {
    pjsua2JNI.BuddyInfo_subTermReason_set(swigCPtr, this, value);
  }

  public String getSubTermReason() {
    return pjsua2JNI.BuddyInfo_subTermReason_get(swigCPtr, this);
  }

  public void setPresStatus(PresenceStatus value) {
    pjsua2JNI.BuddyInfo_presStatus_set(swigCPtr, this, PresenceStatus.getCPtr(value), value);
  }

  public PresenceStatus getPresStatus() {
    long cPtr = pjsua2JNI.BuddyInfo_presStatus_get(swigCPtr, this);
    return (cPtr == 0) ? null : new PresenceStatus(cPtr, false);
  }

  public BuddyInfo() {
    this(pjsua2JNI.new_BuddyInfo(), true);
  }

}
