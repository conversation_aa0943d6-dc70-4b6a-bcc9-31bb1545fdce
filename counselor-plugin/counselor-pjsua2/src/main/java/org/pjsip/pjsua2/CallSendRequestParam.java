/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class CallSendRequestParam {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected CallSendRequestParam(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(CallSendRequestParam obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_CallSendRequestParam(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setMethod(String value) {
    pjsua2JNI.CallSendRequestParam_method_set(swigCPtr, this, value);
  }

  public String getMethod() {
    return pjsua2JNI.CallSendRequestParam_method_get(swigCPtr, this);
  }

  public void setTxOption(SipTxOption value) {
    pjsua2JNI.CallSendRequestParam_txOption_set(swigCPtr, this, SipTxOption.getCPtr(value), value);
  }

  public SipTxOption getTxOption() {
    long cPtr = pjsua2JNI.CallSendRequestParam_txOption_get(swigCPtr, this);
    return (cPtr == 0) ? null : new SipTxOption(cPtr, false);
  }

  public CallSendRequestParam() {
    this(pjsua2JNI.new_CallSendRequestParam(), true);
  }

}
