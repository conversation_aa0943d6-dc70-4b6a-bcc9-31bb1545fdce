/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class BuddyVector2 extends java.util.AbstractList<Buddy> implements java.util.RandomAccess {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected BuddyVector2(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(BuddyVector2 obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_BuddyVector2(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public BuddyVector2(Buddy[] initialElements) {
    this();
    reserve(initialElements.length);

    for (Buddy element : initialElements) {
      add(element);
    }
  }

  public BuddyVector2(Iterable<Buddy> initialElements) {
    this();
    for (Buddy element : initialElements) {
      add(element);
    }
  }

  public Buddy get(int index) {
    return doGet(index);
  }

  public Buddy set(int index, Buddy e) {
    return doSet(index, e);
  }

  public boolean add(Buddy e) {
    modCount++;
    doAdd(e);
    return true;
  }

  public void add(int index, Buddy e) {
    modCount++;
    doAdd(index, e);
  }

  public Buddy remove(int index) {
    modCount++;
    return doRemove(index);
  }

  protected void removeRange(int fromIndex, int toIndex) {
    modCount++;
    doRemoveRange(fromIndex, toIndex);
  }

  public int size() {
    return doSize();
  }

  public BuddyVector2() {
    this(pjsua2JNI.new_BuddyVector2__SWIG_0(), true);
  }

  public BuddyVector2(BuddyVector2 other) {
    this(pjsua2JNI.new_BuddyVector2__SWIG_1(BuddyVector2.getCPtr(other), other), true);
  }

  public long capacity() {
    return pjsua2JNI.BuddyVector2_capacity(swigCPtr, this);
  }

  public void reserve(long n) {
    pjsua2JNI.BuddyVector2_reserve(swigCPtr, this, n);
  }

  public boolean isEmpty() {
    return pjsua2JNI.BuddyVector2_isEmpty(swigCPtr, this);
  }

  public void clear() {
    pjsua2JNI.BuddyVector2_clear(swigCPtr, this);
  }

  public BuddyVector2(int count, Buddy value) {
    this(pjsua2JNI.new_BuddyVector2__SWIG_2(count, Buddy.getCPtr(value), value), true);
  }

  private int doSize() {
    return pjsua2JNI.BuddyVector2_doSize(swigCPtr, this);
  }

  private void doAdd(Buddy x) {
    pjsua2JNI.BuddyVector2_doAdd__SWIG_0(swigCPtr, this, Buddy.getCPtr(x), x);
  }

  private void doAdd(int index, Buddy x) {
    pjsua2JNI.BuddyVector2_doAdd__SWIG_1(swigCPtr, this, index, Buddy.getCPtr(x), x);
  }

  private Buddy doRemove(int index) {
    return new Buddy(pjsua2JNI.BuddyVector2_doRemove(swigCPtr, this, index), true);
  }

  private Buddy doGet(int index) {
    return new Buddy(pjsua2JNI.BuddyVector2_doGet(swigCPtr, this, index), false);
  }

  private Buddy doSet(int index, Buddy val) {
    return new Buddy(pjsua2JNI.BuddyVector2_doSet(swigCPtr, this, index, Buddy.getCPtr(val), val), true);
  }

  private void doRemoveRange(int fromIndex, int toIndex) {
    pjsua2JNI.BuddyVector2_doRemoveRange(swigCPtr, this, fromIndex, toIndex);
  }

}
