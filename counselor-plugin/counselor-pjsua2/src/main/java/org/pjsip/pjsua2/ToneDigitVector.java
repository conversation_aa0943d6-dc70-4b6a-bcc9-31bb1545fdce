/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class ToneDigitVector extends java.util.AbstractList<ToneDigit> implements java.util.RandomAccess {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected ToneDigitVector(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(ToneDigitVector obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_ToneDigitVector(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public ToneDigitVector(ToneDigit[] initialElements) {
    this();
    reserve(initialElements.length);

    for (ToneDigit element : initialElements) {
      add(element);
    }
  }

  public ToneDigitVector(Iterable<ToneDigit> initialElements) {
    this();
    for (ToneDigit element : initialElements) {
      add(element);
    }
  }

  public ToneDigit get(int index) {
    return doGet(index);
  }

  public ToneDigit set(int index, ToneDigit e) {
    return doSet(index, e);
  }

  public boolean add(ToneDigit e) {
    modCount++;
    doAdd(e);
    return true;
  }

  public void add(int index, ToneDigit e) {
    modCount++;
    doAdd(index, e);
  }

  public ToneDigit remove(int index) {
    modCount++;
    return doRemove(index);
  }

  protected void removeRange(int fromIndex, int toIndex) {
    modCount++;
    doRemoveRange(fromIndex, toIndex);
  }

  public int size() {
    return doSize();
  }

  public ToneDigitVector() {
    this(pjsua2JNI.new_ToneDigitVector__SWIG_0(), true);
  }

  public ToneDigitVector(ToneDigitVector other) {
    this(pjsua2JNI.new_ToneDigitVector__SWIG_1(ToneDigitVector.getCPtr(other), other), true);
  }

  public long capacity() {
    return pjsua2JNI.ToneDigitVector_capacity(swigCPtr, this);
  }

  public void reserve(long n) {
    pjsua2JNI.ToneDigitVector_reserve(swigCPtr, this, n);
  }

  public boolean isEmpty() {
    return pjsua2JNI.ToneDigitVector_isEmpty(swigCPtr, this);
  }

  public void clear() {
    pjsua2JNI.ToneDigitVector_clear(swigCPtr, this);
  }

  public ToneDigitVector(int count, ToneDigit value) {
    this(pjsua2JNI.new_ToneDigitVector__SWIG_2(count, ToneDigit.getCPtr(value), value), true);
  }

  private int doSize() {
    return pjsua2JNI.ToneDigitVector_doSize(swigCPtr, this);
  }

  private void doAdd(ToneDigit x) {
    pjsua2JNI.ToneDigitVector_doAdd__SWIG_0(swigCPtr, this, ToneDigit.getCPtr(x), x);
  }

  private void doAdd(int index, ToneDigit x) {
    pjsua2JNI.ToneDigitVector_doAdd__SWIG_1(swigCPtr, this, index, ToneDigit.getCPtr(x), x);
  }

  private ToneDigit doRemove(int index) {
    return new ToneDigit(pjsua2JNI.ToneDigitVector_doRemove(swigCPtr, this, index), true);
  }

  private ToneDigit doGet(int index) {
    return new ToneDigit(pjsua2JNI.ToneDigitVector_doGet(swigCPtr, this, index), false);
  }

  private ToneDigit doSet(int index, ToneDigit val) {
    return new ToneDigit(pjsua2JNI.ToneDigitVector_doSet(swigCPtr, this, index, ToneDigit.getCPtr(val), val), true);
  }

  private void doRemoveRange(int fromIndex, int toIndex) {
    pjsua2JNI.ToneDigitVector_doRemoveRange(swigCPtr, this, fromIndex, toIndex);
  }

}
