/*
 * @Author: shunhua
 * @Date: 2024-12-19 10:39:51
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-22 19:22:27
 * @Description: 
 */
/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class SWIGTYPE_p_void {
  private transient long swigCPtr;

  protected SWIGTYPE_p_void(long cPtr, boolean futureUse) {
    swigCPtr = cPtr;
  }

  protected SWIGTYPE_p_void() {
    swigCPtr = 0;
  }

  protected static long getCPtr(SWIGTYPE_p_void obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }
}

