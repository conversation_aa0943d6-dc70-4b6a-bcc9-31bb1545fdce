/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjsua_call_vid_strm_op {
  public final static int PJSUA_CALL_VID_STRM_NO_OP = 0;
  public final static int PJSUA_CALL_VID_STRM_ADD = PJSUA_CALL_VID_STRM_NO_OP + 1;
  public final static int PJSUA_CALL_VID_STRM_REMOVE = PJSUA_CALL_VID_STRM_ADD + 1;
  public final static int PJSUA_CALL_VID_STRM_CHANGE_DIR = PJSUA_CALL_VID_STRM_REMOVE + 1;
  public final static int PJSUA_CALL_VID_STRM_CHANGE_CAP_DEV = PJSUA_CALL_VID_STRM_CHANGE_DIR + 1;
  public final static int PJSUA_CALL_VID_STRM_START_TRANSMIT = PJSUA_CALL_VID_STRM_CHANGE_CAP_DEV + 1;
  public final static int PJSUA_CALL_VID_STRM_STOP_TRANSMIT = PJSUA_CALL_VID_STRM_START_TRANSMIT + 1;
  public final static int PJSUA_CALL_VID_STRM_SEND_KEYFRAME = PJSUA_CALL_VID_STRM_STOP_TRANSMIT + 1;
}

