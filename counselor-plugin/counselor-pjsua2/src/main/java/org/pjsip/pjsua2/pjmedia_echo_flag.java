/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjmedia_echo_flag {
  public final static int PJMEDIA_ECHO_DEFAULT = 0;
  public final static int PJMEDIA_ECHO_SPEEX = 1;
  public final static int PJMEDIA_ECHO_SIMPLE = 2;
  public final static int PJMEDIA_ECHO_WEBRTC = 3;
  public final static int PJMEDIA_ECHO_WEBRTC_AEC3 = 4;
  public final static int PJMEDIA_ECHO_ALGO_MASK = 15;
  public final static int PJMEDIA_ECHO_NO_LOCK = 16;
  public final static int PJMEDIA_ECHO_USE_SIMPLE_FIFO = 32;
  public final static int PJMEDIA_ECHO_USE_SW_ECHO = 64;
  public final static int PJMEDIA_ECHO_USE_NOISE_SUPPRESSOR = 128;
  public final static int PJMEDIA_ECHO_USE_GAIN_CONTROLLER = 256;
  public final static int PJMEDIA_ECHO_AGGRESSIVENESS_DEFAULT = 0;
  public final static int PJMEDIA_ECHO_AGGRESSIVENESS_CONSERVATIVE = 0x1000;
  public final static int PJMEDIA_ECHO_AGGRESSIVENESS_MODERATE = 0x2000;
  public final static int PJMEDIA_ECHO_AGGRESSIVENESS_AGGRESSIVE = 0x3000;
  public final static int PJMEDIA_ECHO_AGGRESSIVENESS_MASK = 0xF000;
}

