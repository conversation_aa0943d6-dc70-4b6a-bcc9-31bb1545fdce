/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class DigestChallenge {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected DigestChallenge(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(DigestChallenge obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_DigestChallenge(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setRealm(String value) {
    pjsua2JNI.DigestChallenge_realm_set(swigCPtr, this, value);
  }

  public String getRealm() {
    return pjsua2JNI.DigestChallenge_realm_get(swigCPtr, this);
  }

  public void setOtherParam(StringToStringMap value) {
    pjsua2JNI.DigestChallenge_otherParam_set(swigCPtr, this, StringToStringMap.getCPtr(value), value);
  }

  public StringToStringMap getOtherParam() {
    long cPtr = pjsua2JNI.DigestChallenge_otherParam_get(swigCPtr, this);
    return (cPtr == 0) ? null : new StringToStringMap(cPtr, false);
  }

  public void setDomain(String value) {
    pjsua2JNI.DigestChallenge_domain_set(swigCPtr, this, value);
  }

  public String getDomain() {
    return pjsua2JNI.DigestChallenge_domain_get(swigCPtr, this);
  }

  public void setNonce(String value) {
    pjsua2JNI.DigestChallenge_nonce_set(swigCPtr, this, value);
  }

  public String getNonce() {
    return pjsua2JNI.DigestChallenge_nonce_get(swigCPtr, this);
  }

  public void setOpaque(String value) {
    pjsua2JNI.DigestChallenge_opaque_set(swigCPtr, this, value);
  }

  public String getOpaque() {
    return pjsua2JNI.DigestChallenge_opaque_get(swigCPtr, this);
  }

  public void setStale(int value) {
    pjsua2JNI.DigestChallenge_stale_set(swigCPtr, this, value);
  }

  public int getStale() {
    return pjsua2JNI.DigestChallenge_stale_get(swigCPtr, this);
  }

  public void setAlgorithm(String value) {
    pjsua2JNI.DigestChallenge_algorithm_set(swigCPtr, this, value);
  }

  public String getAlgorithm() {
    return pjsua2JNI.DigestChallenge_algorithm_get(swigCPtr, this);
  }

  public void setQop(String value) {
    pjsua2JNI.DigestChallenge_qop_set(swigCPtr, this, value);
  }

  public String getQop() {
    return pjsua2JNI.DigestChallenge_qop_get(swigCPtr, this);
  }

  public DigestChallenge() {
    this(pjsua2JNI.new_DigestChallenge(), true);
  }

}
