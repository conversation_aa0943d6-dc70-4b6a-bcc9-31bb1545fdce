/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class pjsua2JNI {

  public final static native void pj_qos_params_flags_set(long jarg1, pj_qos_params jarg1_, short jarg2);
  public final static native short pj_qos_params_flags_get(long jarg1, pj_qos_params jarg1_);
  public final static native void pj_qos_params_dscp_val_set(long jarg1, pj_qos_params jarg1_, short jarg2);
  public final static native short pj_qos_params_dscp_val_get(long jarg1, pj_qos_params jarg1_);
  public final static native void pj_qos_params_so_prio_set(long jarg1, pj_qos_params jarg1_, short jarg2);
  public final static native short pj_qos_params_so_prio_get(long jarg1, pj_qos_params jarg1_);
  public final static native void pj_qos_params_wmm_prio_set(long jarg1, pj_qos_params jarg1_, int jarg2);
  public final static native int pj_qos_params_wmm_prio_get(long jarg1, pj_qos_params jarg1_);
  public final static native long new_pj_qos_params();
  public final static native void delete_pj_qos_params(long jarg1);
  public final static native void pjmedia_tone_digit_digit_set(long jarg1, pjmedia_tone_digit jarg1_, char jarg2);
  public final static native char pjmedia_tone_digit_digit_get(long jarg1, pjmedia_tone_digit jarg1_);
  public final static native void pjmedia_tone_digit_on_msec_set(long jarg1, pjmedia_tone_digit jarg1_, short jarg2);
  public final static native short pjmedia_tone_digit_on_msec_get(long jarg1, pjmedia_tone_digit jarg1_);
  public final static native void pjmedia_tone_digit_off_msec_set(long jarg1, pjmedia_tone_digit jarg1_, short jarg2);
  public final static native short pjmedia_tone_digit_off_msec_get(long jarg1, pjmedia_tone_digit jarg1_);
  public final static native void pjmedia_tone_digit_volume_set(long jarg1, pjmedia_tone_digit jarg1_, short jarg2);
  public final static native short pjmedia_tone_digit_volume_get(long jarg1, pjmedia_tone_digit jarg1_);
  public final static native long new_pjmedia_tone_digit();
  public final static native void delete_pjmedia_tone_digit(long jarg1);
  public final static native void pjmedia_tone_digit_map_count_set(long jarg1, pjmedia_tone_digit_map jarg1_, long jarg2);
  public final static native long pjmedia_tone_digit_map_count_get(long jarg1, pjmedia_tone_digit_map jarg1_);
  public final static native long new_pjmedia_tone_digit_map();
  public final static native void delete_pjmedia_tone_digit_map(long jarg1);
  public final static native void pjmedia_tone_desc_freq1_set(long jarg1, pjmedia_tone_desc jarg1_, short jarg2);
  public final static native short pjmedia_tone_desc_freq1_get(long jarg1, pjmedia_tone_desc jarg1_);
  public final static native void pjmedia_tone_desc_freq2_set(long jarg1, pjmedia_tone_desc jarg1_, short jarg2);
  public final static native short pjmedia_tone_desc_freq2_get(long jarg1, pjmedia_tone_desc jarg1_);
  public final static native void pjmedia_tone_desc_on_msec_set(long jarg1, pjmedia_tone_desc jarg1_, short jarg2);
  public final static native short pjmedia_tone_desc_on_msec_get(long jarg1, pjmedia_tone_desc jarg1_);
  public final static native void pjmedia_tone_desc_off_msec_set(long jarg1, pjmedia_tone_desc jarg1_, short jarg2);
  public final static native short pjmedia_tone_desc_off_msec_get(long jarg1, pjmedia_tone_desc jarg1_);
  public final static native void pjmedia_tone_desc_volume_set(long jarg1, pjmedia_tone_desc jarg1_, short jarg2);
  public final static native short pjmedia_tone_desc_volume_get(long jarg1, pjmedia_tone_desc jarg1_);
  public final static native void pjmedia_tone_desc_flags_set(long jarg1, pjmedia_tone_desc jarg1_, short jarg2);
  public final static native short pjmedia_tone_desc_flags_get(long jarg1, pjmedia_tone_desc jarg1_);
  public final static native long new_pjmedia_tone_desc();
  public final static native void delete_pjmedia_tone_desc(long jarg1);
  public final static native long new_StringVector__SWIG_0();
  public final static native long new_StringVector__SWIG_1(long jarg1, StringVector jarg1_);
  public final static native long StringVector_capacity(long jarg1, StringVector jarg1_);
  public final static native void StringVector_reserve(long jarg1, StringVector jarg1_, long jarg2);
  public final static native boolean StringVector_isEmpty(long jarg1, StringVector jarg1_);
  public final static native void StringVector_clear(long jarg1, StringVector jarg1_);
  public final static native long new_StringVector__SWIG_2(int jarg1, String jarg2);
  public final static native int StringVector_doSize(long jarg1, StringVector jarg1_);
  public final static native void StringVector_doAdd__SWIG_0(long jarg1, StringVector jarg1_, String jarg2);
  public final static native void StringVector_doAdd__SWIG_1(long jarg1, StringVector jarg1_, int jarg2, String jarg3);
  public final static native String StringVector_doRemove(long jarg1, StringVector jarg1_, int jarg2);
  public final static native String StringVector_doGet(long jarg1, StringVector jarg1_, int jarg2);
  public final static native String StringVector_doSet(long jarg1, StringVector jarg1_, int jarg2, String jarg3);
  public final static native void StringVector_doRemoveRange(long jarg1, StringVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_StringVector(long jarg1);
  public final static native long new_IntVector__SWIG_0();
  public final static native long new_IntVector__SWIG_1(long jarg1, IntVector jarg1_);
  public final static native long IntVector_capacity(long jarg1, IntVector jarg1_);
  public final static native void IntVector_reserve(long jarg1, IntVector jarg1_, long jarg2);
  public final static native boolean IntVector_isEmpty(long jarg1, IntVector jarg1_);
  public final static native void IntVector_clear(long jarg1, IntVector jarg1_);
  public final static native long new_IntVector__SWIG_2(int jarg1, int jarg2);
  public final static native int IntVector_doSize(long jarg1, IntVector jarg1_);
  public final static native void IntVector_doAdd__SWIG_0(long jarg1, IntVector jarg1_, int jarg2);
  public final static native void IntVector_doAdd__SWIG_1(long jarg1, IntVector jarg1_, int jarg2, int jarg3);
  public final static native int IntVector_doRemove(long jarg1, IntVector jarg1_, int jarg2);
  public final static native int IntVector_doGet(long jarg1, IntVector jarg1_, int jarg2);
  public final static native int IntVector_doSet(long jarg1, IntVector jarg1_, int jarg2, int jarg3);
  public final static native void IntVector_doRemoveRange(long jarg1, IntVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_IntVector(long jarg1);
  public final static native long new_StringToStringMap__SWIG_0();
  public final static native long new_StringToStringMap__SWIG_1(long jarg1, StringToStringMap jarg1_);
  public final static native long StringToStringMap_Iterator_getNextUnchecked(long jarg1, StringToStringMap.Iterator jarg1_);
  public final static native boolean StringToStringMap_Iterator_isNot(long jarg1, StringToStringMap.Iterator jarg1_, long jarg2, StringToStringMap.Iterator jarg2_);
  public final static native String StringToStringMap_Iterator_getKey(long jarg1, StringToStringMap.Iterator jarg1_);
  public final static native String StringToStringMap_Iterator_getValue(long jarg1, StringToStringMap.Iterator jarg1_);
  public final static native void StringToStringMap_Iterator_setValue(long jarg1, StringToStringMap.Iterator jarg1_, String jarg2);
  public final static native void delete_StringToStringMap_Iterator(long jarg1);
  public final static native boolean StringToStringMap_isEmpty(long jarg1, StringToStringMap jarg1_);
  public final static native void StringToStringMap_clear(long jarg1, StringToStringMap jarg1_);
  public final static native long StringToStringMap_find(long jarg1, StringToStringMap jarg1_, String jarg2);
  public final static native long StringToStringMap_begin(long jarg1, StringToStringMap jarg1_);
  public final static native long StringToStringMap_end(long jarg1, StringToStringMap jarg1_);
  public final static native int StringToStringMap_sizeImpl(long jarg1, StringToStringMap jarg1_);
  public final static native boolean StringToStringMap_containsImpl(long jarg1, StringToStringMap jarg1_, String jarg2);
  public final static native void StringToStringMap_putUnchecked(long jarg1, StringToStringMap jarg1_, String jarg2, String jarg3);
  public final static native void StringToStringMap_removeUnchecked(long jarg1, StringToStringMap jarg1_, long jarg2, StringToStringMap.Iterator jarg2_);
  public final static native void delete_StringToStringMap(long jarg1);
  public final static native void Error_status_set(long jarg1, Error jarg1_, int jarg2);
  public final static native int Error_status_get(long jarg1, Error jarg1_);
  public final static native void Error_title_set(long jarg1, Error jarg1_, String jarg2);
  public final static native String Error_title_get(long jarg1, Error jarg1_);
  public final static native void Error_reason_set(long jarg1, Error jarg1_, String jarg2);
  public final static native String Error_reason_get(long jarg1, Error jarg1_);
  public final static native void Error_srcFile_set(long jarg1, Error jarg1_, String jarg2);
  public final static native String Error_srcFile_get(long jarg1, Error jarg1_);
  public final static native void Error_srcLine_set(long jarg1, Error jarg1_, int jarg2);
  public final static native int Error_srcLine_get(long jarg1, Error jarg1_);
  public final static native String Error_info__SWIG_0(long jarg1, Error jarg1_, boolean jarg2);
  public final static native String Error_info__SWIG_1(long jarg1, Error jarg1_);
  public final static native long new_Error__SWIG_0();
  public final static native long new_Error__SWIG_1(int jarg1, String jarg2, String jarg3, String jarg4, int jarg5);
  public final static native void delete_Error(long jarg1);
  public final static native void Version_major_set(long jarg1, Version jarg1_, int jarg2);
  public final static native int Version_major_get(long jarg1, Version jarg1_);
  public final static native void Version_minor_set(long jarg1, Version jarg1_, int jarg2);
  public final static native int Version_minor_get(long jarg1, Version jarg1_);
  public final static native void Version_rev_set(long jarg1, Version jarg1_, int jarg2);
  public final static native int Version_rev_get(long jarg1, Version jarg1_);
  public final static native void Version_suffix_set(long jarg1, Version jarg1_, String jarg2);
  public final static native String Version_suffix_get(long jarg1, Version jarg1_);
  public final static native void Version_full_set(long jarg1, Version jarg1_, String jarg2);
  public final static native String Version_full_get(long jarg1, Version jarg1_);
  public final static native void Version_numeric_set(long jarg1, Version jarg1_, long jarg2);
  public final static native long Version_numeric_get(long jarg1, Version jarg1_);
  public final static native long new_Version();
  public final static native void delete_Version(long jarg1);
  public final static native void TimeVal_sec_set(long jarg1, TimeVal jarg1_, int jarg2);
  public final static native int TimeVal_sec_get(long jarg1, TimeVal jarg1_);
  public final static native void TimeVal_msec_set(long jarg1, TimeVal jarg1_, int jarg2);
  public final static native int TimeVal_msec_get(long jarg1, TimeVal jarg1_);
  public final static native long new_TimeVal();
  public final static native void delete_TimeVal(long jarg1);
  public final static native void delete_PersistentObject(long jarg1);
  public final static native void PersistentObject_readObject(long jarg1, PersistentObject jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void PersistentObject_writeObject(long jarg1, PersistentObject jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void delete_PersistentDocument(long jarg1);
  public final static native void PersistentDocument_loadFile(long jarg1, PersistentDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native void PersistentDocument_loadString(long jarg1, PersistentDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native void PersistentDocument_saveFile(long jarg1, PersistentDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native String PersistentDocument_saveString(long jarg1, PersistentDocument jarg1_) throws java.lang.Exception;
  public final static native long PersistentDocument_getRootContainer(long jarg1, PersistentDocument jarg1_);
  public final static native boolean PersistentDocument_hasUnread(long jarg1, PersistentDocument jarg1_);
  public final static native String PersistentDocument_unreadName(long jarg1, PersistentDocument jarg1_) throws java.lang.Exception;
  public final static native int PersistentDocument_readInt__SWIG_0(long jarg1, PersistentDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native int PersistentDocument_readInt__SWIG_1(long jarg1, PersistentDocument jarg1_) throws java.lang.Exception;
  public final static native float PersistentDocument_readNumber__SWIG_0(long jarg1, PersistentDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native float PersistentDocument_readNumber__SWIG_1(long jarg1, PersistentDocument jarg1_) throws java.lang.Exception;
  public final static native boolean PersistentDocument_readBool__SWIG_0(long jarg1, PersistentDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native boolean PersistentDocument_readBool__SWIG_1(long jarg1, PersistentDocument jarg1_) throws java.lang.Exception;
  public final static native String PersistentDocument_readString__SWIG_0(long jarg1, PersistentDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native String PersistentDocument_readString__SWIG_1(long jarg1, PersistentDocument jarg1_) throws java.lang.Exception;
  public final static native long PersistentDocument_readStringVector__SWIG_0(long jarg1, PersistentDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native long PersistentDocument_readStringVector__SWIG_1(long jarg1, PersistentDocument jarg1_) throws java.lang.Exception;
  public final static native void PersistentDocument_readObject(long jarg1, PersistentDocument jarg1_, long jarg2, PersistentObject jarg2_) throws java.lang.Exception;
  public final static native long PersistentDocument_readContainer__SWIG_0(long jarg1, PersistentDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native long PersistentDocument_readContainer__SWIG_1(long jarg1, PersistentDocument jarg1_) throws java.lang.Exception;
  public final static native long PersistentDocument_readArray__SWIG_0(long jarg1, PersistentDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native long PersistentDocument_readArray__SWIG_1(long jarg1, PersistentDocument jarg1_) throws java.lang.Exception;
  public final static native void PersistentDocument_writeNumber(long jarg1, PersistentDocument jarg1_, String jarg2, float jarg3) throws java.lang.Exception;
  public final static native void PersistentDocument_writeInt(long jarg1, PersistentDocument jarg1_, String jarg2, int jarg3) throws java.lang.Exception;
  public final static native void PersistentDocument_writeBool(long jarg1, PersistentDocument jarg1_, String jarg2, boolean jarg3) throws java.lang.Exception;
  public final static native void PersistentDocument_writeString(long jarg1, PersistentDocument jarg1_, String jarg2, String jarg3) throws java.lang.Exception;
  public final static native void PersistentDocument_writeStringVector(long jarg1, PersistentDocument jarg1_, String jarg2, long jarg3, StringVector jarg3_) throws java.lang.Exception;
  public final static native void PersistentDocument_writeObject(long jarg1, PersistentDocument jarg1_, long jarg2, PersistentObject jarg2_) throws java.lang.Exception;
  public final static native long PersistentDocument_writeNewContainer(long jarg1, PersistentDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native long PersistentDocument_writeNewArray(long jarg1, PersistentDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native boolean ContainerNode_hasUnread(long jarg1, ContainerNode jarg1_);
  public final static native String ContainerNode_unreadName(long jarg1, ContainerNode jarg1_) throws java.lang.Exception;
  public final static native int ContainerNode_readInt__SWIG_0(long jarg1, ContainerNode jarg1_, String jarg2) throws java.lang.Exception;
  public final static native int ContainerNode_readInt__SWIG_1(long jarg1, ContainerNode jarg1_) throws java.lang.Exception;
  public final static native float ContainerNode_readNumber__SWIG_0(long jarg1, ContainerNode jarg1_, String jarg2) throws java.lang.Exception;
  public final static native float ContainerNode_readNumber__SWIG_1(long jarg1, ContainerNode jarg1_) throws java.lang.Exception;
  public final static native boolean ContainerNode_readBool__SWIG_0(long jarg1, ContainerNode jarg1_, String jarg2) throws java.lang.Exception;
  public final static native boolean ContainerNode_readBool__SWIG_1(long jarg1, ContainerNode jarg1_) throws java.lang.Exception;
  public final static native String ContainerNode_readString__SWIG_0(long jarg1, ContainerNode jarg1_, String jarg2) throws java.lang.Exception;
  public final static native String ContainerNode_readString__SWIG_1(long jarg1, ContainerNode jarg1_) throws java.lang.Exception;
  public final static native long ContainerNode_readStringVector__SWIG_0(long jarg1, ContainerNode jarg1_, String jarg2) throws java.lang.Exception;
  public final static native long ContainerNode_readStringVector__SWIG_1(long jarg1, ContainerNode jarg1_) throws java.lang.Exception;
  public final static native void ContainerNode_readObject(long jarg1, ContainerNode jarg1_, long jarg2, PersistentObject jarg2_) throws java.lang.Exception;
  public final static native long ContainerNode_readContainer__SWIG_0(long jarg1, ContainerNode jarg1_, String jarg2) throws java.lang.Exception;
  public final static native long ContainerNode_readContainer__SWIG_1(long jarg1, ContainerNode jarg1_) throws java.lang.Exception;
  public final static native long ContainerNode_readArray__SWIG_0(long jarg1, ContainerNode jarg1_, String jarg2) throws java.lang.Exception;
  public final static native long ContainerNode_readArray__SWIG_1(long jarg1, ContainerNode jarg1_) throws java.lang.Exception;
  public final static native void ContainerNode_writeNumber(long jarg1, ContainerNode jarg1_, String jarg2, float jarg3) throws java.lang.Exception;
  public final static native void ContainerNode_writeInt(long jarg1, ContainerNode jarg1_, String jarg2, int jarg3) throws java.lang.Exception;
  public final static native void ContainerNode_writeBool(long jarg1, ContainerNode jarg1_, String jarg2, boolean jarg3) throws java.lang.Exception;
  public final static native void ContainerNode_writeString(long jarg1, ContainerNode jarg1_, String jarg2, String jarg3) throws java.lang.Exception;
  public final static native void ContainerNode_writeStringVector(long jarg1, ContainerNode jarg1_, String jarg2, long jarg3, StringVector jarg3_) throws java.lang.Exception;
  public final static native void ContainerNode_writeObject(long jarg1, ContainerNode jarg1_, long jarg2, PersistentObject jarg2_) throws java.lang.Exception;
  public final static native long ContainerNode_writeNewContainer(long jarg1, ContainerNode jarg1_, String jarg2) throws java.lang.Exception;
  public final static native long ContainerNode_writeNewArray(long jarg1, ContainerNode jarg1_, String jarg2) throws java.lang.Exception;
  public final static native long new_ContainerNode();
  public final static native void delete_ContainerNode(long jarg1);
  public final static native void AuthCredInfo_scheme_set(long jarg1, AuthCredInfo jarg1_, String jarg2);
  public final static native String AuthCredInfo_scheme_get(long jarg1, AuthCredInfo jarg1_);
  public final static native void AuthCredInfo_realm_set(long jarg1, AuthCredInfo jarg1_, String jarg2);
  public final static native String AuthCredInfo_realm_get(long jarg1, AuthCredInfo jarg1_);
  public final static native void AuthCredInfo_username_set(long jarg1, AuthCredInfo jarg1_, String jarg2);
  public final static native String AuthCredInfo_username_get(long jarg1, AuthCredInfo jarg1_);
  public final static native void AuthCredInfo_dataType_set(long jarg1, AuthCredInfo jarg1_, int jarg2);
  public final static native int AuthCredInfo_dataType_get(long jarg1, AuthCredInfo jarg1_);
  public final static native void AuthCredInfo_data_set(long jarg1, AuthCredInfo jarg1_, String jarg2);
  public final static native String AuthCredInfo_data_get(long jarg1, AuthCredInfo jarg1_);
  public final static native void AuthCredInfo_akaK_set(long jarg1, AuthCredInfo jarg1_, String jarg2);
  public final static native String AuthCredInfo_akaK_get(long jarg1, AuthCredInfo jarg1_);
  public final static native void AuthCredInfo_akaOp_set(long jarg1, AuthCredInfo jarg1_, String jarg2);
  public final static native String AuthCredInfo_akaOp_get(long jarg1, AuthCredInfo jarg1_);
  public final static native void AuthCredInfo_akaAmf_set(long jarg1, AuthCredInfo jarg1_, String jarg2);
  public final static native String AuthCredInfo_akaAmf_get(long jarg1, AuthCredInfo jarg1_);
  public final static native long new_AuthCredInfo__SWIG_0();
  public final static native long new_AuthCredInfo__SWIG_1(String jarg1, String jarg2, String jarg3, int jarg4, String jarg5);
  public final static native void AuthCredInfo_readObject(long jarg1, AuthCredInfo jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void AuthCredInfo_writeObject(long jarg1, AuthCredInfo jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void delete_AuthCredInfo(long jarg1);
  public final static native void TlsConfig_CaListFile_set(long jarg1, TlsConfig jarg1_, String jarg2);
  public final static native String TlsConfig_CaListFile_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_certFile_set(long jarg1, TlsConfig jarg1_, String jarg2);
  public final static native String TlsConfig_certFile_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_privKeyFile_set(long jarg1, TlsConfig jarg1_, String jarg2);
  public final static native String TlsConfig_privKeyFile_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_password_set(long jarg1, TlsConfig jarg1_, String jarg2);
  public final static native String TlsConfig_password_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_CaBuf_set(long jarg1, TlsConfig jarg1_, String jarg2);
  public final static native String TlsConfig_CaBuf_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_certBuf_set(long jarg1, TlsConfig jarg1_, String jarg2);
  public final static native String TlsConfig_certBuf_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_privKeyBuf_set(long jarg1, TlsConfig jarg1_, String jarg2);
  public final static native String TlsConfig_privKeyBuf_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_method_set(long jarg1, TlsConfig jarg1_, int jarg2);
  public final static native int TlsConfig_method_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_proto_set(long jarg1, TlsConfig jarg1_, long jarg2);
  public final static native long TlsConfig_proto_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_ciphers_set(long jarg1, TlsConfig jarg1_, long jarg2, IntVector jarg2_);
  public final static native long TlsConfig_ciphers_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_verifyServer_set(long jarg1, TlsConfig jarg1_, boolean jarg2);
  public final static native boolean TlsConfig_verifyServer_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_verifyClient_set(long jarg1, TlsConfig jarg1_, boolean jarg2);
  public final static native boolean TlsConfig_verifyClient_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_requireClientCert_set(long jarg1, TlsConfig jarg1_, boolean jarg2);
  public final static native boolean TlsConfig_requireClientCert_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_msecTimeout_set(long jarg1, TlsConfig jarg1_, long jarg2);
  public final static native long TlsConfig_msecTimeout_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_qosType_set(long jarg1, TlsConfig jarg1_, int jarg2);
  public final static native int TlsConfig_qosType_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_qosParams_set(long jarg1, TlsConfig jarg1_, long jarg2, pj_qos_params jarg2_);
  public final static native long TlsConfig_qosParams_get(long jarg1, TlsConfig jarg1_);
  public final static native void TlsConfig_qosIgnoreError_set(long jarg1, TlsConfig jarg1_, boolean jarg2);
  public final static native boolean TlsConfig_qosIgnoreError_get(long jarg1, TlsConfig jarg1_);
  public final static native long new_TlsConfig();
  public final static native void TlsConfig_readObject(long jarg1, TlsConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void TlsConfig_writeObject(long jarg1, TlsConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void delete_TlsConfig(long jarg1);
  public final static native void TransportConfig_port_set(long jarg1, TransportConfig jarg1_, long jarg2);
  public final static native long TransportConfig_port_get(long jarg1, TransportConfig jarg1_);
  public final static native void TransportConfig_portRange_set(long jarg1, TransportConfig jarg1_, long jarg2);
  public final static native long TransportConfig_portRange_get(long jarg1, TransportConfig jarg1_);
  public final static native void TransportConfig_randomizePort_set(long jarg1, TransportConfig jarg1_, boolean jarg2);
  public final static native boolean TransportConfig_randomizePort_get(long jarg1, TransportConfig jarg1_);
  public final static native void TransportConfig_publicAddress_set(long jarg1, TransportConfig jarg1_, String jarg2);
  public final static native String TransportConfig_publicAddress_get(long jarg1, TransportConfig jarg1_);
  public final static native void TransportConfig_boundAddress_set(long jarg1, TransportConfig jarg1_, String jarg2);
  public final static native String TransportConfig_boundAddress_get(long jarg1, TransportConfig jarg1_);
  public final static native void TransportConfig_tlsConfig_set(long jarg1, TransportConfig jarg1_, long jarg2, TlsConfig jarg2_);
  public final static native long TransportConfig_tlsConfig_get(long jarg1, TransportConfig jarg1_);
  public final static native void TransportConfig_qosType_set(long jarg1, TransportConfig jarg1_, int jarg2);
  public final static native int TransportConfig_qosType_get(long jarg1, TransportConfig jarg1_);
  public final static native void TransportConfig_qosParams_set(long jarg1, TransportConfig jarg1_, long jarg2, pj_qos_params jarg2_);
  public final static native long TransportConfig_qosParams_get(long jarg1, TransportConfig jarg1_);
  public final static native long new_TransportConfig();
  public final static native void TransportConfig_readObject(long jarg1, TransportConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void TransportConfig_writeObject(long jarg1, TransportConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void delete_TransportConfig(long jarg1);
  public final static native void TransportInfo_id_set(long jarg1, TransportInfo jarg1_, int jarg2);
  public final static native int TransportInfo_id_get(long jarg1, TransportInfo jarg1_);
  public final static native void TransportInfo_type_set(long jarg1, TransportInfo jarg1_, int jarg2);
  public final static native int TransportInfo_type_get(long jarg1, TransportInfo jarg1_);
  public final static native void TransportInfo_typeName_set(long jarg1, TransportInfo jarg1_, String jarg2);
  public final static native String TransportInfo_typeName_get(long jarg1, TransportInfo jarg1_);
  public final static native void TransportInfo_info_set(long jarg1, TransportInfo jarg1_, String jarg2);
  public final static native String TransportInfo_info_get(long jarg1, TransportInfo jarg1_);
  public final static native void TransportInfo_flags_set(long jarg1, TransportInfo jarg1_, long jarg2);
  public final static native long TransportInfo_flags_get(long jarg1, TransportInfo jarg1_);
  public final static native void TransportInfo_localAddress_set(long jarg1, TransportInfo jarg1_, String jarg2);
  public final static native String TransportInfo_localAddress_get(long jarg1, TransportInfo jarg1_);
  public final static native void TransportInfo_localName_set(long jarg1, TransportInfo jarg1_, String jarg2);
  public final static native String TransportInfo_localName_get(long jarg1, TransportInfo jarg1_);
  public final static native void TransportInfo_usageCount_set(long jarg1, TransportInfo jarg1_, long jarg2);
  public final static native long TransportInfo_usageCount_get(long jarg1, TransportInfo jarg1_);
  public final static native long new_TransportInfo();
  public final static native void delete_TransportInfo(long jarg1);
  public final static native void SipRxData_info_set(long jarg1, SipRxData jarg1_, String jarg2);
  public final static native String SipRxData_info_get(long jarg1, SipRxData jarg1_);
  public final static native void SipRxData_wholeMsg_set(long jarg1, SipRxData jarg1_, String jarg2);
  public final static native String SipRxData_wholeMsg_get(long jarg1, SipRxData jarg1_);
  public final static native void SipRxData_srcAddress_set(long jarg1, SipRxData jarg1_, String jarg2);
  public final static native String SipRxData_srcAddress_get(long jarg1, SipRxData jarg1_);
  public final static native void SipRxData_pjRxData_set(long jarg1, SipRxData jarg1_, long jarg2);
  public final static native long SipRxData_pjRxData_get(long jarg1, SipRxData jarg1_);
  public final static native long new_SipRxData();
  public final static native void delete_SipRxData(long jarg1);
  public final static native void SipTxData_info_set(long jarg1, SipTxData jarg1_, String jarg2);
  public final static native String SipTxData_info_get(long jarg1, SipTxData jarg1_);
  public final static native void SipTxData_wholeMsg_set(long jarg1, SipTxData jarg1_, String jarg2);
  public final static native String SipTxData_wholeMsg_get(long jarg1, SipTxData jarg1_);
  public final static native void SipTxData_dstAddress_set(long jarg1, SipTxData jarg1_, String jarg2);
  public final static native String SipTxData_dstAddress_get(long jarg1, SipTxData jarg1_);
  public final static native void SipTxData_pjTxData_set(long jarg1, SipTxData jarg1_, long jarg2);
  public final static native long SipTxData_pjTxData_get(long jarg1, SipTxData jarg1_);
  public final static native long new_SipTxData();
  public final static native void delete_SipTxData(long jarg1);
  public final static native void SipTransaction_role_set(long jarg1, SipTransaction jarg1_, int jarg2);
  public final static native int SipTransaction_role_get(long jarg1, SipTransaction jarg1_);
  public final static native void SipTransaction_method_set(long jarg1, SipTransaction jarg1_, String jarg2);
  public final static native String SipTransaction_method_get(long jarg1, SipTransaction jarg1_);
  public final static native void SipTransaction_statusCode_set(long jarg1, SipTransaction jarg1_, int jarg2);
  public final static native int SipTransaction_statusCode_get(long jarg1, SipTransaction jarg1_);
  public final static native void SipTransaction_statusText_set(long jarg1, SipTransaction jarg1_, String jarg2);
  public final static native String SipTransaction_statusText_get(long jarg1, SipTransaction jarg1_);
  public final static native void SipTransaction_state_set(long jarg1, SipTransaction jarg1_, int jarg2);
  public final static native int SipTransaction_state_get(long jarg1, SipTransaction jarg1_);
  public final static native void SipTransaction_lastTx_set(long jarg1, SipTransaction jarg1_, long jarg2, SipTxData jarg2_);
  public final static native long SipTransaction_lastTx_get(long jarg1, SipTransaction jarg1_);
  public final static native void SipTransaction_pjTransaction_set(long jarg1, SipTransaction jarg1_, long jarg2);
  public final static native long SipTransaction_pjTransaction_get(long jarg1, SipTransaction jarg1_);
  public final static native long new_SipTransaction();
  public final static native void delete_SipTransaction(long jarg1);
  public final static native void TimerEvent_entry_set(long jarg1, TimerEvent jarg1_, long jarg2);
  public final static native long TimerEvent_entry_get(long jarg1, TimerEvent jarg1_);
  public final static native long new_TimerEvent();
  public final static native void delete_TimerEvent(long jarg1);
  public final static native void TsxStateEventSrc_rdata_set(long jarg1, TsxStateEventSrc jarg1_, long jarg2, SipRxData jarg2_);
  public final static native long TsxStateEventSrc_rdata_get(long jarg1, TsxStateEventSrc jarg1_);
  public final static native void TsxStateEventSrc_tdata_set(long jarg1, TsxStateEventSrc jarg1_, long jarg2, SipTxData jarg2_);
  public final static native long TsxStateEventSrc_tdata_get(long jarg1, TsxStateEventSrc jarg1_);
  public final static native void TsxStateEventSrc_timer_set(long jarg1, TsxStateEventSrc jarg1_, long jarg2);
  public final static native long TsxStateEventSrc_timer_get(long jarg1, TsxStateEventSrc jarg1_);
  public final static native void TsxStateEventSrc_status_set(long jarg1, TsxStateEventSrc jarg1_, int jarg2);
  public final static native int TsxStateEventSrc_status_get(long jarg1, TsxStateEventSrc jarg1_);
  public final static native void TsxStateEventSrc_data_set(long jarg1, TsxStateEventSrc jarg1_, long jarg2);
  public final static native long TsxStateEventSrc_data_get(long jarg1, TsxStateEventSrc jarg1_);
  public final static native long new_TsxStateEventSrc();
  public final static native void delete_TsxStateEventSrc(long jarg1);
  public final static native void TsxStateEvent_src_set(long jarg1, TsxStateEvent jarg1_, long jarg2, TsxStateEventSrc jarg2_);
  public final static native long TsxStateEvent_src_get(long jarg1, TsxStateEvent jarg1_);
  public final static native void TsxStateEvent_tsx_set(long jarg1, TsxStateEvent jarg1_, long jarg2, SipTransaction jarg2_);
  public final static native long TsxStateEvent_tsx_get(long jarg1, TsxStateEvent jarg1_);
  public final static native void TsxStateEvent_prevState_set(long jarg1, TsxStateEvent jarg1_, int jarg2);
  public final static native int TsxStateEvent_prevState_get(long jarg1, TsxStateEvent jarg1_);
  public final static native void TsxStateEvent_type_set(long jarg1, TsxStateEvent jarg1_, int jarg2);
  public final static native int TsxStateEvent_type_get(long jarg1, TsxStateEvent jarg1_);
  public final static native long new_TsxStateEvent();
  public final static native void delete_TsxStateEvent(long jarg1);
  public final static native void TxMsgEvent_tdata_set(long jarg1, TxMsgEvent jarg1_, long jarg2, SipTxData jarg2_);
  public final static native long TxMsgEvent_tdata_get(long jarg1, TxMsgEvent jarg1_);
  public final static native long new_TxMsgEvent();
  public final static native void delete_TxMsgEvent(long jarg1);
  public final static native void TxErrorEvent_tdata_set(long jarg1, TxErrorEvent jarg1_, long jarg2, SipTxData jarg2_);
  public final static native long TxErrorEvent_tdata_get(long jarg1, TxErrorEvent jarg1_);
  public final static native void TxErrorEvent_tsx_set(long jarg1, TxErrorEvent jarg1_, long jarg2, SipTransaction jarg2_);
  public final static native long TxErrorEvent_tsx_get(long jarg1, TxErrorEvent jarg1_);
  public final static native long new_TxErrorEvent();
  public final static native void delete_TxErrorEvent(long jarg1);
  public final static native void RxMsgEvent_rdata_set(long jarg1, RxMsgEvent jarg1_, long jarg2, SipRxData jarg2_);
  public final static native long RxMsgEvent_rdata_get(long jarg1, RxMsgEvent jarg1_);
  public final static native long new_RxMsgEvent();
  public final static native void delete_RxMsgEvent(long jarg1);
  public final static native void UserEvent_user1_set(long jarg1, UserEvent jarg1_, long jarg2);
  public final static native long UserEvent_user1_get(long jarg1, UserEvent jarg1_);
  public final static native void UserEvent_user2_set(long jarg1, UserEvent jarg1_, long jarg2);
  public final static native long UserEvent_user2_get(long jarg1, UserEvent jarg1_);
  public final static native void UserEvent_user3_set(long jarg1, UserEvent jarg1_, long jarg2);
  public final static native long UserEvent_user3_get(long jarg1, UserEvent jarg1_);
  public final static native void UserEvent_user4_set(long jarg1, UserEvent jarg1_, long jarg2);
  public final static native long UserEvent_user4_get(long jarg1, UserEvent jarg1_);
  public final static native long new_UserEvent();
  public final static native void delete_UserEvent(long jarg1);
  public final static native void SipEventBody_timer_set(long jarg1, SipEventBody jarg1_, long jarg2, TimerEvent jarg2_);
  public final static native long SipEventBody_timer_get(long jarg1, SipEventBody jarg1_);
  public final static native void SipEventBody_tsxState_set(long jarg1, SipEventBody jarg1_, long jarg2, TsxStateEvent jarg2_);
  public final static native long SipEventBody_tsxState_get(long jarg1, SipEventBody jarg1_);
  public final static native void SipEventBody_txMsg_set(long jarg1, SipEventBody jarg1_, long jarg2, TxMsgEvent jarg2_);
  public final static native long SipEventBody_txMsg_get(long jarg1, SipEventBody jarg1_);
  public final static native void SipEventBody_txError_set(long jarg1, SipEventBody jarg1_, long jarg2, TxErrorEvent jarg2_);
  public final static native long SipEventBody_txError_get(long jarg1, SipEventBody jarg1_);
  public final static native void SipEventBody_rxMsg_set(long jarg1, SipEventBody jarg1_, long jarg2, RxMsgEvent jarg2_);
  public final static native long SipEventBody_rxMsg_get(long jarg1, SipEventBody jarg1_);
  public final static native void SipEventBody_user_set(long jarg1, SipEventBody jarg1_, long jarg2, UserEvent jarg2_);
  public final static native long SipEventBody_user_get(long jarg1, SipEventBody jarg1_);
  public final static native long new_SipEventBody();
  public final static native void delete_SipEventBody(long jarg1);
  public final static native void SipEvent_type_set(long jarg1, SipEvent jarg1_, int jarg2);
  public final static native int SipEvent_type_get(long jarg1, SipEvent jarg1_);
  public final static native void SipEvent_body_set(long jarg1, SipEvent jarg1_, long jarg2, SipEventBody jarg2_);
  public final static native long SipEvent_body_get(long jarg1, SipEvent jarg1_);
  public final static native void SipEvent_pjEvent_set(long jarg1, SipEvent jarg1_, long jarg2);
  public final static native long SipEvent_pjEvent_get(long jarg1, SipEvent jarg1_);
  public final static native long new_SipEvent();
  public final static native void delete_SipEvent(long jarg1);
  public final static native void SipMediaType_type_set(long jarg1, SipMediaType jarg1_, String jarg2);
  public final static native String SipMediaType_type_get(long jarg1, SipMediaType jarg1_);
  public final static native void SipMediaType_subType_set(long jarg1, SipMediaType jarg1_, String jarg2);
  public final static native String SipMediaType_subType_get(long jarg1, SipMediaType jarg1_);
  public final static native long new_SipMediaType();
  public final static native void delete_SipMediaType(long jarg1);
  public final static native void SipHeader_hName_set(long jarg1, SipHeader jarg1_, String jarg2);
  public final static native String SipHeader_hName_get(long jarg1, SipHeader jarg1_);
  public final static native void SipHeader_hValue_set(long jarg1, SipHeader jarg1_, String jarg2);
  public final static native String SipHeader_hValue_get(long jarg1, SipHeader jarg1_);
  public final static native long new_SipHeader();
  public final static native void delete_SipHeader(long jarg1);
  public final static native void SipMultipartPart_headers_set(long jarg1, SipMultipartPart jarg1_, long jarg2, SipHeaderVector jarg2_);
  public final static native long SipMultipartPart_headers_get(long jarg1, SipMultipartPart jarg1_);
  public final static native void SipMultipartPart_contentType_set(long jarg1, SipMultipartPart jarg1_, long jarg2, SipMediaType jarg2_);
  public final static native long SipMultipartPart_contentType_get(long jarg1, SipMultipartPart jarg1_);
  public final static native void SipMultipartPart_body_set(long jarg1, SipMultipartPart jarg1_, String jarg2);
  public final static native String SipMultipartPart_body_get(long jarg1, SipMultipartPart jarg1_);
  public final static native long new_SipMultipartPart();
  public final static native void delete_SipMultipartPart(long jarg1);
  public final static native void SipTxOption_targetUri_set(long jarg1, SipTxOption jarg1_, String jarg2);
  public final static native String SipTxOption_targetUri_get(long jarg1, SipTxOption jarg1_);
  public final static native void SipTxOption_headers_set(long jarg1, SipTxOption jarg1_, long jarg2, SipHeaderVector jarg2_);
  public final static native long SipTxOption_headers_get(long jarg1, SipTxOption jarg1_);
  public final static native void SipTxOption_contentType_set(long jarg1, SipTxOption jarg1_, String jarg2);
  public final static native String SipTxOption_contentType_get(long jarg1, SipTxOption jarg1_);
  public final static native void SipTxOption_msgBody_set(long jarg1, SipTxOption jarg1_, String jarg2);
  public final static native String SipTxOption_msgBody_get(long jarg1, SipTxOption jarg1_);
  public final static native void SipTxOption_multipartContentType_set(long jarg1, SipTxOption jarg1_, long jarg2, SipMediaType jarg2_);
  public final static native long SipTxOption_multipartContentType_get(long jarg1, SipTxOption jarg1_);
  public final static native void SipTxOption_multipartParts_set(long jarg1, SipTxOption jarg1_, long jarg2, SipMultipartPartVector jarg2_);
  public final static native long SipTxOption_multipartParts_get(long jarg1, SipTxOption jarg1_);
  public final static native boolean SipTxOption_isEmpty(long jarg1, SipTxOption jarg1_);
  public final static native long new_SipTxOption();
  public final static native void delete_SipTxOption(long jarg1);
  public final static native void SendInstantMessageParam_contentType_set(long jarg1, SendInstantMessageParam jarg1_, String jarg2);
  public final static native String SendInstantMessageParam_contentType_get(long jarg1, SendInstantMessageParam jarg1_);
  public final static native void SendInstantMessageParam_content_set(long jarg1, SendInstantMessageParam jarg1_, String jarg2);
  public final static native String SendInstantMessageParam_content_get(long jarg1, SendInstantMessageParam jarg1_);
  public final static native void SendInstantMessageParam_txOption_set(long jarg1, SendInstantMessageParam jarg1_, long jarg2, SipTxOption jarg2_);
  public final static native long SendInstantMessageParam_txOption_get(long jarg1, SendInstantMessageParam jarg1_);
  public final static native void SendInstantMessageParam_userData_set(long jarg1, SendInstantMessageParam jarg1_, long jarg2);
  public final static native long SendInstantMessageParam_userData_get(long jarg1, SendInstantMessageParam jarg1_);
  public final static native long new_SendInstantMessageParam();
  public final static native void delete_SendInstantMessageParam(long jarg1);
  public final static native void SendTypingIndicationParam_isTyping_set(long jarg1, SendTypingIndicationParam jarg1_, boolean jarg2);
  public final static native boolean SendTypingIndicationParam_isTyping_get(long jarg1, SendTypingIndicationParam jarg1_);
  public final static native void SendTypingIndicationParam_txOption_set(long jarg1, SendTypingIndicationParam jarg1_, long jarg2, SipTxOption jarg2_);
  public final static native long SendTypingIndicationParam_txOption_get(long jarg1, SendTypingIndicationParam jarg1_);
  public final static native long new_SendTypingIndicationParam();
  public final static native void delete_SendTypingIndicationParam(long jarg1);
  public final static native long new_SipHeaderVector__SWIG_0();
  public final static native long new_SipHeaderVector__SWIG_1(long jarg1, SipHeaderVector jarg1_);
  public final static native long SipHeaderVector_capacity(long jarg1, SipHeaderVector jarg1_);
  public final static native void SipHeaderVector_reserve(long jarg1, SipHeaderVector jarg1_, long jarg2);
  public final static native boolean SipHeaderVector_isEmpty(long jarg1, SipHeaderVector jarg1_);
  public final static native void SipHeaderVector_clear(long jarg1, SipHeaderVector jarg1_);
  public final static native long new_SipHeaderVector__SWIG_2(int jarg1, long jarg2, SipHeader jarg2_);
  public final static native int SipHeaderVector_doSize(long jarg1, SipHeaderVector jarg1_);
  public final static native void SipHeaderVector_doAdd__SWIG_0(long jarg1, SipHeaderVector jarg1_, long jarg2, SipHeader jarg2_);
  public final static native void SipHeaderVector_doAdd__SWIG_1(long jarg1, SipHeaderVector jarg1_, int jarg2, long jarg3, SipHeader jarg3_);
  public final static native long SipHeaderVector_doRemove(long jarg1, SipHeaderVector jarg1_, int jarg2);
  public final static native long SipHeaderVector_doGet(long jarg1, SipHeaderVector jarg1_, int jarg2);
  public final static native long SipHeaderVector_doSet(long jarg1, SipHeaderVector jarg1_, int jarg2, long jarg3, SipHeader jarg3_);
  public final static native void SipHeaderVector_doRemoveRange(long jarg1, SipHeaderVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_SipHeaderVector(long jarg1);
  public final static native long new_AuthCredInfoVector__SWIG_0();
  public final static native long new_AuthCredInfoVector__SWIG_1(long jarg1, AuthCredInfoVector jarg1_);
  public final static native long AuthCredInfoVector_capacity(long jarg1, AuthCredInfoVector jarg1_);
  public final static native void AuthCredInfoVector_reserve(long jarg1, AuthCredInfoVector jarg1_, long jarg2);
  public final static native boolean AuthCredInfoVector_isEmpty(long jarg1, AuthCredInfoVector jarg1_);
  public final static native void AuthCredInfoVector_clear(long jarg1, AuthCredInfoVector jarg1_);
  public final static native long new_AuthCredInfoVector__SWIG_2(int jarg1, long jarg2, AuthCredInfo jarg2_);
  public final static native int AuthCredInfoVector_doSize(long jarg1, AuthCredInfoVector jarg1_);
  public final static native void AuthCredInfoVector_doAdd__SWIG_0(long jarg1, AuthCredInfoVector jarg1_, long jarg2, AuthCredInfo jarg2_);
  public final static native void AuthCredInfoVector_doAdd__SWIG_1(long jarg1, AuthCredInfoVector jarg1_, int jarg2, long jarg3, AuthCredInfo jarg3_);
  public final static native long AuthCredInfoVector_doRemove(long jarg1, AuthCredInfoVector jarg1_, int jarg2);
  public final static native long AuthCredInfoVector_doGet(long jarg1, AuthCredInfoVector jarg1_, int jarg2);
  public final static native long AuthCredInfoVector_doSet(long jarg1, AuthCredInfoVector jarg1_, int jarg2, long jarg3, AuthCredInfo jarg3_);
  public final static native void AuthCredInfoVector_doRemoveRange(long jarg1, AuthCredInfoVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_AuthCredInfoVector(long jarg1);
  public final static native long new_SrtpCryptoVector__SWIG_0();
  public final static native long new_SrtpCryptoVector__SWIG_1(long jarg1, SrtpCryptoVector jarg1_);
  public final static native long SrtpCryptoVector_capacity(long jarg1, SrtpCryptoVector jarg1_);
  public final static native void SrtpCryptoVector_reserve(long jarg1, SrtpCryptoVector jarg1_, long jarg2);
  public final static native boolean SrtpCryptoVector_isEmpty(long jarg1, SrtpCryptoVector jarg1_);
  public final static native void SrtpCryptoVector_clear(long jarg1, SrtpCryptoVector jarg1_);
  public final static native long new_SrtpCryptoVector__SWIG_2(int jarg1, long jarg2, SrtpCrypto jarg2_);
  public final static native int SrtpCryptoVector_doSize(long jarg1, SrtpCryptoVector jarg1_);
  public final static native void SrtpCryptoVector_doAdd__SWIG_0(long jarg1, SrtpCryptoVector jarg1_, long jarg2, SrtpCrypto jarg2_);
  public final static native void SrtpCryptoVector_doAdd__SWIG_1(long jarg1, SrtpCryptoVector jarg1_, int jarg2, long jarg3, SrtpCrypto jarg3_);
  public final static native long SrtpCryptoVector_doRemove(long jarg1, SrtpCryptoVector jarg1_, int jarg2);
  public final static native long SrtpCryptoVector_doGet(long jarg1, SrtpCryptoVector jarg1_, int jarg2);
  public final static native long SrtpCryptoVector_doSet(long jarg1, SrtpCryptoVector jarg1_, int jarg2, long jarg3, SrtpCrypto jarg3_);
  public final static native void SrtpCryptoVector_doRemoveRange(long jarg1, SrtpCryptoVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_SrtpCryptoVector(long jarg1);
  public final static native long new_SipMultipartPartVector__SWIG_0();
  public final static native long new_SipMultipartPartVector__SWIG_1(long jarg1, SipMultipartPartVector jarg1_);
  public final static native long SipMultipartPartVector_capacity(long jarg1, SipMultipartPartVector jarg1_);
  public final static native void SipMultipartPartVector_reserve(long jarg1, SipMultipartPartVector jarg1_, long jarg2);
  public final static native boolean SipMultipartPartVector_isEmpty(long jarg1, SipMultipartPartVector jarg1_);
  public final static native void SipMultipartPartVector_clear(long jarg1, SipMultipartPartVector jarg1_);
  public final static native long new_SipMultipartPartVector__SWIG_2(int jarg1, long jarg2, SipMultipartPart jarg2_);
  public final static native int SipMultipartPartVector_doSize(long jarg1, SipMultipartPartVector jarg1_);
  public final static native void SipMultipartPartVector_doAdd__SWIG_0(long jarg1, SipMultipartPartVector jarg1_, long jarg2, SipMultipartPart jarg2_);
  public final static native void SipMultipartPartVector_doAdd__SWIG_1(long jarg1, SipMultipartPartVector jarg1_, int jarg2, long jarg3, SipMultipartPart jarg3_);
  public final static native long SipMultipartPartVector_doRemove(long jarg1, SipMultipartPartVector jarg1_, int jarg2);
  public final static native long SipMultipartPartVector_doGet(long jarg1, SipMultipartPartVector jarg1_, int jarg2);
  public final static native long SipMultipartPartVector_doSet(long jarg1, SipMultipartPartVector jarg1_, int jarg2, long jarg3, SipMultipartPart jarg3_);
  public final static native void SipMultipartPartVector_doRemoveRange(long jarg1, SipMultipartPartVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_SipMultipartPartVector(long jarg1);
  public final static native long new_BuddyVector__SWIG_0();
  public final static native long new_BuddyVector__SWIG_1(long jarg1, BuddyVector jarg1_);
  public final static native long BuddyVector_capacity(long jarg1, BuddyVector jarg1_);
  public final static native void BuddyVector_reserve(long jarg1, BuddyVector jarg1_, long jarg2);
  public final static native boolean BuddyVector_isEmpty(long jarg1, BuddyVector jarg1_);
  public final static native void BuddyVector_clear(long jarg1, BuddyVector jarg1_);
  public final static native long new_BuddyVector__SWIG_2(int jarg1, long jarg2, Buddy jarg2_);
  public final static native int BuddyVector_doSize(long jarg1, BuddyVector jarg1_);
  public final static native void BuddyVector_doAdd__SWIG_0(long jarg1, BuddyVector jarg1_, long jarg2, Buddy jarg2_);
  public final static native void BuddyVector_doAdd__SWIG_1(long jarg1, BuddyVector jarg1_, int jarg2, long jarg3, Buddy jarg3_);
  public final static native long BuddyVector_doRemove(long jarg1, BuddyVector jarg1_, int jarg2);
  public final static native long BuddyVector_doGet(long jarg1, BuddyVector jarg1_, int jarg2);
  public final static native long BuddyVector_doSet(long jarg1, BuddyVector jarg1_, int jarg2, long jarg3, Buddy jarg3_);
  public final static native void BuddyVector_doRemoveRange(long jarg1, BuddyVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_BuddyVector(long jarg1);
  public final static native long new_BuddyVector2__SWIG_0();
  public final static native long new_BuddyVector2__SWIG_1(long jarg1, BuddyVector2 jarg1_);
  public final static native long BuddyVector2_capacity(long jarg1, BuddyVector2 jarg1_);
  public final static native void BuddyVector2_reserve(long jarg1, BuddyVector2 jarg1_, long jarg2);
  public final static native boolean BuddyVector2_isEmpty(long jarg1, BuddyVector2 jarg1_);
  public final static native void BuddyVector2_clear(long jarg1, BuddyVector2 jarg1_);
  public final static native long new_BuddyVector2__SWIG_2(int jarg1, long jarg2, Buddy jarg2_);
  public final static native int BuddyVector2_doSize(long jarg1, BuddyVector2 jarg1_);
  public final static native void BuddyVector2_doAdd__SWIG_0(long jarg1, BuddyVector2 jarg1_, long jarg2, Buddy jarg2_);
  public final static native void BuddyVector2_doAdd__SWIG_1(long jarg1, BuddyVector2 jarg1_, int jarg2, long jarg3, Buddy jarg3_);
  public final static native long BuddyVector2_doRemove(long jarg1, BuddyVector2 jarg1_, int jarg2);
  public final static native long BuddyVector2_doGet(long jarg1, BuddyVector2 jarg1_, int jarg2);
  public final static native long BuddyVector2_doSet(long jarg1, BuddyVector2 jarg1_, int jarg2, long jarg3, Buddy jarg3_);
  public final static native void BuddyVector2_doRemoveRange(long jarg1, BuddyVector2 jarg1_, int jarg2, int jarg3);
  public final static native void delete_BuddyVector2(long jarg1);
  public final static native long new_AudioMediaVector__SWIG_0();
  public final static native long new_AudioMediaVector__SWIG_1(long jarg1, AudioMediaVector jarg1_);
  public final static native long AudioMediaVector_capacity(long jarg1, AudioMediaVector jarg1_);
  public final static native void AudioMediaVector_reserve(long jarg1, AudioMediaVector jarg1_, long jarg2);
  public final static native boolean AudioMediaVector_isEmpty(long jarg1, AudioMediaVector jarg1_);
  public final static native void AudioMediaVector_clear(long jarg1, AudioMediaVector jarg1_);
  public final static native long new_AudioMediaVector__SWIG_2(int jarg1, long jarg2, AudioMedia jarg2_);
  public final static native int AudioMediaVector_doSize(long jarg1, AudioMediaVector jarg1_);
  public final static native void AudioMediaVector_doAdd__SWIG_0(long jarg1, AudioMediaVector jarg1_, long jarg2, AudioMedia jarg2_);
  public final static native void AudioMediaVector_doAdd__SWIG_1(long jarg1, AudioMediaVector jarg1_, int jarg2, long jarg3, AudioMedia jarg3_);
  public final static native long AudioMediaVector_doRemove(long jarg1, AudioMediaVector jarg1_, int jarg2);
  public final static native long AudioMediaVector_doGet(long jarg1, AudioMediaVector jarg1_, int jarg2);
  public final static native long AudioMediaVector_doSet(long jarg1, AudioMediaVector jarg1_, int jarg2, long jarg3, AudioMedia jarg3_);
  public final static native void AudioMediaVector_doRemoveRange(long jarg1, AudioMediaVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_AudioMediaVector(long jarg1);
  public final static native long new_AudioMediaVector2__SWIG_0();
  public final static native long new_AudioMediaVector2__SWIG_1(long jarg1, AudioMediaVector2 jarg1_);
  public final static native long AudioMediaVector2_capacity(long jarg1, AudioMediaVector2 jarg1_);
  public final static native void AudioMediaVector2_reserve(long jarg1, AudioMediaVector2 jarg1_, long jarg2);
  public final static native boolean AudioMediaVector2_isEmpty(long jarg1, AudioMediaVector2 jarg1_);
  public final static native void AudioMediaVector2_clear(long jarg1, AudioMediaVector2 jarg1_);
  public final static native long new_AudioMediaVector2__SWIG_2(int jarg1, long jarg2, AudioMedia jarg2_);
  public final static native int AudioMediaVector2_doSize(long jarg1, AudioMediaVector2 jarg1_);
  public final static native void AudioMediaVector2_doAdd__SWIG_0(long jarg1, AudioMediaVector2 jarg1_, long jarg2, AudioMedia jarg2_);
  public final static native void AudioMediaVector2_doAdd__SWIG_1(long jarg1, AudioMediaVector2 jarg1_, int jarg2, long jarg3, AudioMedia jarg3_);
  public final static native long AudioMediaVector2_doRemove(long jarg1, AudioMediaVector2 jarg1_, int jarg2);
  public final static native long AudioMediaVector2_doGet(long jarg1, AudioMediaVector2 jarg1_, int jarg2);
  public final static native long AudioMediaVector2_doSet(long jarg1, AudioMediaVector2 jarg1_, int jarg2, long jarg3, AudioMedia jarg3_);
  public final static native void AudioMediaVector2_doRemoveRange(long jarg1, AudioMediaVector2 jarg1_, int jarg2, int jarg3);
  public final static native void delete_AudioMediaVector2(long jarg1);
  public final static native long new_VideoMediaVector__SWIG_0();
  public final static native long new_VideoMediaVector__SWIG_1(long jarg1, VideoMediaVector jarg1_);
  public final static native long VideoMediaVector_capacity(long jarg1, VideoMediaVector jarg1_);
  public final static native void VideoMediaVector_reserve(long jarg1, VideoMediaVector jarg1_, long jarg2);
  public final static native boolean VideoMediaVector_isEmpty(long jarg1, VideoMediaVector jarg1_);
  public final static native void VideoMediaVector_clear(long jarg1, VideoMediaVector jarg1_);
  public final static native long new_VideoMediaVector__SWIG_2(int jarg1, long jarg2, VideoMedia jarg2_);
  public final static native int VideoMediaVector_doSize(long jarg1, VideoMediaVector jarg1_);
  public final static native void VideoMediaVector_doAdd__SWIG_0(long jarg1, VideoMediaVector jarg1_, long jarg2, VideoMedia jarg2_);
  public final static native void VideoMediaVector_doAdd__SWIG_1(long jarg1, VideoMediaVector jarg1_, int jarg2, long jarg3, VideoMedia jarg3_);
  public final static native long VideoMediaVector_doRemove(long jarg1, VideoMediaVector jarg1_, int jarg2);
  public final static native long VideoMediaVector_doGet(long jarg1, VideoMediaVector jarg1_, int jarg2);
  public final static native long VideoMediaVector_doSet(long jarg1, VideoMediaVector jarg1_, int jarg2, long jarg3, VideoMedia jarg3_);
  public final static native void VideoMediaVector_doRemoveRange(long jarg1, VideoMediaVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_VideoMediaVector(long jarg1);
  public final static native long new_ToneDescVector__SWIG_0();
  public final static native long new_ToneDescVector__SWIG_1(long jarg1, ToneDescVector jarg1_);
  public final static native long ToneDescVector_capacity(long jarg1, ToneDescVector jarg1_);
  public final static native void ToneDescVector_reserve(long jarg1, ToneDescVector jarg1_, long jarg2);
  public final static native boolean ToneDescVector_isEmpty(long jarg1, ToneDescVector jarg1_);
  public final static native void ToneDescVector_clear(long jarg1, ToneDescVector jarg1_);
  public final static native long new_ToneDescVector__SWIG_2(int jarg1, long jarg2, ToneDesc jarg2_);
  public final static native int ToneDescVector_doSize(long jarg1, ToneDescVector jarg1_);
  public final static native void ToneDescVector_doAdd__SWIG_0(long jarg1, ToneDescVector jarg1_, long jarg2, ToneDesc jarg2_);
  public final static native void ToneDescVector_doAdd__SWIG_1(long jarg1, ToneDescVector jarg1_, int jarg2, long jarg3, ToneDesc jarg3_);
  public final static native long ToneDescVector_doRemove(long jarg1, ToneDescVector jarg1_, int jarg2);
  public final static native long ToneDescVector_doGet(long jarg1, ToneDescVector jarg1_, int jarg2);
  public final static native long ToneDescVector_doSet(long jarg1, ToneDescVector jarg1_, int jarg2, long jarg3, ToneDesc jarg3_);
  public final static native void ToneDescVector_doRemoveRange(long jarg1, ToneDescVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_ToneDescVector(long jarg1);
  public final static native long new_ToneDigitVector__SWIG_0();
  public final static native long new_ToneDigitVector__SWIG_1(long jarg1, ToneDigitVector jarg1_);
  public final static native long ToneDigitVector_capacity(long jarg1, ToneDigitVector jarg1_);
  public final static native void ToneDigitVector_reserve(long jarg1, ToneDigitVector jarg1_, long jarg2);
  public final static native boolean ToneDigitVector_isEmpty(long jarg1, ToneDigitVector jarg1_);
  public final static native void ToneDigitVector_clear(long jarg1, ToneDigitVector jarg1_);
  public final static native long new_ToneDigitVector__SWIG_2(int jarg1, long jarg2, ToneDigit jarg2_);
  public final static native int ToneDigitVector_doSize(long jarg1, ToneDigitVector jarg1_);
  public final static native void ToneDigitVector_doAdd__SWIG_0(long jarg1, ToneDigitVector jarg1_, long jarg2, ToneDigit jarg2_);
  public final static native void ToneDigitVector_doAdd__SWIG_1(long jarg1, ToneDigitVector jarg1_, int jarg2, long jarg3, ToneDigit jarg3_);
  public final static native long ToneDigitVector_doRemove(long jarg1, ToneDigitVector jarg1_, int jarg2);
  public final static native long ToneDigitVector_doGet(long jarg1, ToneDigitVector jarg1_, int jarg2);
  public final static native long ToneDigitVector_doSet(long jarg1, ToneDigitVector jarg1_, int jarg2, long jarg3, ToneDigit jarg3_);
  public final static native void ToneDigitVector_doRemoveRange(long jarg1, ToneDigitVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_ToneDigitVector(long jarg1);
  public final static native long new_ToneDigitMapVector__SWIG_0();
  public final static native long new_ToneDigitMapVector__SWIG_1(long jarg1, ToneDigitMapVector jarg1_);
  public final static native long ToneDigitMapVector_capacity(long jarg1, ToneDigitMapVector jarg1_);
  public final static native void ToneDigitMapVector_reserve(long jarg1, ToneDigitMapVector jarg1_, long jarg2);
  public final static native boolean ToneDigitMapVector_isEmpty(long jarg1, ToneDigitMapVector jarg1_);
  public final static native void ToneDigitMapVector_clear(long jarg1, ToneDigitMapVector jarg1_);
  public final static native long new_ToneDigitMapVector__SWIG_2(int jarg1, long jarg2, ToneDigitMapDigit jarg2_);
  public final static native int ToneDigitMapVector_doSize(long jarg1, ToneDigitMapVector jarg1_);
  public final static native void ToneDigitMapVector_doAdd__SWIG_0(long jarg1, ToneDigitMapVector jarg1_, long jarg2, ToneDigitMapDigit jarg2_);
  public final static native void ToneDigitMapVector_doAdd__SWIG_1(long jarg1, ToneDigitMapVector jarg1_, int jarg2, long jarg3, ToneDigitMapDigit jarg3_);
  public final static native long ToneDigitMapVector_doRemove(long jarg1, ToneDigitMapVector jarg1_, int jarg2);
  public final static native long ToneDigitMapVector_doGet(long jarg1, ToneDigitMapVector jarg1_, int jarg2);
  public final static native long ToneDigitMapVector_doSet(long jarg1, ToneDigitMapVector jarg1_, int jarg2, long jarg3, ToneDigitMapDigit jarg3_);
  public final static native void ToneDigitMapVector_doRemoveRange(long jarg1, ToneDigitMapVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_ToneDigitMapVector(long jarg1);
  public final static native long new_AudioDevInfoVector__SWIG_0();
  public final static native long new_AudioDevInfoVector__SWIG_1(long jarg1, AudioDevInfoVector jarg1_);
  public final static native long AudioDevInfoVector_capacity(long jarg1, AudioDevInfoVector jarg1_);
  public final static native void AudioDevInfoVector_reserve(long jarg1, AudioDevInfoVector jarg1_, long jarg2);
  public final static native boolean AudioDevInfoVector_isEmpty(long jarg1, AudioDevInfoVector jarg1_);
  public final static native void AudioDevInfoVector_clear(long jarg1, AudioDevInfoVector jarg1_);
  public final static native long new_AudioDevInfoVector__SWIG_2(int jarg1, long jarg2, AudioDevInfo jarg2_);
  public final static native int AudioDevInfoVector_doSize(long jarg1, AudioDevInfoVector jarg1_);
  public final static native void AudioDevInfoVector_doAdd__SWIG_0(long jarg1, AudioDevInfoVector jarg1_, long jarg2, AudioDevInfo jarg2_);
  public final static native void AudioDevInfoVector_doAdd__SWIG_1(long jarg1, AudioDevInfoVector jarg1_, int jarg2, long jarg3, AudioDevInfo jarg3_);
  public final static native long AudioDevInfoVector_doRemove(long jarg1, AudioDevInfoVector jarg1_, int jarg2);
  public final static native long AudioDevInfoVector_doGet(long jarg1, AudioDevInfoVector jarg1_, int jarg2);
  public final static native long AudioDevInfoVector_doSet(long jarg1, AudioDevInfoVector jarg1_, int jarg2, long jarg3, AudioDevInfo jarg3_);
  public final static native void AudioDevInfoVector_doRemoveRange(long jarg1, AudioDevInfoVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_AudioDevInfoVector(long jarg1);
  public final static native long new_AudioDevInfoVector2__SWIG_0();
  public final static native long new_AudioDevInfoVector2__SWIG_1(long jarg1, AudioDevInfoVector2 jarg1_);
  public final static native long AudioDevInfoVector2_capacity(long jarg1, AudioDevInfoVector2 jarg1_);
  public final static native void AudioDevInfoVector2_reserve(long jarg1, AudioDevInfoVector2 jarg1_, long jarg2);
  public final static native boolean AudioDevInfoVector2_isEmpty(long jarg1, AudioDevInfoVector2 jarg1_);
  public final static native void AudioDevInfoVector2_clear(long jarg1, AudioDevInfoVector2 jarg1_);
  public final static native long new_AudioDevInfoVector2__SWIG_2(int jarg1, long jarg2, AudioDevInfo jarg2_);
  public final static native int AudioDevInfoVector2_doSize(long jarg1, AudioDevInfoVector2 jarg1_);
  public final static native void AudioDevInfoVector2_doAdd__SWIG_0(long jarg1, AudioDevInfoVector2 jarg1_, long jarg2, AudioDevInfo jarg2_);
  public final static native void AudioDevInfoVector2_doAdd__SWIG_1(long jarg1, AudioDevInfoVector2 jarg1_, int jarg2, long jarg3, AudioDevInfo jarg3_);
  public final static native long AudioDevInfoVector2_doRemove(long jarg1, AudioDevInfoVector2 jarg1_, int jarg2);
  public final static native long AudioDevInfoVector2_doGet(long jarg1, AudioDevInfoVector2 jarg1_, int jarg2);
  public final static native long AudioDevInfoVector2_doSet(long jarg1, AudioDevInfoVector2 jarg1_, int jarg2, long jarg3, AudioDevInfo jarg3_);
  public final static native void AudioDevInfoVector2_doRemoveRange(long jarg1, AudioDevInfoVector2 jarg1_, int jarg2, int jarg3);
  public final static native void delete_AudioDevInfoVector2(long jarg1);
  public final static native long new_CodecInfoVector__SWIG_0();
  public final static native long new_CodecInfoVector__SWIG_1(long jarg1, CodecInfoVector jarg1_);
  public final static native long CodecInfoVector_capacity(long jarg1, CodecInfoVector jarg1_);
  public final static native void CodecInfoVector_reserve(long jarg1, CodecInfoVector jarg1_, long jarg2);
  public final static native boolean CodecInfoVector_isEmpty(long jarg1, CodecInfoVector jarg1_);
  public final static native void CodecInfoVector_clear(long jarg1, CodecInfoVector jarg1_);
  public final static native long new_CodecInfoVector__SWIG_2(int jarg1, long jarg2, CodecInfo jarg2_);
  public final static native int CodecInfoVector_doSize(long jarg1, CodecInfoVector jarg1_);
  public final static native void CodecInfoVector_doAdd__SWIG_0(long jarg1, CodecInfoVector jarg1_, long jarg2, CodecInfo jarg2_);
  public final static native void CodecInfoVector_doAdd__SWIG_1(long jarg1, CodecInfoVector jarg1_, int jarg2, long jarg3, CodecInfo jarg3_);
  public final static native long CodecInfoVector_doRemove(long jarg1, CodecInfoVector jarg1_, int jarg2);
  public final static native long CodecInfoVector_doGet(long jarg1, CodecInfoVector jarg1_, int jarg2);
  public final static native long CodecInfoVector_doSet(long jarg1, CodecInfoVector jarg1_, int jarg2, long jarg3, CodecInfo jarg3_);
  public final static native void CodecInfoVector_doRemoveRange(long jarg1, CodecInfoVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_CodecInfoVector(long jarg1);
  public final static native long new_CodecInfoVector2__SWIG_0();
  public final static native long new_CodecInfoVector2__SWIG_1(long jarg1, CodecInfoVector2 jarg1_);
  public final static native long CodecInfoVector2_capacity(long jarg1, CodecInfoVector2 jarg1_);
  public final static native void CodecInfoVector2_reserve(long jarg1, CodecInfoVector2 jarg1_, long jarg2);
  public final static native boolean CodecInfoVector2_isEmpty(long jarg1, CodecInfoVector2 jarg1_);
  public final static native void CodecInfoVector2_clear(long jarg1, CodecInfoVector2 jarg1_);
  public final static native long new_CodecInfoVector2__SWIG_2(int jarg1, long jarg2, CodecInfo jarg2_);
  public final static native int CodecInfoVector2_doSize(long jarg1, CodecInfoVector2 jarg1_);
  public final static native void CodecInfoVector2_doAdd__SWIG_0(long jarg1, CodecInfoVector2 jarg1_, long jarg2, CodecInfo jarg2_);
  public final static native void CodecInfoVector2_doAdd__SWIG_1(long jarg1, CodecInfoVector2 jarg1_, int jarg2, long jarg3, CodecInfo jarg3_);
  public final static native long CodecInfoVector2_doRemove(long jarg1, CodecInfoVector2 jarg1_, int jarg2);
  public final static native long CodecInfoVector2_doGet(long jarg1, CodecInfoVector2 jarg1_, int jarg2);
  public final static native long CodecInfoVector2_doSet(long jarg1, CodecInfoVector2 jarg1_, int jarg2, long jarg3, CodecInfo jarg3_);
  public final static native void CodecInfoVector2_doRemoveRange(long jarg1, CodecInfoVector2 jarg1_, int jarg2, int jarg3);
  public final static native void delete_CodecInfoVector2(long jarg1);
  public final static native long new_VideoDevInfoVector__SWIG_0();
  public final static native long new_VideoDevInfoVector__SWIG_1(long jarg1, VideoDevInfoVector jarg1_);
  public final static native long VideoDevInfoVector_capacity(long jarg1, VideoDevInfoVector jarg1_);
  public final static native void VideoDevInfoVector_reserve(long jarg1, VideoDevInfoVector jarg1_, long jarg2);
  public final static native boolean VideoDevInfoVector_isEmpty(long jarg1, VideoDevInfoVector jarg1_);
  public final static native void VideoDevInfoVector_clear(long jarg1, VideoDevInfoVector jarg1_);
  public final static native long new_VideoDevInfoVector__SWIG_2(int jarg1, long jarg2, VideoDevInfo jarg2_);
  public final static native int VideoDevInfoVector_doSize(long jarg1, VideoDevInfoVector jarg1_);
  public final static native void VideoDevInfoVector_doAdd__SWIG_0(long jarg1, VideoDevInfoVector jarg1_, long jarg2, VideoDevInfo jarg2_);
  public final static native void VideoDevInfoVector_doAdd__SWIG_1(long jarg1, VideoDevInfoVector jarg1_, int jarg2, long jarg3, VideoDevInfo jarg3_);
  public final static native long VideoDevInfoVector_doRemove(long jarg1, VideoDevInfoVector jarg1_, int jarg2);
  public final static native long VideoDevInfoVector_doGet(long jarg1, VideoDevInfoVector jarg1_, int jarg2);
  public final static native long VideoDevInfoVector_doSet(long jarg1, VideoDevInfoVector jarg1_, int jarg2, long jarg3, VideoDevInfo jarg3_);
  public final static native void VideoDevInfoVector_doRemoveRange(long jarg1, VideoDevInfoVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_VideoDevInfoVector(long jarg1);
  public final static native long new_VideoDevInfoVector2__SWIG_0();
  public final static native long new_VideoDevInfoVector2__SWIG_1(long jarg1, VideoDevInfoVector2 jarg1_);
  public final static native long VideoDevInfoVector2_capacity(long jarg1, VideoDevInfoVector2 jarg1_);
  public final static native void VideoDevInfoVector2_reserve(long jarg1, VideoDevInfoVector2 jarg1_, long jarg2);
  public final static native boolean VideoDevInfoVector2_isEmpty(long jarg1, VideoDevInfoVector2 jarg1_);
  public final static native void VideoDevInfoVector2_clear(long jarg1, VideoDevInfoVector2 jarg1_);
  public final static native long new_VideoDevInfoVector2__SWIG_2(int jarg1, long jarg2, VideoDevInfo jarg2_);
  public final static native int VideoDevInfoVector2_doSize(long jarg1, VideoDevInfoVector2 jarg1_);
  public final static native void VideoDevInfoVector2_doAdd__SWIG_0(long jarg1, VideoDevInfoVector2 jarg1_, long jarg2, VideoDevInfo jarg2_);
  public final static native void VideoDevInfoVector2_doAdd__SWIG_1(long jarg1, VideoDevInfoVector2 jarg1_, int jarg2, long jarg3, VideoDevInfo jarg3_);
  public final static native long VideoDevInfoVector2_doRemove(long jarg1, VideoDevInfoVector2 jarg1_, int jarg2);
  public final static native long VideoDevInfoVector2_doGet(long jarg1, VideoDevInfoVector2 jarg1_, int jarg2);
  public final static native long VideoDevInfoVector2_doSet(long jarg1, VideoDevInfoVector2 jarg1_, int jarg2, long jarg3, VideoDevInfo jarg3_);
  public final static native void VideoDevInfoVector2_doRemoveRange(long jarg1, VideoDevInfoVector2 jarg1_, int jarg2, int jarg3);
  public final static native void delete_VideoDevInfoVector2(long jarg1);
  public final static native long new_CodecFmtpVector__SWIG_0();
  public final static native long new_CodecFmtpVector__SWIG_1(long jarg1, CodecFmtpVector jarg1_);
  public final static native long CodecFmtpVector_capacity(long jarg1, CodecFmtpVector jarg1_);
  public final static native void CodecFmtpVector_reserve(long jarg1, CodecFmtpVector jarg1_, long jarg2);
  public final static native boolean CodecFmtpVector_isEmpty(long jarg1, CodecFmtpVector jarg1_);
  public final static native void CodecFmtpVector_clear(long jarg1, CodecFmtpVector jarg1_);
  public final static native long new_CodecFmtpVector__SWIG_2(int jarg1, long jarg2, CodecFmtp jarg2_);
  public final static native int CodecFmtpVector_doSize(long jarg1, CodecFmtpVector jarg1_);
  public final static native void CodecFmtpVector_doAdd__SWIG_0(long jarg1, CodecFmtpVector jarg1_, long jarg2, CodecFmtp jarg2_);
  public final static native void CodecFmtpVector_doAdd__SWIG_1(long jarg1, CodecFmtpVector jarg1_, int jarg2, long jarg3, CodecFmtp jarg3_);
  public final static native long CodecFmtpVector_doRemove(long jarg1, CodecFmtpVector jarg1_, int jarg2);
  public final static native long CodecFmtpVector_doGet(long jarg1, CodecFmtpVector jarg1_, int jarg2);
  public final static native long CodecFmtpVector_doSet(long jarg1, CodecFmtpVector jarg1_, int jarg2, long jarg3, CodecFmtp jarg3_);
  public final static native void CodecFmtpVector_doRemoveRange(long jarg1, CodecFmtpVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_CodecFmtpVector(long jarg1);
  public final static native long new_MediaFormatAudioVector__SWIG_0();
  public final static native long new_MediaFormatAudioVector__SWIG_1(long jarg1, MediaFormatAudioVector jarg1_);
  public final static native long MediaFormatAudioVector_capacity(long jarg1, MediaFormatAudioVector jarg1_);
  public final static native void MediaFormatAudioVector_reserve(long jarg1, MediaFormatAudioVector jarg1_, long jarg2);
  public final static native boolean MediaFormatAudioVector_isEmpty(long jarg1, MediaFormatAudioVector jarg1_);
  public final static native void MediaFormatAudioVector_clear(long jarg1, MediaFormatAudioVector jarg1_);
  public final static native long new_MediaFormatAudioVector__SWIG_2(int jarg1, long jarg2, MediaFormatAudio jarg2_);
  public final static native int MediaFormatAudioVector_doSize(long jarg1, MediaFormatAudioVector jarg1_);
  public final static native void MediaFormatAudioVector_doAdd__SWIG_0(long jarg1, MediaFormatAudioVector jarg1_, long jarg2, MediaFormatAudio jarg2_);
  public final static native void MediaFormatAudioVector_doAdd__SWIG_1(long jarg1, MediaFormatAudioVector jarg1_, int jarg2, long jarg3, MediaFormatAudio jarg3_);
  public final static native long MediaFormatAudioVector_doRemove(long jarg1, MediaFormatAudioVector jarg1_, int jarg2);
  public final static native long MediaFormatAudioVector_doGet(long jarg1, MediaFormatAudioVector jarg1_, int jarg2);
  public final static native long MediaFormatAudioVector_doSet(long jarg1, MediaFormatAudioVector jarg1_, int jarg2, long jarg3, MediaFormatAudio jarg3_);
  public final static native void MediaFormatAudioVector_doRemoveRange(long jarg1, MediaFormatAudioVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_MediaFormatAudioVector(long jarg1);
  public final static native long new_MediaFormatVideoVector__SWIG_0();
  public final static native long new_MediaFormatVideoVector__SWIG_1(long jarg1, MediaFormatVideoVector jarg1_);
  public final static native long MediaFormatVideoVector_capacity(long jarg1, MediaFormatVideoVector jarg1_);
  public final static native void MediaFormatVideoVector_reserve(long jarg1, MediaFormatVideoVector jarg1_, long jarg2);
  public final static native boolean MediaFormatVideoVector_isEmpty(long jarg1, MediaFormatVideoVector jarg1_);
  public final static native void MediaFormatVideoVector_clear(long jarg1, MediaFormatVideoVector jarg1_);
  public final static native long new_MediaFormatVideoVector__SWIG_2(int jarg1, long jarg2, MediaFormatVideo jarg2_);
  public final static native int MediaFormatVideoVector_doSize(long jarg1, MediaFormatVideoVector jarg1_);
  public final static native void MediaFormatVideoVector_doAdd__SWIG_0(long jarg1, MediaFormatVideoVector jarg1_, long jarg2, MediaFormatVideo jarg2_);
  public final static native void MediaFormatVideoVector_doAdd__SWIG_1(long jarg1, MediaFormatVideoVector jarg1_, int jarg2, long jarg3, MediaFormatVideo jarg3_);
  public final static native long MediaFormatVideoVector_doRemove(long jarg1, MediaFormatVideoVector jarg1_, int jarg2);
  public final static native long MediaFormatVideoVector_doGet(long jarg1, MediaFormatVideoVector jarg1_, int jarg2);
  public final static native long MediaFormatVideoVector_doSet(long jarg1, MediaFormatVideoVector jarg1_, int jarg2, long jarg3, MediaFormatVideo jarg3_);
  public final static native void MediaFormatVideoVector_doRemoveRange(long jarg1, MediaFormatVideoVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_MediaFormatVideoVector(long jarg1);
  public final static native long new_CallMediaInfoVector__SWIG_0();
  public final static native long new_CallMediaInfoVector__SWIG_1(long jarg1, CallMediaInfoVector jarg1_);
  public final static native long CallMediaInfoVector_capacity(long jarg1, CallMediaInfoVector jarg1_);
  public final static native void CallMediaInfoVector_reserve(long jarg1, CallMediaInfoVector jarg1_, long jarg2);
  public final static native boolean CallMediaInfoVector_isEmpty(long jarg1, CallMediaInfoVector jarg1_);
  public final static native void CallMediaInfoVector_clear(long jarg1, CallMediaInfoVector jarg1_);
  public final static native long new_CallMediaInfoVector__SWIG_2(int jarg1, long jarg2, CallMediaInfo jarg2_);
  public final static native int CallMediaInfoVector_doSize(long jarg1, CallMediaInfoVector jarg1_);
  public final static native void CallMediaInfoVector_doAdd__SWIG_0(long jarg1, CallMediaInfoVector jarg1_, long jarg2, CallMediaInfo jarg2_);
  public final static native void CallMediaInfoVector_doAdd__SWIG_1(long jarg1, CallMediaInfoVector jarg1_, int jarg2, long jarg3, CallMediaInfo jarg3_);
  public final static native long CallMediaInfoVector_doRemove(long jarg1, CallMediaInfoVector jarg1_, int jarg2);
  public final static native long CallMediaInfoVector_doGet(long jarg1, CallMediaInfoVector jarg1_, int jarg2);
  public final static native long CallMediaInfoVector_doSet(long jarg1, CallMediaInfoVector jarg1_, int jarg2, long jarg3, CallMediaInfo jarg3_);
  public final static native void CallMediaInfoVector_doRemoveRange(long jarg1, CallMediaInfoVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_CallMediaInfoVector(long jarg1);
  public final static native long new_RtcpFbCapVector__SWIG_0();
  public final static native long new_RtcpFbCapVector__SWIG_1(long jarg1, RtcpFbCapVector jarg1_);
  public final static native long RtcpFbCapVector_capacity(long jarg1, RtcpFbCapVector jarg1_);
  public final static native void RtcpFbCapVector_reserve(long jarg1, RtcpFbCapVector jarg1_, long jarg2);
  public final static native boolean RtcpFbCapVector_isEmpty(long jarg1, RtcpFbCapVector jarg1_);
  public final static native void RtcpFbCapVector_clear(long jarg1, RtcpFbCapVector jarg1_);
  public final static native long new_RtcpFbCapVector__SWIG_2(int jarg1, long jarg2, RtcpFbCap jarg2_);
  public final static native int RtcpFbCapVector_doSize(long jarg1, RtcpFbCapVector jarg1_);
  public final static native void RtcpFbCapVector_doAdd__SWIG_0(long jarg1, RtcpFbCapVector jarg1_, long jarg2, RtcpFbCap jarg2_);
  public final static native void RtcpFbCapVector_doAdd__SWIG_1(long jarg1, RtcpFbCapVector jarg1_, int jarg2, long jarg3, RtcpFbCap jarg3_);
  public final static native long RtcpFbCapVector_doRemove(long jarg1, RtcpFbCapVector jarg1_, int jarg2);
  public final static native long RtcpFbCapVector_doGet(long jarg1, RtcpFbCapVector jarg1_, int jarg2);
  public final static native long RtcpFbCapVector_doSet(long jarg1, RtcpFbCapVector jarg1_, int jarg2, long jarg3, RtcpFbCap jarg3_);
  public final static native void RtcpFbCapVector_doRemoveRange(long jarg1, RtcpFbCapVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_RtcpFbCapVector(long jarg1);
  public final static native long new_SslCertNameVector__SWIG_0();
  public final static native long new_SslCertNameVector__SWIG_1(long jarg1, SslCertNameVector jarg1_);
  public final static native long SslCertNameVector_capacity(long jarg1, SslCertNameVector jarg1_);
  public final static native void SslCertNameVector_reserve(long jarg1, SslCertNameVector jarg1_, long jarg2);
  public final static native boolean SslCertNameVector_isEmpty(long jarg1, SslCertNameVector jarg1_);
  public final static native void SslCertNameVector_clear(long jarg1, SslCertNameVector jarg1_);
  public final static native long new_SslCertNameVector__SWIG_2(int jarg1, long jarg2, SslCertName jarg2_);
  public final static native int SslCertNameVector_doSize(long jarg1, SslCertNameVector jarg1_);
  public final static native void SslCertNameVector_doAdd__SWIG_0(long jarg1, SslCertNameVector jarg1_, long jarg2, SslCertName jarg2_);
  public final static native void SslCertNameVector_doAdd__SWIG_1(long jarg1, SslCertNameVector jarg1_, int jarg2, long jarg3, SslCertName jarg3_);
  public final static native long SslCertNameVector_doRemove(long jarg1, SslCertNameVector jarg1_, int jarg2);
  public final static native long SslCertNameVector_doGet(long jarg1, SslCertNameVector jarg1_, int jarg2);
  public final static native long SslCertNameVector_doSet(long jarg1, SslCertNameVector jarg1_, int jarg2, long jarg3, SslCertName jarg3_);
  public final static native void SslCertNameVector_doRemoveRange(long jarg1, SslCertNameVector jarg1_, int jarg2, int jarg3);
  public final static native void delete_SslCertNameVector(long jarg1);
  public final static native void MediaFormat_id_set(long jarg1, MediaFormat jarg1_, long jarg2);
  public final static native long MediaFormat_id_get(long jarg1, MediaFormat jarg1_);
  public final static native void MediaFormat_type_set(long jarg1, MediaFormat jarg1_, int jarg2);
  public final static native int MediaFormat_type_get(long jarg1, MediaFormat jarg1_);
  public final static native long new_MediaFormat();
  public final static native void delete_MediaFormat(long jarg1);
  public final static native void MediaFormatAudio_clockRate_set(long jarg1, MediaFormatAudio jarg1_, long jarg2);
  public final static native long MediaFormatAudio_clockRate_get(long jarg1, MediaFormatAudio jarg1_);
  public final static native void MediaFormatAudio_channelCount_set(long jarg1, MediaFormatAudio jarg1_, long jarg2);
  public final static native long MediaFormatAudio_channelCount_get(long jarg1, MediaFormatAudio jarg1_);
  public final static native void MediaFormatAudio_frameTimeUsec_set(long jarg1, MediaFormatAudio jarg1_, long jarg2);
  public final static native long MediaFormatAudio_frameTimeUsec_get(long jarg1, MediaFormatAudio jarg1_);
  public final static native void MediaFormatAudio_bitsPerSample_set(long jarg1, MediaFormatAudio jarg1_, long jarg2);
  public final static native long MediaFormatAudio_bitsPerSample_get(long jarg1, MediaFormatAudio jarg1_);
  public final static native void MediaFormatAudio_avgBps_set(long jarg1, MediaFormatAudio jarg1_, long jarg2);
  public final static native long MediaFormatAudio_avgBps_get(long jarg1, MediaFormatAudio jarg1_);
  public final static native void MediaFormatAudio_maxBps_set(long jarg1, MediaFormatAudio jarg1_, long jarg2);
  public final static native long MediaFormatAudio_maxBps_get(long jarg1, MediaFormatAudio jarg1_);
  public final static native long new_MediaFormatAudio();
  public final static native void delete_MediaFormatAudio(long jarg1);
  public final static native void MediaFormatVideo_width_set(long jarg1, MediaFormatVideo jarg1_, long jarg2);
  public final static native long MediaFormatVideo_width_get(long jarg1, MediaFormatVideo jarg1_);
  public final static native void MediaFormatVideo_height_set(long jarg1, MediaFormatVideo jarg1_, long jarg2);
  public final static native long MediaFormatVideo_height_get(long jarg1, MediaFormatVideo jarg1_);
  public final static native void MediaFormatVideo_fpsNum_set(long jarg1, MediaFormatVideo jarg1_, int jarg2);
  public final static native int MediaFormatVideo_fpsNum_get(long jarg1, MediaFormatVideo jarg1_);
  public final static native void MediaFormatVideo_fpsDenum_set(long jarg1, MediaFormatVideo jarg1_, int jarg2);
  public final static native int MediaFormatVideo_fpsDenum_get(long jarg1, MediaFormatVideo jarg1_);
  public final static native void MediaFormatVideo_avgBps_set(long jarg1, MediaFormatVideo jarg1_, long jarg2);
  public final static native long MediaFormatVideo_avgBps_get(long jarg1, MediaFormatVideo jarg1_);
  public final static native void MediaFormatVideo_maxBps_set(long jarg1, MediaFormatVideo jarg1_, long jarg2);
  public final static native long MediaFormatVideo_maxBps_get(long jarg1, MediaFormatVideo jarg1_);
  public final static native long new_MediaFormatVideo();
  public final static native void delete_MediaFormatVideo(long jarg1);
  public final static native void ConfPortInfo_portId_set(long jarg1, ConfPortInfo jarg1_, int jarg2);
  public final static native int ConfPortInfo_portId_get(long jarg1, ConfPortInfo jarg1_);
  public final static native void ConfPortInfo_name_set(long jarg1, ConfPortInfo jarg1_, String jarg2);
  public final static native String ConfPortInfo_name_get(long jarg1, ConfPortInfo jarg1_);
  public final static native void ConfPortInfo_format_set(long jarg1, ConfPortInfo jarg1_, long jarg2, MediaFormatAudio jarg2_);
  public final static native long ConfPortInfo_format_get(long jarg1, ConfPortInfo jarg1_);
  public final static native void ConfPortInfo_txLevelAdj_set(long jarg1, ConfPortInfo jarg1_, float jarg2);
  public final static native float ConfPortInfo_txLevelAdj_get(long jarg1, ConfPortInfo jarg1_);
  public final static native void ConfPortInfo_rxLevelAdj_set(long jarg1, ConfPortInfo jarg1_, float jarg2);
  public final static native float ConfPortInfo_rxLevelAdj_get(long jarg1, ConfPortInfo jarg1_);
  public final static native void ConfPortInfo_listeners_set(long jarg1, ConfPortInfo jarg1_, long jarg2, IntVector jarg2_);
  public final static native long ConfPortInfo_listeners_get(long jarg1, ConfPortInfo jarg1_);
  public final static native long new_ConfPortInfo();
  public final static native void delete_ConfPortInfo(long jarg1);
  public final static native void delete_Media(long jarg1);
  public final static native int Media_getType(long jarg1, Media jarg1_);
  public final static native void AudioMediaTransmitParam_level_set(long jarg1, AudioMediaTransmitParam jarg1_, float jarg2);
  public final static native float AudioMediaTransmitParam_level_get(long jarg1, AudioMediaTransmitParam jarg1_);
  public final static native long new_AudioMediaTransmitParam();
  public final static native void delete_AudioMediaTransmitParam(long jarg1);
  public final static native long AudioMedia_getPortInfo(long jarg1, AudioMedia jarg1_) throws java.lang.Exception;
  public final static native int AudioMedia_getPortId(long jarg1, AudioMedia jarg1_);
  public final static native long AudioMedia_getPortInfoFromId(int jarg1) throws java.lang.Exception;
  public final static native void AudioMedia_startTransmit(long jarg1, AudioMedia jarg1_, long jarg2, AudioMedia jarg2_) throws java.lang.Exception;
  public final static native void AudioMedia_startTransmit2(long jarg1, AudioMedia jarg1_, long jarg2, AudioMedia jarg2_, long jarg3, AudioMediaTransmitParam jarg3_) throws java.lang.Exception;
  public final static native void AudioMedia_stopTransmit(long jarg1, AudioMedia jarg1_, long jarg2, AudioMedia jarg2_) throws java.lang.Exception;
  public final static native void AudioMedia_adjustRxLevel(long jarg1, AudioMedia jarg1_, float jarg2) throws java.lang.Exception;
  public final static native void AudioMedia_adjustTxLevel(long jarg1, AudioMedia jarg1_, float jarg2) throws java.lang.Exception;
  public final static native long AudioMedia_getRxLevel(long jarg1, AudioMedia jarg1_) throws java.lang.Exception;
  public final static native long AudioMedia_getTxLevel(long jarg1, AudioMedia jarg1_) throws java.lang.Exception;
  public final static native long AudioMedia_typecastFromMedia(long jarg1, Media jarg1_);
  public final static native long new_AudioMedia();
  public final static native void delete_AudioMedia(long jarg1);
  public final static native void AudioMediaPlayerInfo_formatId_set(long jarg1, AudioMediaPlayerInfo jarg1_, int jarg2);
  public final static native int AudioMediaPlayerInfo_formatId_get(long jarg1, AudioMediaPlayerInfo jarg1_);
  public final static native void AudioMediaPlayerInfo_payloadBitsPerSample_set(long jarg1, AudioMediaPlayerInfo jarg1_, long jarg2);
  public final static native long AudioMediaPlayerInfo_payloadBitsPerSample_get(long jarg1, AudioMediaPlayerInfo jarg1_);
  public final static native void AudioMediaPlayerInfo_sizeBytes_set(long jarg1, AudioMediaPlayerInfo jarg1_, long jarg2);
  public final static native long AudioMediaPlayerInfo_sizeBytes_get(long jarg1, AudioMediaPlayerInfo jarg1_);
  public final static native void AudioMediaPlayerInfo_sizeSamples_set(long jarg1, AudioMediaPlayerInfo jarg1_, long jarg2);
  public final static native long AudioMediaPlayerInfo_sizeSamples_get(long jarg1, AudioMediaPlayerInfo jarg1_);
  public final static native long new_AudioMediaPlayerInfo();
  public final static native void delete_AudioMediaPlayerInfo(long jarg1);
  public final static native long new_AudioMediaPlayer();
  public final static native void AudioMediaPlayer_createPlayer__SWIG_0(long jarg1, AudioMediaPlayer jarg1_, String jarg2, long jarg3) throws java.lang.Exception;
  public final static native void AudioMediaPlayer_createPlayer__SWIG_1(long jarg1, AudioMediaPlayer jarg1_, String jarg2) throws java.lang.Exception;
  public final static native void AudioMediaPlayer_createPlaylist__SWIG_0(long jarg1, AudioMediaPlayer jarg1_, long jarg2, StringVector jarg2_, String jarg3, long jarg4) throws java.lang.Exception;
  public final static native void AudioMediaPlayer_createPlaylist__SWIG_1(long jarg1, AudioMediaPlayer jarg1_, long jarg2, StringVector jarg2_, String jarg3) throws java.lang.Exception;
  public final static native void AudioMediaPlayer_createPlaylist__SWIG_2(long jarg1, AudioMediaPlayer jarg1_, long jarg2, StringVector jarg2_) throws java.lang.Exception;
  public final static native long AudioMediaPlayer_getInfo(long jarg1, AudioMediaPlayer jarg1_) throws java.lang.Exception;
  public final static native long AudioMediaPlayer_getPos(long jarg1, AudioMediaPlayer jarg1_) throws java.lang.Exception;
  public final static native void AudioMediaPlayer_setPos(long jarg1, AudioMediaPlayer jarg1_, long jarg2) throws java.lang.Exception;
  public final static native long AudioMediaPlayer_typecastFromAudioMedia(long jarg1, AudioMedia jarg1_);
  public final static native void delete_AudioMediaPlayer(long jarg1);
  public final static native void AudioMediaPlayer_onEof2(long jarg1, AudioMediaPlayer jarg1_);
  public final static native void AudioMediaPlayer_onEof2SwigExplicitAudioMediaPlayer(long jarg1, AudioMediaPlayer jarg1_);
  public final static native void AudioMediaPlayer_director_connect(AudioMediaPlayer obj, long cptr, boolean mem_own, boolean weak_global);
  public final static native void AudioMediaPlayer_change_ownership(AudioMediaPlayer obj, long cptr, boolean take_or_release);
  public final static native long new_AudioMediaRecorder();
  public final static native void AudioMediaRecorder_createRecorder__SWIG_0(long jarg1, AudioMediaRecorder jarg1_, String jarg2, long jarg3, int jarg4, long jarg5) throws java.lang.Exception;
  public final static native void AudioMediaRecorder_createRecorder__SWIG_1(long jarg1, AudioMediaRecorder jarg1_, String jarg2, long jarg3, int jarg4) throws java.lang.Exception;
  public final static native void AudioMediaRecorder_createRecorder__SWIG_2(long jarg1, AudioMediaRecorder jarg1_, String jarg2, long jarg3) throws java.lang.Exception;
  public final static native void AudioMediaRecorder_createRecorder__SWIG_3(long jarg1, AudioMediaRecorder jarg1_, String jarg2) throws java.lang.Exception;
  public final static native long AudioMediaRecorder_typecastFromAudioMedia(long jarg1, AudioMedia jarg1_);
  public final static native void delete_AudioMediaRecorder(long jarg1);
  public final static native long new_ToneDesc();
  public final static native void delete_ToneDesc(long jarg1);
  public final static native long new_ToneDigit();
  public final static native void delete_ToneDigit(long jarg1);
  public final static native void ToneDigitMapDigit_digit_set(long jarg1, ToneDigitMapDigit jarg1_, String jarg2);
  public final static native String ToneDigitMapDigit_digit_get(long jarg1, ToneDigitMapDigit jarg1_);
  public final static native void ToneDigitMapDigit_freq1_set(long jarg1, ToneDigitMapDigit jarg1_, int jarg2);
  public final static native int ToneDigitMapDigit_freq1_get(long jarg1, ToneDigitMapDigit jarg1_);
  public final static native void ToneDigitMapDigit_freq2_set(long jarg1, ToneDigitMapDigit jarg1_, int jarg2);
  public final static native int ToneDigitMapDigit_freq2_get(long jarg1, ToneDigitMapDigit jarg1_);
  public final static native long new_ToneDigitMapDigit();
  public final static native void delete_ToneDigitMapDigit(long jarg1);
  public final static native long new_ToneGenerator();
  public final static native void delete_ToneGenerator(long jarg1);
  public final static native void ToneGenerator_createToneGenerator__SWIG_0(long jarg1, ToneGenerator jarg1_, long jarg2, long jarg3) throws java.lang.Exception;
  public final static native void ToneGenerator_createToneGenerator__SWIG_1(long jarg1, ToneGenerator jarg1_, long jarg2) throws java.lang.Exception;
  public final static native void ToneGenerator_createToneGenerator__SWIG_2(long jarg1, ToneGenerator jarg1_) throws java.lang.Exception;
  public final static native boolean ToneGenerator_isBusy(long jarg1, ToneGenerator jarg1_);
  public final static native void ToneGenerator_stop(long jarg1, ToneGenerator jarg1_) throws java.lang.Exception;
  public final static native void ToneGenerator_rewind(long jarg1, ToneGenerator jarg1_) throws java.lang.Exception;
  public final static native void ToneGenerator_play__SWIG_0(long jarg1, ToneGenerator jarg1_, long jarg2, ToneDescVector jarg2_, boolean jarg3) throws java.lang.Exception;
  public final static native void ToneGenerator_play__SWIG_1(long jarg1, ToneGenerator jarg1_, long jarg2, ToneDescVector jarg2_) throws java.lang.Exception;
  public final static native void ToneGenerator_playDigits__SWIG_0(long jarg1, ToneGenerator jarg1_, long jarg2, ToneDigitVector jarg2_, boolean jarg3) throws java.lang.Exception;
  public final static native void ToneGenerator_playDigits__SWIG_1(long jarg1, ToneGenerator jarg1_, long jarg2, ToneDigitVector jarg2_) throws java.lang.Exception;
  public final static native long ToneGenerator_getDigitMap(long jarg1, ToneGenerator jarg1_) throws java.lang.Exception;
  public final static native void ToneGenerator_setDigitMap(long jarg1, ToneGenerator jarg1_, long jarg2, ToneDigitMapVector jarg2_) throws java.lang.Exception;
  public final static native void AudioDevInfo_name_set(long jarg1, AudioDevInfo jarg1_, String jarg2);
  public final static native String AudioDevInfo_name_get(long jarg1, AudioDevInfo jarg1_);
  public final static native void AudioDevInfo_inputCount_set(long jarg1, AudioDevInfo jarg1_, long jarg2);
  public final static native long AudioDevInfo_inputCount_get(long jarg1, AudioDevInfo jarg1_);
  public final static native void AudioDevInfo_outputCount_set(long jarg1, AudioDevInfo jarg1_, long jarg2);
  public final static native long AudioDevInfo_outputCount_get(long jarg1, AudioDevInfo jarg1_);
  public final static native void AudioDevInfo_defaultSamplesPerSec_set(long jarg1, AudioDevInfo jarg1_, long jarg2);
  public final static native long AudioDevInfo_defaultSamplesPerSec_get(long jarg1, AudioDevInfo jarg1_);
  public final static native void AudioDevInfo_driver_set(long jarg1, AudioDevInfo jarg1_, String jarg2);
  public final static native String AudioDevInfo_driver_get(long jarg1, AudioDevInfo jarg1_);
  public final static native void AudioDevInfo_caps_set(long jarg1, AudioDevInfo jarg1_, long jarg2);
  public final static native long AudioDevInfo_caps_get(long jarg1, AudioDevInfo jarg1_);
  public final static native void AudioDevInfo_routes_set(long jarg1, AudioDevInfo jarg1_, long jarg2);
  public final static native long AudioDevInfo_routes_get(long jarg1, AudioDevInfo jarg1_);
  public final static native void AudioDevInfo_extFmt_set(long jarg1, AudioDevInfo jarg1_, long jarg2, MediaFormatAudioVector jarg2_);
  public final static native long AudioDevInfo_extFmt_get(long jarg1, AudioDevInfo jarg1_);
  public final static native void delete_AudioDevInfo(long jarg1);
  public final static native long new_AudioDevInfo();
  public final static native int AudDevManager_getCaptureDev(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native long AudDevManager_getCaptureDevMedia(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native int AudDevManager_getPlaybackDev(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native long AudDevManager_getPlaybackDevMedia(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native void AudDevManager_setCaptureDev(long jarg1, AudDevManager jarg1_, int jarg2) throws java.lang.Exception;
  public final static native void AudDevManager_setPlaybackDev(long jarg1, AudDevManager jarg1_, int jarg2) throws java.lang.Exception;
  public final static native long AudDevManager_enumDev2(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native void AudDevManager_setNullDev(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native long AudDevManager_setNoDev(long jarg1, AudDevManager jarg1_);
  public final static native void AudDevManager_setSndDevMode(long jarg1, AudDevManager jarg1_, long jarg2) throws java.lang.Exception;
  public final static native void AudDevManager_setEcOptions(long jarg1, AudDevManager jarg1_, long jarg2, long jarg3) throws java.lang.Exception;
  public final static native long AudDevManager_getEcTail(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native boolean AudDevManager_sndIsActive(long jarg1, AudDevManager jarg1_);
  public final static native void AudDevManager_refreshDevs(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native long AudDevManager_getDevCount(long jarg1, AudDevManager jarg1_);
  public final static native long AudDevManager_getDevInfo(long jarg1, AudDevManager jarg1_, int jarg2) throws java.lang.Exception;
  public final static native int AudDevManager_lookupDev(long jarg1, AudDevManager jarg1_, String jarg2, String jarg3) throws java.lang.Exception;
  public final static native String AudDevManager_capName(long jarg1, AudDevManager jarg1_, int jarg2);
  public final static native void AudDevManager_setExtFormat__SWIG_0(long jarg1, AudDevManager jarg1_, long jarg2, MediaFormatAudio jarg2_, boolean jarg3) throws java.lang.Exception;
  public final static native void AudDevManager_setExtFormat__SWIG_1(long jarg1, AudDevManager jarg1_, long jarg2, MediaFormatAudio jarg2_) throws java.lang.Exception;
  public final static native long AudDevManager_getExtFormat(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native void AudDevManager_setInputLatency__SWIG_0(long jarg1, AudDevManager jarg1_, long jarg2, boolean jarg3) throws java.lang.Exception;
  public final static native void AudDevManager_setInputLatency__SWIG_1(long jarg1, AudDevManager jarg1_, long jarg2) throws java.lang.Exception;
  public final static native long AudDevManager_getInputLatency(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native void AudDevManager_setOutputLatency__SWIG_0(long jarg1, AudDevManager jarg1_, long jarg2, boolean jarg3) throws java.lang.Exception;
  public final static native void AudDevManager_setOutputLatency__SWIG_1(long jarg1, AudDevManager jarg1_, long jarg2) throws java.lang.Exception;
  public final static native long AudDevManager_getOutputLatency(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native void AudDevManager_setInputVolume__SWIG_0(long jarg1, AudDevManager jarg1_, long jarg2, boolean jarg3) throws java.lang.Exception;
  public final static native void AudDevManager_setInputVolume__SWIG_1(long jarg1, AudDevManager jarg1_, long jarg2) throws java.lang.Exception;
  public final static native long AudDevManager_getInputVolume(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native void AudDevManager_setOutputVolume__SWIG_0(long jarg1, AudDevManager jarg1_, long jarg2, boolean jarg3) throws java.lang.Exception;
  public final static native void AudDevManager_setOutputVolume__SWIG_1(long jarg1, AudDevManager jarg1_, long jarg2) throws java.lang.Exception;
  public final static native long AudDevManager_getOutputVolume(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native long AudDevManager_getInputSignal(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native long AudDevManager_getOutputSignal(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native void AudDevManager_setInputRoute__SWIG_0(long jarg1, AudDevManager jarg1_, int jarg2, boolean jarg3) throws java.lang.Exception;
  public final static native void AudDevManager_setInputRoute__SWIG_1(long jarg1, AudDevManager jarg1_, int jarg2) throws java.lang.Exception;
  public final static native int AudDevManager_getInputRoute(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native void AudDevManager_setOutputRoute__SWIG_0(long jarg1, AudDevManager jarg1_, int jarg2, boolean jarg3) throws java.lang.Exception;
  public final static native void AudDevManager_setOutputRoute__SWIG_1(long jarg1, AudDevManager jarg1_, int jarg2) throws java.lang.Exception;
  public final static native int AudDevManager_getOutputRoute(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native void AudDevManager_setVad__SWIG_0(long jarg1, AudDevManager jarg1_, boolean jarg2, boolean jarg3) throws java.lang.Exception;
  public final static native void AudDevManager_setVad__SWIG_1(long jarg1, AudDevManager jarg1_, boolean jarg2) throws java.lang.Exception;
  public final static native boolean AudDevManager_getVad(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native void AudDevManager_setCng__SWIG_0(long jarg1, AudDevManager jarg1_, boolean jarg2, boolean jarg3) throws java.lang.Exception;
  public final static native void AudDevManager_setCng__SWIG_1(long jarg1, AudDevManager jarg1_, boolean jarg2) throws java.lang.Exception;
  public final static native boolean AudDevManager_getCng(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native void AudDevManager_setPlc__SWIG_0(long jarg1, AudDevManager jarg1_, boolean jarg2, boolean jarg3) throws java.lang.Exception;
  public final static native void AudDevManager_setPlc__SWIG_1(long jarg1, AudDevManager jarg1_, boolean jarg2) throws java.lang.Exception;
  public final static native boolean AudDevManager_getPlc(long jarg1, AudDevManager jarg1_) throws java.lang.Exception;
  public final static native long new_ExtraAudioDevice(int jarg1, int jarg2);
  public final static native void delete_ExtraAudioDevice(long jarg1);
  public final static native void ExtraAudioDevice_open(long jarg1, ExtraAudioDevice jarg1_);
  public final static native void ExtraAudioDevice_close(long jarg1, ExtraAudioDevice jarg1_);
  public final static native boolean ExtraAudioDevice_isOpened(long jarg1, ExtraAudioDevice jarg1_);
  public final static native void MediaCoordinate_x_set(long jarg1, MediaCoordinate jarg1_, int jarg2);
  public final static native int MediaCoordinate_x_get(long jarg1, MediaCoordinate jarg1_);
  public final static native void MediaCoordinate_y_set(long jarg1, MediaCoordinate jarg1_, int jarg2);
  public final static native int MediaCoordinate_y_get(long jarg1, MediaCoordinate jarg1_);
  public final static native long new_MediaCoordinate();
  public final static native void delete_MediaCoordinate(long jarg1);
  public final static native void MediaSize_w_set(long jarg1, MediaSize jarg1_, long jarg2);
  public final static native long MediaSize_w_get(long jarg1, MediaSize jarg1_);
  public final static native void MediaSize_h_set(long jarg1, MediaSize jarg1_, long jarg2);
  public final static native long MediaSize_h_get(long jarg1, MediaSize jarg1_);
  public final static native long new_MediaSize();
  public final static native void delete_MediaSize(long jarg1);
  public final static native void VidConfPortInfo_portId_set(long jarg1, VidConfPortInfo jarg1_, int jarg2);
  public final static native int VidConfPortInfo_portId_get(long jarg1, VidConfPortInfo jarg1_);
  public final static native void VidConfPortInfo_name_set(long jarg1, VidConfPortInfo jarg1_, String jarg2);
  public final static native String VidConfPortInfo_name_get(long jarg1, VidConfPortInfo jarg1_);
  public final static native void VidConfPortInfo_format_set(long jarg1, VidConfPortInfo jarg1_, long jarg2, MediaFormatVideo jarg2_);
  public final static native long VidConfPortInfo_format_get(long jarg1, VidConfPortInfo jarg1_);
  public final static native void VidConfPortInfo_listeners_set(long jarg1, VidConfPortInfo jarg1_, long jarg2, IntVector jarg2_);
  public final static native long VidConfPortInfo_listeners_get(long jarg1, VidConfPortInfo jarg1_);
  public final static native void VidConfPortInfo_transmitters_set(long jarg1, VidConfPortInfo jarg1_, long jarg2, IntVector jarg2_);
  public final static native long VidConfPortInfo_transmitters_get(long jarg1, VidConfPortInfo jarg1_);
  public final static native long new_VidConfPortInfo();
  public final static native void delete_VidConfPortInfo(long jarg1);
  public final static native long new_VideoMediaTransmitParam();
  public final static native void delete_VideoMediaTransmitParam(long jarg1);
  public final static native long VideoMedia_getPortInfo(long jarg1, VideoMedia jarg1_) throws java.lang.Exception;
  public final static native int VideoMedia_getPortId(long jarg1, VideoMedia jarg1_);
  public final static native long VideoMedia_getPortInfoFromId(int jarg1) throws java.lang.Exception;
  public final static native void VideoMedia_startTransmit(long jarg1, VideoMedia jarg1_, long jarg2, VideoMedia jarg2_, long jarg3, VideoMediaTransmitParam jarg3_) throws java.lang.Exception;
  public final static native void VideoMedia_stopTransmit(long jarg1, VideoMedia jarg1_, long jarg2, VideoMedia jarg2_) throws java.lang.Exception;
  public final static native void VideoMedia_update(long jarg1, VideoMedia jarg1_) throws java.lang.Exception;
  public final static native long new_VideoMedia();
  public final static native void delete_VideoMedia(long jarg1);
  public final static native void WindowHandle_setWindow(long jarg1, WindowHandle jarg1_, long jarg2);
  public final static native long new_WindowHandle();
  public final static native void delete_WindowHandle(long jarg1);
  public final static native void VideoWindowHandle_type_set(long jarg1, VideoWindowHandle jarg1_, long jarg2);
  public final static native long VideoWindowHandle_type_get(long jarg1, VideoWindowHandle jarg1_);
  public final static native void VideoWindowHandle_handle_set(long jarg1, VideoWindowHandle jarg1_, long jarg2, WindowHandle jarg2_);
  public final static native long VideoWindowHandle_handle_get(long jarg1, VideoWindowHandle jarg1_);
  public final static native long new_VideoWindowHandle();
  public final static native void delete_VideoWindowHandle(long jarg1);
  public final static native void VideoWindowInfo_isNative_set(long jarg1, VideoWindowInfo jarg1_, boolean jarg2);
  public final static native boolean VideoWindowInfo_isNative_get(long jarg1, VideoWindowInfo jarg1_);
  public final static native void VideoWindowInfo_winHandle_set(long jarg1, VideoWindowInfo jarg1_, long jarg2, VideoWindowHandle jarg2_);
  public final static native long VideoWindowInfo_winHandle_get(long jarg1, VideoWindowInfo jarg1_);
  public final static native void VideoWindowInfo_renderDeviceId_set(long jarg1, VideoWindowInfo jarg1_, int jarg2);
  public final static native int VideoWindowInfo_renderDeviceId_get(long jarg1, VideoWindowInfo jarg1_);
  public final static native void VideoWindowInfo_show_set(long jarg1, VideoWindowInfo jarg1_, boolean jarg2);
  public final static native boolean VideoWindowInfo_show_get(long jarg1, VideoWindowInfo jarg1_);
  public final static native void VideoWindowInfo_pos_set(long jarg1, VideoWindowInfo jarg1_, long jarg2, MediaCoordinate jarg2_);
  public final static native long VideoWindowInfo_pos_get(long jarg1, VideoWindowInfo jarg1_);
  public final static native void VideoWindowInfo_size_set(long jarg1, VideoWindowInfo jarg1_, long jarg2, MediaSize jarg2_);
  public final static native long VideoWindowInfo_size_get(long jarg1, VideoWindowInfo jarg1_);
  public final static native long new_VideoWindowInfo();
  public final static native void delete_VideoWindowInfo(long jarg1);
  public final static native long new_VideoWindow(int jarg1);
  public final static native long VideoWindow_getInfo(long jarg1, VideoWindow jarg1_) throws java.lang.Exception;
  public final static native long VideoWindow_getVideoMedia(long jarg1, VideoWindow jarg1_) throws java.lang.Exception;
  public final static native void VideoWindow_Show(long jarg1, VideoWindow jarg1_, boolean jarg2) throws java.lang.Exception;
  public final static native void VideoWindow_setPos(long jarg1, VideoWindow jarg1_, long jarg2, MediaCoordinate jarg2_) throws java.lang.Exception;
  public final static native void VideoWindow_setSize(long jarg1, VideoWindow jarg1_, long jarg2, MediaSize jarg2_) throws java.lang.Exception;
  public final static native void VideoWindow_rotate(long jarg1, VideoWindow jarg1_, int jarg2) throws java.lang.Exception;
  public final static native void VideoWindow_setWindow(long jarg1, VideoWindow jarg1_, long jarg2, VideoWindowHandle jarg2_) throws java.lang.Exception;
  public final static native void VideoWindow_setFullScreen(long jarg1, VideoWindow jarg1_, boolean jarg2) throws java.lang.Exception;
  public final static native void VideoWindow_setFullScreen2(long jarg1, VideoWindow jarg1_, int jarg2) throws java.lang.Exception;
  public final static native void delete_VideoWindow(long jarg1);
  public final static native void VideoPreviewOpParam_rendId_set(long jarg1, VideoPreviewOpParam jarg1_, int jarg2);
  public final static native int VideoPreviewOpParam_rendId_get(long jarg1, VideoPreviewOpParam jarg1_);
  public final static native void VideoPreviewOpParam_show_set(long jarg1, VideoPreviewOpParam jarg1_, boolean jarg2);
  public final static native boolean VideoPreviewOpParam_show_get(long jarg1, VideoPreviewOpParam jarg1_);
  public final static native void VideoPreviewOpParam_windowFlags_set(long jarg1, VideoPreviewOpParam jarg1_, long jarg2);
  public final static native long VideoPreviewOpParam_windowFlags_get(long jarg1, VideoPreviewOpParam jarg1_);
  public final static native void VideoPreviewOpParam_format_set(long jarg1, VideoPreviewOpParam jarg1_, long jarg2, MediaFormat jarg2_);
  public final static native long VideoPreviewOpParam_format_get(long jarg1, VideoPreviewOpParam jarg1_);
  public final static native void VideoPreviewOpParam_window_set(long jarg1, VideoPreviewOpParam jarg1_, long jarg2, VideoWindowHandle jarg2_);
  public final static native long VideoPreviewOpParam_window_get(long jarg1, VideoPreviewOpParam jarg1_);
  public final static native long new_VideoPreviewOpParam();
  public final static native void delete_VideoPreviewOpParam(long jarg1);
  public final static native long new_VideoPreview(int jarg1);
  public final static native boolean VideoPreview_hasNative(long jarg1, VideoPreview jarg1_);
  public final static native void VideoPreview_start(long jarg1, VideoPreview jarg1_, long jarg2, VideoPreviewOpParam jarg2_) throws java.lang.Exception;
  public final static native void VideoPreview_stop(long jarg1, VideoPreview jarg1_) throws java.lang.Exception;
  public final static native long VideoPreview_getVideoWindow(long jarg1, VideoPreview jarg1_);
  public final static native long VideoPreview_getVideoMedia(long jarg1, VideoPreview jarg1_) throws java.lang.Exception;
  public final static native void delete_VideoPreview(long jarg1);
  public final static native void VideoDevInfo_id_set(long jarg1, VideoDevInfo jarg1_, int jarg2);
  public final static native int VideoDevInfo_id_get(long jarg1, VideoDevInfo jarg1_);
  public final static native void VideoDevInfo_name_set(long jarg1, VideoDevInfo jarg1_, String jarg2);
  public final static native String VideoDevInfo_name_get(long jarg1, VideoDevInfo jarg1_);
  public final static native void VideoDevInfo_driver_set(long jarg1, VideoDevInfo jarg1_, String jarg2);
  public final static native String VideoDevInfo_driver_get(long jarg1, VideoDevInfo jarg1_);
  public final static native void VideoDevInfo_dir_set(long jarg1, VideoDevInfo jarg1_, int jarg2);
  public final static native int VideoDevInfo_dir_get(long jarg1, VideoDevInfo jarg1_);
  public final static native void VideoDevInfo_caps_set(long jarg1, VideoDevInfo jarg1_, long jarg2);
  public final static native long VideoDevInfo_caps_get(long jarg1, VideoDevInfo jarg1_);
  public final static native void VideoDevInfo_fmt_set(long jarg1, VideoDevInfo jarg1_, long jarg2, MediaFormatVideoVector jarg2_);
  public final static native long VideoDevInfo_fmt_get(long jarg1, VideoDevInfo jarg1_);
  public final static native long new_VideoDevInfo();
  public final static native void delete_VideoDevInfo(long jarg1);
  public final static native void VideoSwitchParam_target_id_set(long jarg1, VideoSwitchParam jarg1_, int jarg2);
  public final static native int VideoSwitchParam_target_id_get(long jarg1, VideoSwitchParam jarg1_);
  public final static native long new_VideoSwitchParam();
  public final static native void delete_VideoSwitchParam(long jarg1);
  public final static native void VidDevManager_initSubsys(long jarg1, VidDevManager jarg1_) throws java.lang.Exception;
  public final static native void VidDevManager_refreshDevs(long jarg1, VidDevManager jarg1_) throws java.lang.Exception;
  public final static native long VidDevManager_getDevCount(long jarg1, VidDevManager jarg1_);
  public final static native long VidDevManager_getDevInfo(long jarg1, VidDevManager jarg1_, int jarg2) throws java.lang.Exception;
  public final static native long VidDevManager_enumDev2(long jarg1, VidDevManager jarg1_) throws java.lang.Exception;
  public final static native int VidDevManager_lookupDev(long jarg1, VidDevManager jarg1_, String jarg2, String jarg3) throws java.lang.Exception;
  public final static native String VidDevManager_capName(long jarg1, VidDevManager jarg1_, int jarg2);
  public final static native void VidDevManager_setFormat(long jarg1, VidDevManager jarg1_, int jarg2, long jarg3, MediaFormatVideo jarg3_, boolean jarg4) throws java.lang.Exception;
  public final static native long VidDevManager_getFormat(long jarg1, VidDevManager jarg1_, int jarg2) throws java.lang.Exception;
  public final static native void VidDevManager_setInputScale(long jarg1, VidDevManager jarg1_, int jarg2, long jarg3, MediaSize jarg3_, boolean jarg4) throws java.lang.Exception;
  public final static native long VidDevManager_getInputScale(long jarg1, VidDevManager jarg1_, int jarg2) throws java.lang.Exception;
  public final static native void VidDevManager_setOutputWindowFlags(long jarg1, VidDevManager jarg1_, int jarg2, int jarg3, boolean jarg4) throws java.lang.Exception;
  public final static native int VidDevManager_getOutputWindowFlags(long jarg1, VidDevManager jarg1_, int jarg2) throws java.lang.Exception;
  public final static native void VidDevManager_switchDev(long jarg1, VidDevManager jarg1_, int jarg2, long jarg3, VideoSwitchParam jarg3_) throws java.lang.Exception;
  public final static native boolean VidDevManager_isCaptureActive(long jarg1, VidDevManager jarg1_, int jarg2);
  public final static native void VidDevManager_setCaptureOrient__SWIG_0(long jarg1, VidDevManager jarg1_, int jarg2, int jarg3, boolean jarg4) throws java.lang.Exception;
  public final static native void VidDevManager_setCaptureOrient__SWIG_1(long jarg1, VidDevManager jarg1_, int jarg2, int jarg3) throws java.lang.Exception;
  public final static native void CodecInfo_codecId_set(long jarg1, CodecInfo jarg1_, String jarg2);
  public final static native String CodecInfo_codecId_get(long jarg1, CodecInfo jarg1_);
  public final static native void CodecInfo_priority_set(long jarg1, CodecInfo jarg1_, short jarg2);
  public final static native short CodecInfo_priority_get(long jarg1, CodecInfo jarg1_);
  public final static native void CodecInfo_desc_set(long jarg1, CodecInfo jarg1_, String jarg2);
  public final static native String CodecInfo_desc_get(long jarg1, CodecInfo jarg1_);
  public final static native long new_CodecInfo();
  public final static native void delete_CodecInfo(long jarg1);
  public final static native void CodecFmtp_name_set(long jarg1, CodecFmtp jarg1_, String jarg2);
  public final static native String CodecFmtp_name_get(long jarg1, CodecFmtp jarg1_);
  public final static native void CodecFmtp_val_set(long jarg1, CodecFmtp jarg1_, String jarg2);
  public final static native String CodecFmtp_val_get(long jarg1, CodecFmtp jarg1_);
  public final static native long new_CodecFmtp();
  public final static native void delete_CodecFmtp(long jarg1);
  public final static native void CodecParamInfo_clockRate_set(long jarg1, CodecParamInfo jarg1_, long jarg2);
  public final static native long CodecParamInfo_clockRate_get(long jarg1, CodecParamInfo jarg1_);
  public final static native void CodecParamInfo_channelCnt_set(long jarg1, CodecParamInfo jarg1_, long jarg2);
  public final static native long CodecParamInfo_channelCnt_get(long jarg1, CodecParamInfo jarg1_);
  public final static native void CodecParamInfo_avgBps_set(long jarg1, CodecParamInfo jarg1_, long jarg2);
  public final static native long CodecParamInfo_avgBps_get(long jarg1, CodecParamInfo jarg1_);
  public final static native void CodecParamInfo_maxBps_set(long jarg1, CodecParamInfo jarg1_, long jarg2);
  public final static native long CodecParamInfo_maxBps_get(long jarg1, CodecParamInfo jarg1_);
  public final static native void CodecParamInfo_maxRxFrameSize_set(long jarg1, CodecParamInfo jarg1_, long jarg2);
  public final static native long CodecParamInfo_maxRxFrameSize_get(long jarg1, CodecParamInfo jarg1_);
  public final static native void CodecParamInfo_frameLen_set(long jarg1, CodecParamInfo jarg1_, long jarg2);
  public final static native long CodecParamInfo_frameLen_get(long jarg1, CodecParamInfo jarg1_);
  public final static native void CodecParamInfo_encFrameLen_set(long jarg1, CodecParamInfo jarg1_, long jarg2);
  public final static native long CodecParamInfo_encFrameLen_get(long jarg1, CodecParamInfo jarg1_);
  public final static native void CodecParamInfo_pcmBitsPerSample_set(long jarg1, CodecParamInfo jarg1_, long jarg2);
  public final static native long CodecParamInfo_pcmBitsPerSample_get(long jarg1, CodecParamInfo jarg1_);
  public final static native void CodecParamInfo_pt_set(long jarg1, CodecParamInfo jarg1_, long jarg2);
  public final static native long CodecParamInfo_pt_get(long jarg1, CodecParamInfo jarg1_);
  public final static native void CodecParamInfo_fmtId_set(long jarg1, CodecParamInfo jarg1_, int jarg2);
  public final static native int CodecParamInfo_fmtId_get(long jarg1, CodecParamInfo jarg1_);
  public final static native long new_CodecParamInfo();
  public final static native void delete_CodecParamInfo(long jarg1);
  public final static native void CodecParamSetting_frmPerPkt_set(long jarg1, CodecParamSetting jarg1_, long jarg2);
  public final static native long CodecParamSetting_frmPerPkt_get(long jarg1, CodecParamSetting jarg1_);
  public final static native void CodecParamSetting_vad_set(long jarg1, CodecParamSetting jarg1_, boolean jarg2);
  public final static native boolean CodecParamSetting_vad_get(long jarg1, CodecParamSetting jarg1_);
  public final static native void CodecParamSetting_cng_set(long jarg1, CodecParamSetting jarg1_, boolean jarg2);
  public final static native boolean CodecParamSetting_cng_get(long jarg1, CodecParamSetting jarg1_);
  public final static native void CodecParamSetting_penh_set(long jarg1, CodecParamSetting jarg1_, boolean jarg2);
  public final static native boolean CodecParamSetting_penh_get(long jarg1, CodecParamSetting jarg1_);
  public final static native void CodecParamSetting_plc_set(long jarg1, CodecParamSetting jarg1_, boolean jarg2);
  public final static native boolean CodecParamSetting_plc_get(long jarg1, CodecParamSetting jarg1_);
  public final static native void CodecParamSetting_reserved_set(long jarg1, CodecParamSetting jarg1_, boolean jarg2);
  public final static native boolean CodecParamSetting_reserved_get(long jarg1, CodecParamSetting jarg1_);
  public final static native void CodecParamSetting_encFmtp_set(long jarg1, CodecParamSetting jarg1_, long jarg2, CodecFmtpVector jarg2_);
  public final static native long CodecParamSetting_encFmtp_get(long jarg1, CodecParamSetting jarg1_);
  public final static native void CodecParamSetting_decFmtp_set(long jarg1, CodecParamSetting jarg1_, long jarg2, CodecFmtpVector jarg2_);
  public final static native long CodecParamSetting_decFmtp_get(long jarg1, CodecParamSetting jarg1_);
  public final static native void CodecParamSetting_packetLoss_set(long jarg1, CodecParamSetting jarg1_, long jarg2);
  public final static native long CodecParamSetting_packetLoss_get(long jarg1, CodecParamSetting jarg1_);
  public final static native void CodecParamSetting_complexity_set(long jarg1, CodecParamSetting jarg1_, long jarg2);
  public final static native long CodecParamSetting_complexity_get(long jarg1, CodecParamSetting jarg1_);
  public final static native void CodecParamSetting_cbr_set(long jarg1, CodecParamSetting jarg1_, boolean jarg2);
  public final static native boolean CodecParamSetting_cbr_get(long jarg1, CodecParamSetting jarg1_);
  public final static native long new_CodecParamSetting();
  public final static native void delete_CodecParamSetting(long jarg1);
  public final static native void CodecParam_info_set(long jarg1, CodecParam jarg1_, long jarg2, CodecParamInfo jarg2_);
  public final static native long CodecParam_info_get(long jarg1, CodecParam jarg1_);
  public final static native void CodecParam_setting_set(long jarg1, CodecParam jarg1_, long jarg2, CodecParamSetting jarg2_);
  public final static native long CodecParam_setting_get(long jarg1, CodecParam jarg1_);
  public final static native long new_CodecParam();
  public final static native void delete_CodecParam(long jarg1);
  public final static native void CodecOpusConfig_sample_rate_set(long jarg1, CodecOpusConfig jarg1_, long jarg2);
  public final static native long CodecOpusConfig_sample_rate_get(long jarg1, CodecOpusConfig jarg1_);
  public final static native void CodecOpusConfig_channel_cnt_set(long jarg1, CodecOpusConfig jarg1_, long jarg2);
  public final static native long CodecOpusConfig_channel_cnt_get(long jarg1, CodecOpusConfig jarg1_);
  public final static native void CodecOpusConfig_frm_ptime_set(long jarg1, CodecOpusConfig jarg1_, long jarg2);
  public final static native long CodecOpusConfig_frm_ptime_get(long jarg1, CodecOpusConfig jarg1_);
  public final static native void CodecOpusConfig_bit_rate_set(long jarg1, CodecOpusConfig jarg1_, long jarg2);
  public final static native long CodecOpusConfig_bit_rate_get(long jarg1, CodecOpusConfig jarg1_);
  public final static native void CodecOpusConfig_packet_loss_set(long jarg1, CodecOpusConfig jarg1_, long jarg2);
  public final static native long CodecOpusConfig_packet_loss_get(long jarg1, CodecOpusConfig jarg1_);
  public final static native void CodecOpusConfig_complexity_set(long jarg1, CodecOpusConfig jarg1_, long jarg2);
  public final static native long CodecOpusConfig_complexity_get(long jarg1, CodecOpusConfig jarg1_);
  public final static native void CodecOpusConfig_cbr_set(long jarg1, CodecOpusConfig jarg1_, boolean jarg2);
  public final static native boolean CodecOpusConfig_cbr_get(long jarg1, CodecOpusConfig jarg1_);
  public final static native long new_CodecOpusConfig();
  public final static native void delete_CodecOpusConfig(long jarg1);
  public final static native void VidCodecParam_dir_set(long jarg1, VidCodecParam jarg1_, int jarg2);
  public final static native int VidCodecParam_dir_get(long jarg1, VidCodecParam jarg1_);
  public final static native void VidCodecParam_packing_set(long jarg1, VidCodecParam jarg1_, int jarg2);
  public final static native int VidCodecParam_packing_get(long jarg1, VidCodecParam jarg1_);
  public final static native void VidCodecParam_encFmt_set(long jarg1, VidCodecParam jarg1_, long jarg2, MediaFormatVideo jarg2_);
  public final static native long VidCodecParam_encFmt_get(long jarg1, VidCodecParam jarg1_);
  public final static native void VidCodecParam_encFmtp_set(long jarg1, VidCodecParam jarg1_, long jarg2, CodecFmtpVector jarg2_);
  public final static native long VidCodecParam_encFmtp_get(long jarg1, VidCodecParam jarg1_);
  public final static native void VidCodecParam_encMtu_set(long jarg1, VidCodecParam jarg1_, long jarg2);
  public final static native long VidCodecParam_encMtu_get(long jarg1, VidCodecParam jarg1_);
  public final static native void VidCodecParam_decFmt_set(long jarg1, VidCodecParam jarg1_, long jarg2, MediaFormatVideo jarg2_);
  public final static native long VidCodecParam_decFmt_get(long jarg1, VidCodecParam jarg1_);
  public final static native void VidCodecParam_decFmtp_set(long jarg1, VidCodecParam jarg1_, long jarg2, CodecFmtpVector jarg2_);
  public final static native long VidCodecParam_decFmtp_get(long jarg1, VidCodecParam jarg1_);
  public final static native void VidCodecParam_ignoreFmtp_set(long jarg1, VidCodecParam jarg1_, boolean jarg2);
  public final static native boolean VidCodecParam_ignoreFmtp_get(long jarg1, VidCodecParam jarg1_);
  public final static native long new_VidCodecParam();
  public final static native void delete_VidCodecParam(long jarg1);
  public final static native void MediaFmtChangedEvent_newWidth_set(long jarg1, MediaFmtChangedEvent jarg1_, long jarg2);
  public final static native long MediaFmtChangedEvent_newWidth_get(long jarg1, MediaFmtChangedEvent jarg1_);
  public final static native void MediaFmtChangedEvent_newHeight_set(long jarg1, MediaFmtChangedEvent jarg1_, long jarg2);
  public final static native long MediaFmtChangedEvent_newHeight_get(long jarg1, MediaFmtChangedEvent jarg1_);
  public final static native long new_MediaFmtChangedEvent();
  public final static native void delete_MediaFmtChangedEvent(long jarg1);
  public final static native void AudDevErrorEvent_dir_set(long jarg1, AudDevErrorEvent jarg1_, int jarg2);
  public final static native int AudDevErrorEvent_dir_get(long jarg1, AudDevErrorEvent jarg1_);
  public final static native void AudDevErrorEvent_id_set(long jarg1, AudDevErrorEvent jarg1_, int jarg2);
  public final static native int AudDevErrorEvent_id_get(long jarg1, AudDevErrorEvent jarg1_);
  public final static native void AudDevErrorEvent_status_set(long jarg1, AudDevErrorEvent jarg1_, int jarg2);
  public final static native int AudDevErrorEvent_status_get(long jarg1, AudDevErrorEvent jarg1_);
  public final static native long new_AudDevErrorEvent();
  public final static native void delete_AudDevErrorEvent(long jarg1);
  public final static native void MediaEventData_fmtChanged_set(long jarg1, MediaEventData jarg1_, long jarg2, MediaFmtChangedEvent jarg2_);
  public final static native long MediaEventData_fmtChanged_get(long jarg1, MediaEventData jarg1_);
  public final static native void MediaEventData_audDevError_set(long jarg1, MediaEventData jarg1_, long jarg2, AudDevErrorEvent jarg2_);
  public final static native long MediaEventData_audDevError_get(long jarg1, MediaEventData jarg1_);
  public final static native void MediaEventData_ptr_set(long jarg1, MediaEventData jarg1_, long jarg2);
  public final static native long MediaEventData_ptr_get(long jarg1, MediaEventData jarg1_);
  public final static native long new_MediaEventData();
  public final static native void delete_MediaEventData(long jarg1);
  public final static native void MediaEvent_type_set(long jarg1, MediaEvent jarg1_, int jarg2);
  public final static native int MediaEvent_type_get(long jarg1, MediaEvent jarg1_);
  public final static native void MediaEvent_data_set(long jarg1, MediaEvent jarg1_, long jarg2, MediaEventData jarg2_);
  public final static native long MediaEvent_data_get(long jarg1, MediaEvent jarg1_);
  public final static native void MediaEvent_pjMediaEvent_set(long jarg1, MediaEvent jarg1_, long jarg2);
  public final static native long MediaEvent_pjMediaEvent_get(long jarg1, MediaEvent jarg1_);
  public final static native long new_MediaEvent();
  public final static native void delete_MediaEvent(long jarg1);
  public final static native void PresenceStatus_status_set(long jarg1, PresenceStatus jarg1_, int jarg2);
  public final static native int PresenceStatus_status_get(long jarg1, PresenceStatus jarg1_);
  public final static native void PresenceStatus_statusText_set(long jarg1, PresenceStatus jarg1_, String jarg2);
  public final static native String PresenceStatus_statusText_get(long jarg1, PresenceStatus jarg1_);
  public final static native void PresenceStatus_activity_set(long jarg1, PresenceStatus jarg1_, int jarg2);
  public final static native int PresenceStatus_activity_get(long jarg1, PresenceStatus jarg1_);
  public final static native void PresenceStatus_note_set(long jarg1, PresenceStatus jarg1_, String jarg2);
  public final static native String PresenceStatus_note_get(long jarg1, PresenceStatus jarg1_);
  public final static native void PresenceStatus_rpidId_set(long jarg1, PresenceStatus jarg1_, String jarg2);
  public final static native String PresenceStatus_rpidId_get(long jarg1, PresenceStatus jarg1_);
  public final static native long new_PresenceStatus();
  public final static native void delete_PresenceStatus(long jarg1);
  public final static native void BuddyConfig_uri_set(long jarg1, BuddyConfig jarg1_, String jarg2);
  public final static native String BuddyConfig_uri_get(long jarg1, BuddyConfig jarg1_);
  public final static native void BuddyConfig_subscribe_set(long jarg1, BuddyConfig jarg1_, boolean jarg2);
  public final static native boolean BuddyConfig_subscribe_get(long jarg1, BuddyConfig jarg1_);
  public final static native void BuddyConfig_readObject(long jarg1, BuddyConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void BuddyConfig_writeObject(long jarg1, BuddyConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native long new_BuddyConfig();
  public final static native void delete_BuddyConfig(long jarg1);
  public final static native void BuddyInfo_uri_set(long jarg1, BuddyInfo jarg1_, String jarg2);
  public final static native String BuddyInfo_uri_get(long jarg1, BuddyInfo jarg1_);
  public final static native void BuddyInfo_contact_set(long jarg1, BuddyInfo jarg1_, String jarg2);
  public final static native String BuddyInfo_contact_get(long jarg1, BuddyInfo jarg1_);
  public final static native void BuddyInfo_presMonitorEnabled_set(long jarg1, BuddyInfo jarg1_, boolean jarg2);
  public final static native boolean BuddyInfo_presMonitorEnabled_get(long jarg1, BuddyInfo jarg1_);
  public final static native void BuddyInfo_subState_set(long jarg1, BuddyInfo jarg1_, int jarg2);
  public final static native int BuddyInfo_subState_get(long jarg1, BuddyInfo jarg1_);
  public final static native void BuddyInfo_subStateName_set(long jarg1, BuddyInfo jarg1_, String jarg2);
  public final static native String BuddyInfo_subStateName_get(long jarg1, BuddyInfo jarg1_);
  public final static native void BuddyInfo_subTermCode_set(long jarg1, BuddyInfo jarg1_, int jarg2);
  public final static native int BuddyInfo_subTermCode_get(long jarg1, BuddyInfo jarg1_);
  public final static native void BuddyInfo_subTermReason_set(long jarg1, BuddyInfo jarg1_, String jarg2);
  public final static native String BuddyInfo_subTermReason_get(long jarg1, BuddyInfo jarg1_);
  public final static native void BuddyInfo_presStatus_set(long jarg1, BuddyInfo jarg1_, long jarg2, PresenceStatus jarg2_);
  public final static native long BuddyInfo_presStatus_get(long jarg1, BuddyInfo jarg1_);
  public final static native long new_BuddyInfo();
  public final static native void delete_BuddyInfo(long jarg1);
  public final static native void OnBuddyEvSubStateParam_e_set(long jarg1, OnBuddyEvSubStateParam jarg1_, long jarg2, SipEvent jarg2_);
  public final static native long OnBuddyEvSubStateParam_e_get(long jarg1, OnBuddyEvSubStateParam jarg1_);
  public final static native long new_OnBuddyEvSubStateParam();
  public final static native void delete_OnBuddyEvSubStateParam(long jarg1);
  public final static native long new_Buddy();
  public final static native void delete_Buddy(long jarg1);
  public final static native void Buddy_create(long jarg1, Buddy jarg1_, long jarg2, Account jarg2_, long jarg3, BuddyConfig jarg3_) throws java.lang.Exception;
  public final static native boolean Buddy_isValid(long jarg1, Buddy jarg1_);
  public final static native int Buddy_getId(long jarg1, Buddy jarg1_);
  public final static native long Buddy_getInfo(long jarg1, Buddy jarg1_) throws java.lang.Exception;
  public final static native void Buddy_subscribePresence(long jarg1, Buddy jarg1_, boolean jarg2) throws java.lang.Exception;
  public final static native void Buddy_updatePresence(long jarg1, Buddy jarg1_) throws java.lang.Exception;
  public final static native void Buddy_sendInstantMessage(long jarg1, Buddy jarg1_, long jarg2, SendInstantMessageParam jarg2_) throws java.lang.Exception;
  public final static native void Buddy_sendTypingIndication(long jarg1, Buddy jarg1_, long jarg2, SendTypingIndicationParam jarg2_) throws java.lang.Exception;
  public final static native void Buddy_onBuddyState(long jarg1, Buddy jarg1_);
  public final static native void Buddy_onBuddyStateSwigExplicitBuddy(long jarg1, Buddy jarg1_);
  public final static native void Buddy_onBuddyEvSubState(long jarg1, Buddy jarg1_, long jarg2, OnBuddyEvSubStateParam jarg2_);
  public final static native void Buddy_onBuddyEvSubStateSwigExplicitBuddy(long jarg1, Buddy jarg1_, long jarg2, OnBuddyEvSubStateParam jarg2_);
  public final static native void Buddy_director_connect(Buddy obj, long cptr, boolean mem_own, boolean weak_global);
  public final static native void Buddy_change_ownership(Buddy obj, long cptr, boolean take_or_release);
  public final static native void AccountRegConfig_registrarUri_set(long jarg1, AccountRegConfig jarg1_, String jarg2);
  public final static native String AccountRegConfig_registrarUri_get(long jarg1, AccountRegConfig jarg1_);
  public final static native void AccountRegConfig_registerOnAdd_set(long jarg1, AccountRegConfig jarg1_, boolean jarg2);
  public final static native boolean AccountRegConfig_registerOnAdd_get(long jarg1, AccountRegConfig jarg1_);
  public final static native void AccountRegConfig_headers_set(long jarg1, AccountRegConfig jarg1_, long jarg2, SipHeaderVector jarg2_);
  public final static native long AccountRegConfig_headers_get(long jarg1, AccountRegConfig jarg1_);
  public final static native void AccountRegConfig_contactParams_set(long jarg1, AccountRegConfig jarg1_, String jarg2);
  public final static native String AccountRegConfig_contactParams_get(long jarg1, AccountRegConfig jarg1_);
  public final static native void AccountRegConfig_contactUriParams_set(long jarg1, AccountRegConfig jarg1_, String jarg2);
  public final static native String AccountRegConfig_contactUriParams_get(long jarg1, AccountRegConfig jarg1_);
  public final static native void AccountRegConfig_timeoutSec_set(long jarg1, AccountRegConfig jarg1_, long jarg2);
  public final static native long AccountRegConfig_timeoutSec_get(long jarg1, AccountRegConfig jarg1_);
  public final static native void AccountRegConfig_retryIntervalSec_set(long jarg1, AccountRegConfig jarg1_, long jarg2);
  public final static native long AccountRegConfig_retryIntervalSec_get(long jarg1, AccountRegConfig jarg1_);
  public final static native void AccountRegConfig_firstRetryIntervalSec_set(long jarg1, AccountRegConfig jarg1_, long jarg2);
  public final static native long AccountRegConfig_firstRetryIntervalSec_get(long jarg1, AccountRegConfig jarg1_);
  public final static native void AccountRegConfig_randomRetryIntervalSec_set(long jarg1, AccountRegConfig jarg1_, long jarg2);
  public final static native long AccountRegConfig_randomRetryIntervalSec_get(long jarg1, AccountRegConfig jarg1_);
  public final static native void AccountRegConfig_delayBeforeRefreshSec_set(long jarg1, AccountRegConfig jarg1_, long jarg2);
  public final static native long AccountRegConfig_delayBeforeRefreshSec_get(long jarg1, AccountRegConfig jarg1_);
  public final static native void AccountRegConfig_dropCallsOnFail_set(long jarg1, AccountRegConfig jarg1_, boolean jarg2);
  public final static native boolean AccountRegConfig_dropCallsOnFail_get(long jarg1, AccountRegConfig jarg1_);
  public final static native void AccountRegConfig_unregWaitMsec_set(long jarg1, AccountRegConfig jarg1_, long jarg2);
  public final static native long AccountRegConfig_unregWaitMsec_get(long jarg1, AccountRegConfig jarg1_);
  public final static native void AccountRegConfig_proxyUse_set(long jarg1, AccountRegConfig jarg1_, long jarg2);
  public final static native long AccountRegConfig_proxyUse_get(long jarg1, AccountRegConfig jarg1_);
  public final static native void AccountRegConfig_readObject(long jarg1, AccountRegConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void AccountRegConfig_writeObject(long jarg1, AccountRegConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native long new_AccountRegConfig();
  public final static native void delete_AccountRegConfig(long jarg1);
  public final static native void AccountSipConfig_authCreds_set(long jarg1, AccountSipConfig jarg1_, long jarg2, AuthCredInfoVector jarg2_);
  public final static native long AccountSipConfig_authCreds_get(long jarg1, AccountSipConfig jarg1_);
  public final static native void AccountSipConfig_proxies_set(long jarg1, AccountSipConfig jarg1_, long jarg2, StringVector jarg2_);
  public final static native long AccountSipConfig_proxies_get(long jarg1, AccountSipConfig jarg1_);
  public final static native void AccountSipConfig_contactForced_set(long jarg1, AccountSipConfig jarg1_, String jarg2);
  public final static native String AccountSipConfig_contactForced_get(long jarg1, AccountSipConfig jarg1_);
  public final static native void AccountSipConfig_contactParams_set(long jarg1, AccountSipConfig jarg1_, String jarg2);
  public final static native String AccountSipConfig_contactParams_get(long jarg1, AccountSipConfig jarg1_);
  public final static native void AccountSipConfig_contactUriParams_set(long jarg1, AccountSipConfig jarg1_, String jarg2);
  public final static native String AccountSipConfig_contactUriParams_get(long jarg1, AccountSipConfig jarg1_);
  public final static native void AccountSipConfig_authInitialEmpty_set(long jarg1, AccountSipConfig jarg1_, boolean jarg2);
  public final static native boolean AccountSipConfig_authInitialEmpty_get(long jarg1, AccountSipConfig jarg1_);
  public final static native void AccountSipConfig_authInitialAlgorithm_set(long jarg1, AccountSipConfig jarg1_, String jarg2);
  public final static native String AccountSipConfig_authInitialAlgorithm_get(long jarg1, AccountSipConfig jarg1_);
  public final static native void AccountSipConfig_transportId_set(long jarg1, AccountSipConfig jarg1_, int jarg2);
  public final static native int AccountSipConfig_transportId_get(long jarg1, AccountSipConfig jarg1_);
  public final static native void AccountSipConfig_readObject(long jarg1, AccountSipConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void AccountSipConfig_writeObject(long jarg1, AccountSipConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native long new_AccountSipConfig();
  public final static native void delete_AccountSipConfig(long jarg1);
  public final static native void AccountCallConfig_holdType_set(long jarg1, AccountCallConfig jarg1_, int jarg2);
  public final static native int AccountCallConfig_holdType_get(long jarg1, AccountCallConfig jarg1_);
  public final static native void AccountCallConfig_prackUse_set(long jarg1, AccountCallConfig jarg1_, int jarg2);
  public final static native int AccountCallConfig_prackUse_get(long jarg1, AccountCallConfig jarg1_);
  public final static native void AccountCallConfig_timerUse_set(long jarg1, AccountCallConfig jarg1_, int jarg2);
  public final static native int AccountCallConfig_timerUse_get(long jarg1, AccountCallConfig jarg1_);
  public final static native void AccountCallConfig_timerMinSESec_set(long jarg1, AccountCallConfig jarg1_, long jarg2);
  public final static native long AccountCallConfig_timerMinSESec_get(long jarg1, AccountCallConfig jarg1_);
  public final static native void AccountCallConfig_timerSessExpiresSec_set(long jarg1, AccountCallConfig jarg1_, long jarg2);
  public final static native long AccountCallConfig_timerSessExpiresSec_get(long jarg1, AccountCallConfig jarg1_);
  public final static native long new_AccountCallConfig();
  public final static native void AccountCallConfig_readObject(long jarg1, AccountCallConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void AccountCallConfig_writeObject(long jarg1, AccountCallConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void delete_AccountCallConfig(long jarg1);
  public final static native void AccountPresConfig_headers_set(long jarg1, AccountPresConfig jarg1_, long jarg2, SipHeaderVector jarg2_);
  public final static native long AccountPresConfig_headers_get(long jarg1, AccountPresConfig jarg1_);
  public final static native void AccountPresConfig_publishEnabled_set(long jarg1, AccountPresConfig jarg1_, boolean jarg2);
  public final static native boolean AccountPresConfig_publishEnabled_get(long jarg1, AccountPresConfig jarg1_);
  public final static native void AccountPresConfig_publishQueue_set(long jarg1, AccountPresConfig jarg1_, boolean jarg2);
  public final static native boolean AccountPresConfig_publishQueue_get(long jarg1, AccountPresConfig jarg1_);
  public final static native void AccountPresConfig_publishShutdownWaitMsec_set(long jarg1, AccountPresConfig jarg1_, long jarg2);
  public final static native long AccountPresConfig_publishShutdownWaitMsec_get(long jarg1, AccountPresConfig jarg1_);
  public final static native void AccountPresConfig_pidfTupleId_set(long jarg1, AccountPresConfig jarg1_, String jarg2);
  public final static native String AccountPresConfig_pidfTupleId_get(long jarg1, AccountPresConfig jarg1_);
  public final static native void AccountPresConfig_readObject(long jarg1, AccountPresConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void AccountPresConfig_writeObject(long jarg1, AccountPresConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native long new_AccountPresConfig();
  public final static native void delete_AccountPresConfig(long jarg1);
  public final static native void AccountMwiConfig_enabled_set(long jarg1, AccountMwiConfig jarg1_, boolean jarg2);
  public final static native boolean AccountMwiConfig_enabled_get(long jarg1, AccountMwiConfig jarg1_);
  public final static native void AccountMwiConfig_expirationSec_set(long jarg1, AccountMwiConfig jarg1_, long jarg2);
  public final static native long AccountMwiConfig_expirationSec_get(long jarg1, AccountMwiConfig jarg1_);
  public final static native void AccountMwiConfig_readObject(long jarg1, AccountMwiConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void AccountMwiConfig_writeObject(long jarg1, AccountMwiConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native long new_AccountMwiConfig();
  public final static native void delete_AccountMwiConfig(long jarg1);
  public final static native void AccountNatConfig_sipStunUse_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_sipStunUse_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_mediaStunUse_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_mediaStunUse_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_sipUpnpUse_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_sipUpnpUse_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_mediaUpnpUse_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_mediaUpnpUse_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_nat64Opt_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_nat64Opt_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_iceEnabled_set(long jarg1, AccountNatConfig jarg1_, boolean jarg2);
  public final static native boolean AccountNatConfig_iceEnabled_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_iceTrickle_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_iceTrickle_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_iceMaxHostCands_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_iceMaxHostCands_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_iceAggressiveNomination_set(long jarg1, AccountNatConfig jarg1_, boolean jarg2);
  public final static native boolean AccountNatConfig_iceAggressiveNomination_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_iceNominatedCheckDelayMsec_set(long jarg1, AccountNatConfig jarg1_, long jarg2);
  public final static native long AccountNatConfig_iceNominatedCheckDelayMsec_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_iceWaitNominationTimeoutMsec_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_iceWaitNominationTimeoutMsec_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_iceNoRtcp_set(long jarg1, AccountNatConfig jarg1_, boolean jarg2);
  public final static native boolean AccountNatConfig_iceNoRtcp_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_iceAlwaysUpdate_set(long jarg1, AccountNatConfig jarg1_, boolean jarg2);
  public final static native boolean AccountNatConfig_iceAlwaysUpdate_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_turnEnabled_set(long jarg1, AccountNatConfig jarg1_, boolean jarg2);
  public final static native boolean AccountNatConfig_turnEnabled_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_turnServer_set(long jarg1, AccountNatConfig jarg1_, String jarg2);
  public final static native String AccountNatConfig_turnServer_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_turnConnType_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_turnConnType_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_turnUserName_set(long jarg1, AccountNatConfig jarg1_, String jarg2);
  public final static native String AccountNatConfig_turnUserName_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_turnPasswordType_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_turnPasswordType_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_turnPassword_set(long jarg1, AccountNatConfig jarg1_, String jarg2);
  public final static native String AccountNatConfig_turnPassword_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_contactRewriteUse_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_contactRewriteUse_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_contactRewriteMethod_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_contactRewriteMethod_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_contactUseSrcPort_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_contactUseSrcPort_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_viaRewriteUse_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_viaRewriteUse_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_sdpNatRewriteUse_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_sdpNatRewriteUse_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_sipOutboundUse_set(long jarg1, AccountNatConfig jarg1_, int jarg2);
  public final static native int AccountNatConfig_sipOutboundUse_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_sipOutboundInstanceId_set(long jarg1, AccountNatConfig jarg1_, String jarg2);
  public final static native String AccountNatConfig_sipOutboundInstanceId_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_sipOutboundRegId_set(long jarg1, AccountNatConfig jarg1_, String jarg2);
  public final static native String AccountNatConfig_sipOutboundRegId_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_udpKaIntervalSec_set(long jarg1, AccountNatConfig jarg1_, long jarg2);
  public final static native long AccountNatConfig_udpKaIntervalSec_get(long jarg1, AccountNatConfig jarg1_);
  public final static native void AccountNatConfig_udpKaData_set(long jarg1, AccountNatConfig jarg1_, String jarg2);
  public final static native String AccountNatConfig_udpKaData_get(long jarg1, AccountNatConfig jarg1_);
  public final static native long new_AccountNatConfig();
  public final static native void AccountNatConfig_readObject(long jarg1, AccountNatConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void AccountNatConfig_writeObject(long jarg1, AccountNatConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void delete_AccountNatConfig(long jarg1);
  public final static native void SrtpCrypto_key_set(long jarg1, SrtpCrypto jarg1_, String jarg2);
  public final static native String SrtpCrypto_key_get(long jarg1, SrtpCrypto jarg1_);
  public final static native void SrtpCrypto_name_set(long jarg1, SrtpCrypto jarg1_, String jarg2);
  public final static native String SrtpCrypto_name_get(long jarg1, SrtpCrypto jarg1_);
  public final static native void SrtpCrypto_flags_set(long jarg1, SrtpCrypto jarg1_, long jarg2);
  public final static native long SrtpCrypto_flags_get(long jarg1, SrtpCrypto jarg1_);
  public final static native long new_SrtpCrypto();
  public final static native void delete_SrtpCrypto(long jarg1);
  public final static native void SrtpOpt_cryptos_set(long jarg1, SrtpOpt jarg1_, long jarg2, SrtpCryptoVector jarg2_);
  public final static native long SrtpOpt_cryptos_get(long jarg1, SrtpOpt jarg1_);
  public final static native void SrtpOpt_keyings_set(long jarg1, SrtpOpt jarg1_, long jarg2, IntVector jarg2_);
  public final static native long SrtpOpt_keyings_get(long jarg1, SrtpOpt jarg1_);
  public final static native long new_SrtpOpt();
  public final static native void SrtpOpt_readObject(long jarg1, SrtpOpt jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void SrtpOpt_writeObject(long jarg1, SrtpOpt jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void delete_SrtpOpt(long jarg1);
  public final static native void RtcpFbCap_codecId_set(long jarg1, RtcpFbCap jarg1_, String jarg2);
  public final static native String RtcpFbCap_codecId_get(long jarg1, RtcpFbCap jarg1_);
  public final static native void RtcpFbCap_type_set(long jarg1, RtcpFbCap jarg1_, int jarg2);
  public final static native int RtcpFbCap_type_get(long jarg1, RtcpFbCap jarg1_);
  public final static native void RtcpFbCap_typeName_set(long jarg1, RtcpFbCap jarg1_, String jarg2);
  public final static native String RtcpFbCap_typeName_get(long jarg1, RtcpFbCap jarg1_);
  public final static native void RtcpFbCap_param_set(long jarg1, RtcpFbCap jarg1_, String jarg2);
  public final static native String RtcpFbCap_param_get(long jarg1, RtcpFbCap jarg1_);
  public final static native long new_RtcpFbCap();
  public final static native void delete_RtcpFbCap(long jarg1);
  public final static native void RtcpFbConfig_dontUseAvpf_set(long jarg1, RtcpFbConfig jarg1_, boolean jarg2);
  public final static native boolean RtcpFbConfig_dontUseAvpf_get(long jarg1, RtcpFbConfig jarg1_);
  public final static native void RtcpFbConfig_caps_set(long jarg1, RtcpFbConfig jarg1_, long jarg2, RtcpFbCapVector jarg2_);
  public final static native long RtcpFbConfig_caps_get(long jarg1, RtcpFbConfig jarg1_);
  public final static native long new_RtcpFbConfig();
  public final static native void RtcpFbConfig_readObject(long jarg1, RtcpFbConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void RtcpFbConfig_writeObject(long jarg1, RtcpFbConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void delete_RtcpFbConfig(long jarg1);
  public final static native void AccountMediaConfig_transportConfig_set(long jarg1, AccountMediaConfig jarg1_, long jarg2, TransportConfig jarg2_);
  public final static native long AccountMediaConfig_transportConfig_get(long jarg1, AccountMediaConfig jarg1_);
  public final static native void AccountMediaConfig_lockCodecEnabled_set(long jarg1, AccountMediaConfig jarg1_, boolean jarg2);
  public final static native boolean AccountMediaConfig_lockCodecEnabled_get(long jarg1, AccountMediaConfig jarg1_);
  public final static native void AccountMediaConfig_streamKaEnabled_set(long jarg1, AccountMediaConfig jarg1_, boolean jarg2);
  public final static native boolean AccountMediaConfig_streamKaEnabled_get(long jarg1, AccountMediaConfig jarg1_);
  public final static native void AccountMediaConfig_srtpUse_set(long jarg1, AccountMediaConfig jarg1_, int jarg2);
  public final static native int AccountMediaConfig_srtpUse_get(long jarg1, AccountMediaConfig jarg1_);
  public final static native void AccountMediaConfig_srtpSecureSignaling_set(long jarg1, AccountMediaConfig jarg1_, int jarg2);
  public final static native int AccountMediaConfig_srtpSecureSignaling_get(long jarg1, AccountMediaConfig jarg1_);
  public final static native void AccountMediaConfig_srtpOpt_set(long jarg1, AccountMediaConfig jarg1_, long jarg2, SrtpOpt jarg2_);
  public final static native long AccountMediaConfig_srtpOpt_get(long jarg1, AccountMediaConfig jarg1_);
  public final static native void AccountMediaConfig_ipv6Use_set(long jarg1, AccountMediaConfig jarg1_, int jarg2);
  public final static native int AccountMediaConfig_ipv6Use_get(long jarg1, AccountMediaConfig jarg1_);
  public final static native void AccountMediaConfig_rtcpMuxEnabled_set(long jarg1, AccountMediaConfig jarg1_, boolean jarg2);
  public final static native boolean AccountMediaConfig_rtcpMuxEnabled_get(long jarg1, AccountMediaConfig jarg1_);
  public final static native void AccountMediaConfig_rtcpFbConfig_set(long jarg1, AccountMediaConfig jarg1_, long jarg2, RtcpFbConfig jarg2_);
  public final static native long AccountMediaConfig_rtcpFbConfig_get(long jarg1, AccountMediaConfig jarg1_);
  public final static native void AccountMediaConfig_rtcpXrEnabled_set(long jarg1, AccountMediaConfig jarg1_, boolean jarg2);
  public final static native boolean AccountMediaConfig_rtcpXrEnabled_get(long jarg1, AccountMediaConfig jarg1_);
  public final static native void AccountMediaConfig_useLoopMedTp_set(long jarg1, AccountMediaConfig jarg1_, boolean jarg2);
  public final static native boolean AccountMediaConfig_useLoopMedTp_get(long jarg1, AccountMediaConfig jarg1_);
  public final static native void AccountMediaConfig_enableLoopback_set(long jarg1, AccountMediaConfig jarg1_, boolean jarg2);
  public final static native boolean AccountMediaConfig_enableLoopback_get(long jarg1, AccountMediaConfig jarg1_);
  public final static native long new_AccountMediaConfig();
  public final static native void AccountMediaConfig_readObject(long jarg1, AccountMediaConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void AccountMediaConfig_writeObject(long jarg1, AccountMediaConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void delete_AccountMediaConfig(long jarg1);
  public final static native void AccountVideoConfig_autoShowIncoming_set(long jarg1, AccountVideoConfig jarg1_, boolean jarg2);
  public final static native boolean AccountVideoConfig_autoShowIncoming_get(long jarg1, AccountVideoConfig jarg1_);
  public final static native void AccountVideoConfig_autoTransmitOutgoing_set(long jarg1, AccountVideoConfig jarg1_, boolean jarg2);
  public final static native boolean AccountVideoConfig_autoTransmitOutgoing_get(long jarg1, AccountVideoConfig jarg1_);
  public final static native void AccountVideoConfig_windowFlags_set(long jarg1, AccountVideoConfig jarg1_, long jarg2);
  public final static native long AccountVideoConfig_windowFlags_get(long jarg1, AccountVideoConfig jarg1_);
  public final static native void AccountVideoConfig_defaultCaptureDevice_set(long jarg1, AccountVideoConfig jarg1_, int jarg2);
  public final static native int AccountVideoConfig_defaultCaptureDevice_get(long jarg1, AccountVideoConfig jarg1_);
  public final static native void AccountVideoConfig_defaultRenderDevice_set(long jarg1, AccountVideoConfig jarg1_, int jarg2);
  public final static native int AccountVideoConfig_defaultRenderDevice_get(long jarg1, AccountVideoConfig jarg1_);
  public final static native void AccountVideoConfig_rateControlMethod_set(long jarg1, AccountVideoConfig jarg1_, int jarg2);
  public final static native int AccountVideoConfig_rateControlMethod_get(long jarg1, AccountVideoConfig jarg1_);
  public final static native void AccountVideoConfig_rateControlBandwidth_set(long jarg1, AccountVideoConfig jarg1_, long jarg2);
  public final static native long AccountVideoConfig_rateControlBandwidth_get(long jarg1, AccountVideoConfig jarg1_);
  public final static native void AccountVideoConfig_startKeyframeCount_set(long jarg1, AccountVideoConfig jarg1_, long jarg2);
  public final static native long AccountVideoConfig_startKeyframeCount_get(long jarg1, AccountVideoConfig jarg1_);
  public final static native void AccountVideoConfig_startKeyframeInterval_set(long jarg1, AccountVideoConfig jarg1_, long jarg2);
  public final static native long AccountVideoConfig_startKeyframeInterval_get(long jarg1, AccountVideoConfig jarg1_);
  public final static native long new_AccountVideoConfig();
  public final static native void AccountVideoConfig_readObject(long jarg1, AccountVideoConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void AccountVideoConfig_writeObject(long jarg1, AccountVideoConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void delete_AccountVideoConfig(long jarg1);
  public final static native void AccountIpChangeConfig_shutdownTp_set(long jarg1, AccountIpChangeConfig jarg1_, boolean jarg2);
  public final static native boolean AccountIpChangeConfig_shutdownTp_get(long jarg1, AccountIpChangeConfig jarg1_);
  public final static native void AccountIpChangeConfig_hangupCalls_set(long jarg1, AccountIpChangeConfig jarg1_, boolean jarg2);
  public final static native boolean AccountIpChangeConfig_hangupCalls_get(long jarg1, AccountIpChangeConfig jarg1_);
  public final static native void AccountIpChangeConfig_reinviteFlags_set(long jarg1, AccountIpChangeConfig jarg1_, long jarg2);
  public final static native long AccountIpChangeConfig_reinviteFlags_get(long jarg1, AccountIpChangeConfig jarg1_);
  public final static native void AccountIpChangeConfig_reinvUseUpdate_set(long jarg1, AccountIpChangeConfig jarg1_, long jarg2);
  public final static native long AccountIpChangeConfig_reinvUseUpdate_get(long jarg1, AccountIpChangeConfig jarg1_);
  public final static native void delete_AccountIpChangeConfig(long jarg1);
  public final static native void AccountIpChangeConfig_readObject(long jarg1, AccountIpChangeConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void AccountIpChangeConfig_writeObject(long jarg1, AccountIpChangeConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native long new_AccountIpChangeConfig();
  public final static native void AccountConfig_priority_set(long jarg1, AccountConfig jarg1_, int jarg2);
  public final static native int AccountConfig_priority_get(long jarg1, AccountConfig jarg1_);
  public final static native void AccountConfig_idUri_set(long jarg1, AccountConfig jarg1_, String jarg2);
  public final static native String AccountConfig_idUri_get(long jarg1, AccountConfig jarg1_);
  public final static native void AccountConfig_regConfig_set(long jarg1, AccountConfig jarg1_, long jarg2, AccountRegConfig jarg2_);
  public final static native long AccountConfig_regConfig_get(long jarg1, AccountConfig jarg1_);
  public final static native void AccountConfig_sipConfig_set(long jarg1, AccountConfig jarg1_, long jarg2, AccountSipConfig jarg2_);
  public final static native long AccountConfig_sipConfig_get(long jarg1, AccountConfig jarg1_);
  public final static native void AccountConfig_callConfig_set(long jarg1, AccountConfig jarg1_, long jarg2, AccountCallConfig jarg2_);
  public final static native long AccountConfig_callConfig_get(long jarg1, AccountConfig jarg1_);
  public final static native void AccountConfig_presConfig_set(long jarg1, AccountConfig jarg1_, long jarg2, AccountPresConfig jarg2_);
  public final static native long AccountConfig_presConfig_get(long jarg1, AccountConfig jarg1_);
  public final static native void AccountConfig_mwiConfig_set(long jarg1, AccountConfig jarg1_, long jarg2, AccountMwiConfig jarg2_);
  public final static native long AccountConfig_mwiConfig_get(long jarg1, AccountConfig jarg1_);
  public final static native void AccountConfig_natConfig_set(long jarg1, AccountConfig jarg1_, long jarg2, AccountNatConfig jarg2_);
  public final static native long AccountConfig_natConfig_get(long jarg1, AccountConfig jarg1_);
  public final static native void AccountConfig_mediaConfig_set(long jarg1, AccountConfig jarg1_, long jarg2, AccountMediaConfig jarg2_);
  public final static native long AccountConfig_mediaConfig_get(long jarg1, AccountConfig jarg1_);
  public final static native void AccountConfig_videoConfig_set(long jarg1, AccountConfig jarg1_, long jarg2, AccountVideoConfig jarg2_);
  public final static native long AccountConfig_videoConfig_get(long jarg1, AccountConfig jarg1_);
  public final static native void AccountConfig_ipChangeConfig_set(long jarg1, AccountConfig jarg1_, long jarg2, AccountIpChangeConfig jarg2_);
  public final static native long AccountConfig_ipChangeConfig_get(long jarg1, AccountConfig jarg1_);
  public final static native long new_AccountConfig();
  public final static native void AccountConfig_readObject(long jarg1, AccountConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void AccountConfig_writeObject(long jarg1, AccountConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void delete_AccountConfig(long jarg1);
  public final static native void AccountInfo_id_set(long jarg1, AccountInfo jarg1_, int jarg2);
  public final static native int AccountInfo_id_get(long jarg1, AccountInfo jarg1_);
  public final static native void AccountInfo_isDefault_set(long jarg1, AccountInfo jarg1_, boolean jarg2);
  public final static native boolean AccountInfo_isDefault_get(long jarg1, AccountInfo jarg1_);
  public final static native void AccountInfo_uri_set(long jarg1, AccountInfo jarg1_, String jarg2);
  public final static native String AccountInfo_uri_get(long jarg1, AccountInfo jarg1_);
  public final static native void AccountInfo_regIsConfigured_set(long jarg1, AccountInfo jarg1_, boolean jarg2);
  public final static native boolean AccountInfo_regIsConfigured_get(long jarg1, AccountInfo jarg1_);
  public final static native void AccountInfo_regIsActive_set(long jarg1, AccountInfo jarg1_, boolean jarg2);
  public final static native boolean AccountInfo_regIsActive_get(long jarg1, AccountInfo jarg1_);
  public final static native void AccountInfo_regExpiresSec_set(long jarg1, AccountInfo jarg1_, long jarg2);
  public final static native long AccountInfo_regExpiresSec_get(long jarg1, AccountInfo jarg1_);
  public final static native void AccountInfo_regStatus_set(long jarg1, AccountInfo jarg1_, int jarg2);
  public final static native int AccountInfo_regStatus_get(long jarg1, AccountInfo jarg1_);
  public final static native void AccountInfo_regStatusText_set(long jarg1, AccountInfo jarg1_, String jarg2);
  public final static native String AccountInfo_regStatusText_get(long jarg1, AccountInfo jarg1_);
  public final static native void AccountInfo_regLastErr_set(long jarg1, AccountInfo jarg1_, int jarg2);
  public final static native int AccountInfo_regLastErr_get(long jarg1, AccountInfo jarg1_);
  public final static native void AccountInfo_onlineStatus_set(long jarg1, AccountInfo jarg1_, boolean jarg2);
  public final static native boolean AccountInfo_onlineStatus_get(long jarg1, AccountInfo jarg1_);
  public final static native void AccountInfo_onlineStatusText_set(long jarg1, AccountInfo jarg1_, String jarg2);
  public final static native String AccountInfo_onlineStatusText_get(long jarg1, AccountInfo jarg1_);
  public final static native long new_AccountInfo();
  public final static native void delete_AccountInfo(long jarg1);
  public final static native void OnIncomingCallParam_callId_set(long jarg1, OnIncomingCallParam jarg1_, int jarg2);
  public final static native int OnIncomingCallParam_callId_get(long jarg1, OnIncomingCallParam jarg1_);
  public final static native void OnIncomingCallParam_rdata_set(long jarg1, OnIncomingCallParam jarg1_, long jarg2, SipRxData jarg2_);
  public final static native long OnIncomingCallParam_rdata_get(long jarg1, OnIncomingCallParam jarg1_);
  public final static native long new_OnIncomingCallParam();
  public final static native void delete_OnIncomingCallParam(long jarg1);
  public final static native void OnRegStartedParam_renew_set(long jarg1, OnRegStartedParam jarg1_, boolean jarg2);
  public final static native boolean OnRegStartedParam_renew_get(long jarg1, OnRegStartedParam jarg1_);
  public final static native long new_OnRegStartedParam();
  public final static native void delete_OnRegStartedParam(long jarg1);
  public final static native void OnRegStateParam_status_set(long jarg1, OnRegStateParam jarg1_, int jarg2);
  public final static native int OnRegStateParam_status_get(long jarg1, OnRegStateParam jarg1_);
  public final static native void OnRegStateParam_code_set(long jarg1, OnRegStateParam jarg1_, int jarg2);
  public final static native int OnRegStateParam_code_get(long jarg1, OnRegStateParam jarg1_);
  public final static native void OnRegStateParam_reason_set(long jarg1, OnRegStateParam jarg1_, String jarg2);
  public final static native String OnRegStateParam_reason_get(long jarg1, OnRegStateParam jarg1_);
  public final static native void OnRegStateParam_rdata_set(long jarg1, OnRegStateParam jarg1_, long jarg2, SipRxData jarg2_);
  public final static native long OnRegStateParam_rdata_get(long jarg1, OnRegStateParam jarg1_);
  public final static native void OnRegStateParam_expiration_set(long jarg1, OnRegStateParam jarg1_, long jarg2);
  public final static native long OnRegStateParam_expiration_get(long jarg1, OnRegStateParam jarg1_);
  public final static native long new_OnRegStateParam();
  public final static native void delete_OnRegStateParam(long jarg1);
  public final static native void OnIncomingSubscribeParam_srvPres_set(long jarg1, OnIncomingSubscribeParam jarg1_, long jarg2);
  public final static native long OnIncomingSubscribeParam_srvPres_get(long jarg1, OnIncomingSubscribeParam jarg1_);
  public final static native void OnIncomingSubscribeParam_fromUri_set(long jarg1, OnIncomingSubscribeParam jarg1_, String jarg2);
  public final static native String OnIncomingSubscribeParam_fromUri_get(long jarg1, OnIncomingSubscribeParam jarg1_);
  public final static native void OnIncomingSubscribeParam_rdata_set(long jarg1, OnIncomingSubscribeParam jarg1_, long jarg2, SipRxData jarg2_);
  public final static native long OnIncomingSubscribeParam_rdata_get(long jarg1, OnIncomingSubscribeParam jarg1_);
  public final static native void OnIncomingSubscribeParam_code_set(long jarg1, OnIncomingSubscribeParam jarg1_, int jarg2);
  public final static native int OnIncomingSubscribeParam_code_get(long jarg1, OnIncomingSubscribeParam jarg1_);
  public final static native void OnIncomingSubscribeParam_reason_set(long jarg1, OnIncomingSubscribeParam jarg1_, String jarg2);
  public final static native String OnIncomingSubscribeParam_reason_get(long jarg1, OnIncomingSubscribeParam jarg1_);
  public final static native void OnIncomingSubscribeParam_txOption_set(long jarg1, OnIncomingSubscribeParam jarg1_, long jarg2, SipTxOption jarg2_);
  public final static native long OnIncomingSubscribeParam_txOption_get(long jarg1, OnIncomingSubscribeParam jarg1_);
  public final static native long new_OnIncomingSubscribeParam();
  public final static native void delete_OnIncomingSubscribeParam(long jarg1);
  public final static native void OnInstantMessageParam_fromUri_set(long jarg1, OnInstantMessageParam jarg1_, String jarg2);
  public final static native String OnInstantMessageParam_fromUri_get(long jarg1, OnInstantMessageParam jarg1_);
  public final static native void OnInstantMessageParam_toUri_set(long jarg1, OnInstantMessageParam jarg1_, String jarg2);
  public final static native String OnInstantMessageParam_toUri_get(long jarg1, OnInstantMessageParam jarg1_);
  public final static native void OnInstantMessageParam_contactUri_set(long jarg1, OnInstantMessageParam jarg1_, String jarg2);
  public final static native String OnInstantMessageParam_contactUri_get(long jarg1, OnInstantMessageParam jarg1_);
  public final static native void OnInstantMessageParam_contentType_set(long jarg1, OnInstantMessageParam jarg1_, String jarg2);
  public final static native String OnInstantMessageParam_contentType_get(long jarg1, OnInstantMessageParam jarg1_);
  public final static native void OnInstantMessageParam_msgBody_set(long jarg1, OnInstantMessageParam jarg1_, String jarg2);
  public final static native String OnInstantMessageParam_msgBody_get(long jarg1, OnInstantMessageParam jarg1_);
  public final static native void OnInstantMessageParam_rdata_set(long jarg1, OnInstantMessageParam jarg1_, long jarg2, SipRxData jarg2_);
  public final static native long OnInstantMessageParam_rdata_get(long jarg1, OnInstantMessageParam jarg1_);
  public final static native long new_OnInstantMessageParam();
  public final static native void delete_OnInstantMessageParam(long jarg1);
  public final static native void OnInstantMessageStatusParam_userData_set(long jarg1, OnInstantMessageStatusParam jarg1_, long jarg2);
  public final static native long OnInstantMessageStatusParam_userData_get(long jarg1, OnInstantMessageStatusParam jarg1_);
  public final static native void OnInstantMessageStatusParam_toUri_set(long jarg1, OnInstantMessageStatusParam jarg1_, String jarg2);
  public final static native String OnInstantMessageStatusParam_toUri_get(long jarg1, OnInstantMessageStatusParam jarg1_);
  public final static native void OnInstantMessageStatusParam_msgBody_set(long jarg1, OnInstantMessageStatusParam jarg1_, String jarg2);
  public final static native String OnInstantMessageStatusParam_msgBody_get(long jarg1, OnInstantMessageStatusParam jarg1_);
  public final static native void OnInstantMessageStatusParam_code_set(long jarg1, OnInstantMessageStatusParam jarg1_, int jarg2);
  public final static native int OnInstantMessageStatusParam_code_get(long jarg1, OnInstantMessageStatusParam jarg1_);
  public final static native void OnInstantMessageStatusParam_reason_set(long jarg1, OnInstantMessageStatusParam jarg1_, String jarg2);
  public final static native String OnInstantMessageStatusParam_reason_get(long jarg1, OnInstantMessageStatusParam jarg1_);
  public final static native void OnInstantMessageStatusParam_rdata_set(long jarg1, OnInstantMessageStatusParam jarg1_, long jarg2, SipRxData jarg2_);
  public final static native long OnInstantMessageStatusParam_rdata_get(long jarg1, OnInstantMessageStatusParam jarg1_);
  public final static native long new_OnInstantMessageStatusParam();
  public final static native void delete_OnInstantMessageStatusParam(long jarg1);
  public final static native void OnTypingIndicationParam_fromUri_set(long jarg1, OnTypingIndicationParam jarg1_, String jarg2);
  public final static native String OnTypingIndicationParam_fromUri_get(long jarg1, OnTypingIndicationParam jarg1_);
  public final static native void OnTypingIndicationParam_toUri_set(long jarg1, OnTypingIndicationParam jarg1_, String jarg2);
  public final static native String OnTypingIndicationParam_toUri_get(long jarg1, OnTypingIndicationParam jarg1_);
  public final static native void OnTypingIndicationParam_contactUri_set(long jarg1, OnTypingIndicationParam jarg1_, String jarg2);
  public final static native String OnTypingIndicationParam_contactUri_get(long jarg1, OnTypingIndicationParam jarg1_);
  public final static native void OnTypingIndicationParam_isTyping_set(long jarg1, OnTypingIndicationParam jarg1_, boolean jarg2);
  public final static native boolean OnTypingIndicationParam_isTyping_get(long jarg1, OnTypingIndicationParam jarg1_);
  public final static native void OnTypingIndicationParam_rdata_set(long jarg1, OnTypingIndicationParam jarg1_, long jarg2, SipRxData jarg2_);
  public final static native long OnTypingIndicationParam_rdata_get(long jarg1, OnTypingIndicationParam jarg1_);
  public final static native long new_OnTypingIndicationParam();
  public final static native void delete_OnTypingIndicationParam(long jarg1);
  public final static native void OnMwiInfoParam_state_set(long jarg1, OnMwiInfoParam jarg1_, int jarg2);
  public final static native int OnMwiInfoParam_state_get(long jarg1, OnMwiInfoParam jarg1_);
  public final static native void OnMwiInfoParam_rdata_set(long jarg1, OnMwiInfoParam jarg1_, long jarg2, SipRxData jarg2_);
  public final static native long OnMwiInfoParam_rdata_get(long jarg1, OnMwiInfoParam jarg1_);
  public final static native long new_OnMwiInfoParam();
  public final static native void delete_OnMwiInfoParam(long jarg1);
  public final static native void PresNotifyParam_srvPres_set(long jarg1, PresNotifyParam jarg1_, long jarg2);
  public final static native long PresNotifyParam_srvPres_get(long jarg1, PresNotifyParam jarg1_);
  public final static native void PresNotifyParam_state_set(long jarg1, PresNotifyParam jarg1_, int jarg2);
  public final static native int PresNotifyParam_state_get(long jarg1, PresNotifyParam jarg1_);
  public final static native void PresNotifyParam_stateStr_set(long jarg1, PresNotifyParam jarg1_, String jarg2);
  public final static native String PresNotifyParam_stateStr_get(long jarg1, PresNotifyParam jarg1_);
  public final static native void PresNotifyParam_reason_set(long jarg1, PresNotifyParam jarg1_, String jarg2);
  public final static native String PresNotifyParam_reason_get(long jarg1, PresNotifyParam jarg1_);
  public final static native void PresNotifyParam_withBody_set(long jarg1, PresNotifyParam jarg1_, boolean jarg2);
  public final static native boolean PresNotifyParam_withBody_get(long jarg1, PresNotifyParam jarg1_);
  public final static native void PresNotifyParam_txOption_set(long jarg1, PresNotifyParam jarg1_, long jarg2, SipTxOption jarg2_);
  public final static native long PresNotifyParam_txOption_get(long jarg1, PresNotifyParam jarg1_);
  public final static native long new_PresNotifyParam();
  public final static native void delete_PresNotifyParam(long jarg1);
  public final static native boolean FindBuddyMatch_match(long jarg1, FindBuddyMatch jarg1_, String jarg2, long jarg3, Buddy jarg3_);
  public final static native boolean FindBuddyMatch_matchSwigExplicitFindBuddyMatch(long jarg1, FindBuddyMatch jarg1_, String jarg2, long jarg3, Buddy jarg3_);
  public final static native void delete_FindBuddyMatch(long jarg1);
  public final static native long new_FindBuddyMatch();
  public final static native void FindBuddyMatch_director_connect(FindBuddyMatch obj, long cptr, boolean mem_own, boolean weak_global);
  public final static native void FindBuddyMatch_change_ownership(FindBuddyMatch obj, long cptr, boolean take_or_release);
  public final static native long new_Account();
  public final static native void delete_Account(long jarg1);
  public final static native void Account_create__SWIG_0(long jarg1, Account jarg1_, long jarg2, AccountConfig jarg2_, boolean jarg3) throws java.lang.Exception;
  public final static native void Account_create__SWIG_1(long jarg1, Account jarg1_, long jarg2, AccountConfig jarg2_) throws java.lang.Exception;
  public final static native void Account_shutdown(long jarg1, Account jarg1_);
  public final static native void Account_modify(long jarg1, Account jarg1_, long jarg2, AccountConfig jarg2_) throws java.lang.Exception;
  public final static native boolean Account_isValid(long jarg1, Account jarg1_);
  public final static native void Account_setDefault(long jarg1, Account jarg1_) throws java.lang.Exception;
  public final static native boolean Account_isDefault(long jarg1, Account jarg1_);
  public final static native int Account_getId(long jarg1, Account jarg1_);
  public final static native long Account_lookup(int jarg1);
  public final static native long Account_getInfo(long jarg1, Account jarg1_) throws java.lang.Exception;
  public final static native void Account_setRegistration(long jarg1, Account jarg1_, boolean jarg2) throws java.lang.Exception;
  public final static native void Account_setOnlineStatus(long jarg1, Account jarg1_, long jarg2, PresenceStatus jarg2_) throws java.lang.Exception;
  public final static native void Account_setTransport(long jarg1, Account jarg1_, int jarg2) throws java.lang.Exception;
  public final static native void Account_presNotify(long jarg1, Account jarg1_, long jarg2, PresNotifyParam jarg2_) throws java.lang.Exception;
  public final static native long Account_enumBuddies2(long jarg1, Account jarg1_) throws java.lang.Exception;
  public final static native long Account_findBuddy2(long jarg1, Account jarg1_, String jarg2) throws java.lang.Exception;
  public final static native void Account_onIncomingCall(long jarg1, Account jarg1_, long jarg2, OnIncomingCallParam jarg2_);
  public final static native void Account_onIncomingCallSwigExplicitAccount(long jarg1, Account jarg1_, long jarg2, OnIncomingCallParam jarg2_);
  public final static native void Account_onRegStarted(long jarg1, Account jarg1_, long jarg2, OnRegStartedParam jarg2_);
  public final static native void Account_onRegStartedSwigExplicitAccount(long jarg1, Account jarg1_, long jarg2, OnRegStartedParam jarg2_);
  public final static native void Account_onRegState(long jarg1, Account jarg1_, long jarg2, OnRegStateParam jarg2_);
  public final static native void Account_onRegStateSwigExplicitAccount(long jarg1, Account jarg1_, long jarg2, OnRegStateParam jarg2_);
  public final static native void Account_onIncomingSubscribe(long jarg1, Account jarg1_, long jarg2, OnIncomingSubscribeParam jarg2_);
  public final static native void Account_onIncomingSubscribeSwigExplicitAccount(long jarg1, Account jarg1_, long jarg2, OnIncomingSubscribeParam jarg2_);
  public final static native void Account_onInstantMessage(long jarg1, Account jarg1_, long jarg2, OnInstantMessageParam jarg2_);
  public final static native void Account_onInstantMessageSwigExplicitAccount(long jarg1, Account jarg1_, long jarg2, OnInstantMessageParam jarg2_);
  public final static native void Account_onInstantMessageStatus(long jarg1, Account jarg1_, long jarg2, OnInstantMessageStatusParam jarg2_);
  public final static native void Account_onInstantMessageStatusSwigExplicitAccount(long jarg1, Account jarg1_, long jarg2, OnInstantMessageStatusParam jarg2_);
  public final static native void Account_onTypingIndication(long jarg1, Account jarg1_, long jarg2, OnTypingIndicationParam jarg2_);
  public final static native void Account_onTypingIndicationSwigExplicitAccount(long jarg1, Account jarg1_, long jarg2, OnTypingIndicationParam jarg2_);
  public final static native void Account_onMwiInfo(long jarg1, Account jarg1_, long jarg2, OnMwiInfoParam jarg2_);
  public final static native void Account_onMwiInfoSwigExplicitAccount(long jarg1, Account jarg1_, long jarg2, OnMwiInfoParam jarg2_);
  public final static native void Account_director_connect(Account obj, long cptr, boolean mem_own, boolean weak_global);
  public final static native void Account_change_ownership(Account obj, long cptr, boolean take_or_release);
  public final static native void MathStat_n_set(long jarg1, MathStat jarg1_, int jarg2);
  public final static native int MathStat_n_get(long jarg1, MathStat jarg1_);
  public final static native void MathStat_max_set(long jarg1, MathStat jarg1_, int jarg2);
  public final static native int MathStat_max_get(long jarg1, MathStat jarg1_);
  public final static native void MathStat_min_set(long jarg1, MathStat jarg1_, int jarg2);
  public final static native int MathStat_min_get(long jarg1, MathStat jarg1_);
  public final static native void MathStat_last_set(long jarg1, MathStat jarg1_, int jarg2);
  public final static native int MathStat_last_get(long jarg1, MathStat jarg1_);
  public final static native void MathStat_mean_set(long jarg1, MathStat jarg1_, int jarg2);
  public final static native int MathStat_mean_get(long jarg1, MathStat jarg1_);
  public final static native long new_MathStat();
  public final static native void delete_MathStat(long jarg1);
  public final static native void LossType_burst_set(long jarg1, LossType jarg1_, long jarg2);
  public final static native long LossType_burst_get(long jarg1, LossType jarg1_);
  public final static native void LossType_random_set(long jarg1, LossType jarg1_, long jarg2);
  public final static native long LossType_random_get(long jarg1, LossType jarg1_);
  public final static native long new_LossType();
  public final static native void delete_LossType(long jarg1);
  public final static native void RtcpStreamStat_update_set(long jarg1, RtcpStreamStat jarg1_, long jarg2, TimeVal jarg2_);
  public final static native long RtcpStreamStat_update_get(long jarg1, RtcpStreamStat jarg1_);
  public final static native void RtcpStreamStat_updateCount_set(long jarg1, RtcpStreamStat jarg1_, long jarg2);
  public final static native long RtcpStreamStat_updateCount_get(long jarg1, RtcpStreamStat jarg1_);
  public final static native void RtcpStreamStat_pkt_set(long jarg1, RtcpStreamStat jarg1_, long jarg2);
  public final static native long RtcpStreamStat_pkt_get(long jarg1, RtcpStreamStat jarg1_);
  public final static native void RtcpStreamStat_bytes_set(long jarg1, RtcpStreamStat jarg1_, long jarg2);
  public final static native long RtcpStreamStat_bytes_get(long jarg1, RtcpStreamStat jarg1_);
  public final static native void RtcpStreamStat_discard_set(long jarg1, RtcpStreamStat jarg1_, long jarg2);
  public final static native long RtcpStreamStat_discard_get(long jarg1, RtcpStreamStat jarg1_);
  public final static native void RtcpStreamStat_loss_set(long jarg1, RtcpStreamStat jarg1_, long jarg2);
  public final static native long RtcpStreamStat_loss_get(long jarg1, RtcpStreamStat jarg1_);
  public final static native void RtcpStreamStat_reorder_set(long jarg1, RtcpStreamStat jarg1_, long jarg2);
  public final static native long RtcpStreamStat_reorder_get(long jarg1, RtcpStreamStat jarg1_);
  public final static native void RtcpStreamStat_dup_set(long jarg1, RtcpStreamStat jarg1_, long jarg2);
  public final static native long RtcpStreamStat_dup_get(long jarg1, RtcpStreamStat jarg1_);
  public final static native void RtcpStreamStat_lossPeriodUsec_set(long jarg1, RtcpStreamStat jarg1_, long jarg2, MathStat jarg2_);
  public final static native long RtcpStreamStat_lossPeriodUsec_get(long jarg1, RtcpStreamStat jarg1_);
  public final static native void RtcpStreamStat_lossType_set(long jarg1, RtcpStreamStat jarg1_, long jarg2, LossType jarg2_);
  public final static native long RtcpStreamStat_lossType_get(long jarg1, RtcpStreamStat jarg1_);
  public final static native void RtcpStreamStat_jitterUsec_set(long jarg1, RtcpStreamStat jarg1_, long jarg2, MathStat jarg2_);
  public final static native long RtcpStreamStat_jitterUsec_get(long jarg1, RtcpStreamStat jarg1_);
  public final static native long new_RtcpStreamStat();
  public final static native void delete_RtcpStreamStat(long jarg1);
  public final static native void RtcpSdes_cname_set(long jarg1, RtcpSdes jarg1_, String jarg2);
  public final static native String RtcpSdes_cname_get(long jarg1, RtcpSdes jarg1_);
  public final static native void RtcpSdes_name_set(long jarg1, RtcpSdes jarg1_, String jarg2);
  public final static native String RtcpSdes_name_get(long jarg1, RtcpSdes jarg1_);
  public final static native void RtcpSdes_email_set(long jarg1, RtcpSdes jarg1_, String jarg2);
  public final static native String RtcpSdes_email_get(long jarg1, RtcpSdes jarg1_);
  public final static native void RtcpSdes_phone_set(long jarg1, RtcpSdes jarg1_, String jarg2);
  public final static native String RtcpSdes_phone_get(long jarg1, RtcpSdes jarg1_);
  public final static native void RtcpSdes_loc_set(long jarg1, RtcpSdes jarg1_, String jarg2);
  public final static native String RtcpSdes_loc_get(long jarg1, RtcpSdes jarg1_);
  public final static native void RtcpSdes_tool_set(long jarg1, RtcpSdes jarg1_, String jarg2);
  public final static native String RtcpSdes_tool_get(long jarg1, RtcpSdes jarg1_);
  public final static native void RtcpSdes_note_set(long jarg1, RtcpSdes jarg1_, String jarg2);
  public final static native String RtcpSdes_note_get(long jarg1, RtcpSdes jarg1_);
  public final static native long new_RtcpSdes();
  public final static native void delete_RtcpSdes(long jarg1);
  public final static native void RtcpStat_start_set(long jarg1, RtcpStat jarg1_, long jarg2, TimeVal jarg2_);
  public final static native long RtcpStat_start_get(long jarg1, RtcpStat jarg1_);
  public final static native void RtcpStat_txStat_set(long jarg1, RtcpStat jarg1_, long jarg2, RtcpStreamStat jarg2_);
  public final static native long RtcpStat_txStat_get(long jarg1, RtcpStat jarg1_);
  public final static native void RtcpStat_rxStat_set(long jarg1, RtcpStat jarg1_, long jarg2, RtcpStreamStat jarg2_);
  public final static native long RtcpStat_rxStat_get(long jarg1, RtcpStat jarg1_);
  public final static native void RtcpStat_rttUsec_set(long jarg1, RtcpStat jarg1_, long jarg2, MathStat jarg2_);
  public final static native long RtcpStat_rttUsec_get(long jarg1, RtcpStat jarg1_);
  public final static native void RtcpStat_rtpTxLastTs_set(long jarg1, RtcpStat jarg1_, long jarg2);
  public final static native long RtcpStat_rtpTxLastTs_get(long jarg1, RtcpStat jarg1_);
  public final static native void RtcpStat_rtpTxLastSeq_set(long jarg1, RtcpStat jarg1_, int jarg2);
  public final static native int RtcpStat_rtpTxLastSeq_get(long jarg1, RtcpStat jarg1_);
  public final static native void RtcpStat_rxIpdvUsec_set(long jarg1, RtcpStat jarg1_, long jarg2, MathStat jarg2_);
  public final static native long RtcpStat_rxIpdvUsec_get(long jarg1, RtcpStat jarg1_);
  public final static native void RtcpStat_rxRawJitterUsec_set(long jarg1, RtcpStat jarg1_, long jarg2, MathStat jarg2_);
  public final static native long RtcpStat_rxRawJitterUsec_get(long jarg1, RtcpStat jarg1_);
  public final static native void RtcpStat_peerSdes_set(long jarg1, RtcpStat jarg1_, long jarg2, RtcpSdes jarg2_);
  public final static native long RtcpStat_peerSdes_get(long jarg1, RtcpStat jarg1_);
  public final static native long new_RtcpStat();
  public final static native void delete_RtcpStat(long jarg1);
  public final static native void JbufState_frameSize_set(long jarg1, JbufState jarg1_, long jarg2);
  public final static native long JbufState_frameSize_get(long jarg1, JbufState jarg1_);
  public final static native void JbufState_minPrefetch_set(long jarg1, JbufState jarg1_, long jarg2);
  public final static native long JbufState_minPrefetch_get(long jarg1, JbufState jarg1_);
  public final static native void JbufState_maxPrefetch_set(long jarg1, JbufState jarg1_, long jarg2);
  public final static native long JbufState_maxPrefetch_get(long jarg1, JbufState jarg1_);
  public final static native void JbufState_burst_set(long jarg1, JbufState jarg1_, long jarg2);
  public final static native long JbufState_burst_get(long jarg1, JbufState jarg1_);
  public final static native void JbufState_prefetch_set(long jarg1, JbufState jarg1_, long jarg2);
  public final static native long JbufState_prefetch_get(long jarg1, JbufState jarg1_);
  public final static native void JbufState_size_set(long jarg1, JbufState jarg1_, long jarg2);
  public final static native long JbufState_size_get(long jarg1, JbufState jarg1_);
  public final static native void JbufState_avgDelayMsec_set(long jarg1, JbufState jarg1_, long jarg2);
  public final static native long JbufState_avgDelayMsec_get(long jarg1, JbufState jarg1_);
  public final static native void JbufState_minDelayMsec_set(long jarg1, JbufState jarg1_, long jarg2);
  public final static native long JbufState_minDelayMsec_get(long jarg1, JbufState jarg1_);
  public final static native void JbufState_maxDelayMsec_set(long jarg1, JbufState jarg1_, long jarg2);
  public final static native long JbufState_maxDelayMsec_get(long jarg1, JbufState jarg1_);
  public final static native void JbufState_devDelayMsec_set(long jarg1, JbufState jarg1_, long jarg2);
  public final static native long JbufState_devDelayMsec_get(long jarg1, JbufState jarg1_);
  public final static native void JbufState_avgBurst_set(long jarg1, JbufState jarg1_, long jarg2);
  public final static native long JbufState_avgBurst_get(long jarg1, JbufState jarg1_);
  public final static native void JbufState_lost_set(long jarg1, JbufState jarg1_, long jarg2);
  public final static native long JbufState_lost_get(long jarg1, JbufState jarg1_);
  public final static native void JbufState_discard_set(long jarg1, JbufState jarg1_, long jarg2);
  public final static native long JbufState_discard_get(long jarg1, JbufState jarg1_);
  public final static native void JbufState_empty_set(long jarg1, JbufState jarg1_, long jarg2);
  public final static native long JbufState_empty_get(long jarg1, JbufState jarg1_);
  public final static native long new_JbufState();
  public final static native void delete_JbufState(long jarg1);
  public final static native void SdpSession_wholeSdp_set(long jarg1, SdpSession jarg1_, String jarg2);
  public final static native String SdpSession_wholeSdp_get(long jarg1, SdpSession jarg1_);
  public final static native void SdpSession_pjSdpSession_set(long jarg1, SdpSession jarg1_, long jarg2);
  public final static native long SdpSession_pjSdpSession_get(long jarg1, SdpSession jarg1_);
  public final static native long new_SdpSession();
  public final static native void delete_SdpSession(long jarg1);
  public final static native void MediaTransportInfo_localRtpName_set(long jarg1, MediaTransportInfo jarg1_, String jarg2);
  public final static native String MediaTransportInfo_localRtpName_get(long jarg1, MediaTransportInfo jarg1_);
  public final static native void MediaTransportInfo_localRtcpName_set(long jarg1, MediaTransportInfo jarg1_, String jarg2);
  public final static native String MediaTransportInfo_localRtcpName_get(long jarg1, MediaTransportInfo jarg1_);
  public final static native void MediaTransportInfo_srcRtpName_set(long jarg1, MediaTransportInfo jarg1_, String jarg2);
  public final static native String MediaTransportInfo_srcRtpName_get(long jarg1, MediaTransportInfo jarg1_);
  public final static native void MediaTransportInfo_srcRtcpName_set(long jarg1, MediaTransportInfo jarg1_, String jarg2);
  public final static native String MediaTransportInfo_srcRtcpName_get(long jarg1, MediaTransportInfo jarg1_);
  public final static native long new_MediaTransportInfo();
  public final static native void delete_MediaTransportInfo(long jarg1);
  public final static native void CallSetting_flag_set(long jarg1, CallSetting jarg1_, long jarg2);
  public final static native long CallSetting_flag_get(long jarg1, CallSetting jarg1_);
  public final static native void CallSetting_reqKeyframeMethod_set(long jarg1, CallSetting jarg1_, long jarg2);
  public final static native long CallSetting_reqKeyframeMethod_get(long jarg1, CallSetting jarg1_);
  public final static native void CallSetting_audioCount_set(long jarg1, CallSetting jarg1_, long jarg2);
  public final static native long CallSetting_audioCount_get(long jarg1, CallSetting jarg1_);
  public final static native void CallSetting_videoCount_set(long jarg1, CallSetting jarg1_, long jarg2);
  public final static native long CallSetting_videoCount_get(long jarg1, CallSetting jarg1_);
  public final static native void CallSetting_mediaDir_set(long jarg1, CallSetting jarg1_, long jarg2, IntVector jarg2_);
  public final static native long CallSetting_mediaDir_get(long jarg1, CallSetting jarg1_);
  public final static native long new_CallSetting__SWIG_0(boolean jarg1);
  public final static native long new_CallSetting__SWIG_1();
  public final static native boolean CallSetting_isEmpty(long jarg1, CallSetting jarg1_);
  public final static native void delete_CallSetting(long jarg1);
  public final static native void CallMediaInfo_index_set(long jarg1, CallMediaInfo jarg1_, long jarg2);
  public final static native long CallMediaInfo_index_get(long jarg1, CallMediaInfo jarg1_);
  public final static native void CallMediaInfo_type_set(long jarg1, CallMediaInfo jarg1_, int jarg2);
  public final static native int CallMediaInfo_type_get(long jarg1, CallMediaInfo jarg1_);
  public final static native void CallMediaInfo_dir_set(long jarg1, CallMediaInfo jarg1_, int jarg2);
  public final static native int CallMediaInfo_dir_get(long jarg1, CallMediaInfo jarg1_);
  public final static native void CallMediaInfo_status_set(long jarg1, CallMediaInfo jarg1_, int jarg2);
  public final static native int CallMediaInfo_status_get(long jarg1, CallMediaInfo jarg1_);
  public final static native void CallMediaInfo_audioConfSlot_set(long jarg1, CallMediaInfo jarg1_, int jarg2);
  public final static native int CallMediaInfo_audioConfSlot_get(long jarg1, CallMediaInfo jarg1_);
  public final static native void CallMediaInfo_videoIncomingWindowId_set(long jarg1, CallMediaInfo jarg1_, int jarg2);
  public final static native int CallMediaInfo_videoIncomingWindowId_get(long jarg1, CallMediaInfo jarg1_);
  public final static native void CallMediaInfo_videoWindow_set(long jarg1, CallMediaInfo jarg1_, long jarg2, VideoWindow jarg2_);
  public final static native long CallMediaInfo_videoWindow_get(long jarg1, CallMediaInfo jarg1_);
  public final static native void CallMediaInfo_videoCapDev_set(long jarg1, CallMediaInfo jarg1_, int jarg2);
  public final static native int CallMediaInfo_videoCapDev_get(long jarg1, CallMediaInfo jarg1_);
  public final static native long new_CallMediaInfo();
  public final static native void delete_CallMediaInfo(long jarg1);
  public final static native void CallInfo_id_set(long jarg1, CallInfo jarg1_, int jarg2);
  public final static native int CallInfo_id_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_role_set(long jarg1, CallInfo jarg1_, int jarg2);
  public final static native int CallInfo_role_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_accId_set(long jarg1, CallInfo jarg1_, int jarg2);
  public final static native int CallInfo_accId_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_localUri_set(long jarg1, CallInfo jarg1_, String jarg2);
  public final static native String CallInfo_localUri_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_localContact_set(long jarg1, CallInfo jarg1_, String jarg2);
  public final static native String CallInfo_localContact_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_remoteUri_set(long jarg1, CallInfo jarg1_, String jarg2);
  public final static native String CallInfo_remoteUri_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_remoteContact_set(long jarg1, CallInfo jarg1_, String jarg2);
  public final static native String CallInfo_remoteContact_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_callIdString_set(long jarg1, CallInfo jarg1_, String jarg2);
  public final static native String CallInfo_callIdString_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_setting_set(long jarg1, CallInfo jarg1_, long jarg2, CallSetting jarg2_);
  public final static native long CallInfo_setting_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_state_set(long jarg1, CallInfo jarg1_, int jarg2);
  public final static native int CallInfo_state_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_stateText_set(long jarg1, CallInfo jarg1_, String jarg2);
  public final static native String CallInfo_stateText_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_lastStatusCode_set(long jarg1, CallInfo jarg1_, int jarg2);
  public final static native int CallInfo_lastStatusCode_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_lastReason_set(long jarg1, CallInfo jarg1_, String jarg2);
  public final static native String CallInfo_lastReason_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_media_set(long jarg1, CallInfo jarg1_, long jarg2, CallMediaInfoVector jarg2_);
  public final static native long CallInfo_media_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_provMedia_set(long jarg1, CallInfo jarg1_, long jarg2, CallMediaInfoVector jarg2_);
  public final static native long CallInfo_provMedia_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_connectDuration_set(long jarg1, CallInfo jarg1_, long jarg2, TimeVal jarg2_);
  public final static native long CallInfo_connectDuration_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_totalDuration_set(long jarg1, CallInfo jarg1_, long jarg2, TimeVal jarg2_);
  public final static native long CallInfo_totalDuration_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_remOfferer_set(long jarg1, CallInfo jarg1_, boolean jarg2);
  public final static native boolean CallInfo_remOfferer_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_remAudioCount_set(long jarg1, CallInfo jarg1_, long jarg2);
  public final static native long CallInfo_remAudioCount_get(long jarg1, CallInfo jarg1_);
  public final static native void CallInfo_remVideoCount_set(long jarg1, CallInfo jarg1_, long jarg2);
  public final static native long CallInfo_remVideoCount_get(long jarg1, CallInfo jarg1_);
  public final static native long new_CallInfo();
  public final static native void delete_CallInfo(long jarg1);
  public final static native void StreamInfo_type_set(long jarg1, StreamInfo jarg1_, int jarg2);
  public final static native int StreamInfo_type_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_proto_set(long jarg1, StreamInfo jarg1_, int jarg2);
  public final static native int StreamInfo_proto_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_dir_set(long jarg1, StreamInfo jarg1_, int jarg2);
  public final static native int StreamInfo_dir_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_remoteRtpAddress_set(long jarg1, StreamInfo jarg1_, String jarg2);
  public final static native String StreamInfo_remoteRtpAddress_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_remoteRtcpAddress_set(long jarg1, StreamInfo jarg1_, String jarg2);
  public final static native String StreamInfo_remoteRtcpAddress_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_txPt_set(long jarg1, StreamInfo jarg1_, long jarg2);
  public final static native long StreamInfo_txPt_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_rxPt_set(long jarg1, StreamInfo jarg1_, long jarg2);
  public final static native long StreamInfo_rxPt_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_codecName_set(long jarg1, StreamInfo jarg1_, String jarg2);
  public final static native String StreamInfo_codecName_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_codecClockRate_set(long jarg1, StreamInfo jarg1_, long jarg2);
  public final static native long StreamInfo_codecClockRate_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_audCodecParam_set(long jarg1, StreamInfo jarg1_, long jarg2, CodecParam jarg2_);
  public final static native long StreamInfo_audCodecParam_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_vidCodecParam_set(long jarg1, StreamInfo jarg1_, long jarg2, VidCodecParam jarg2_);
  public final static native long StreamInfo_vidCodecParam_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_jbInit_set(long jarg1, StreamInfo jarg1_, int jarg2);
  public final static native int StreamInfo_jbInit_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_jbMinPre_set(long jarg1, StreamInfo jarg1_, int jarg2);
  public final static native int StreamInfo_jbMinPre_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_jbMaxPre_set(long jarg1, StreamInfo jarg1_, int jarg2);
  public final static native int StreamInfo_jbMaxPre_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_jbMax_set(long jarg1, StreamInfo jarg1_, int jarg2);
  public final static native int StreamInfo_jbMax_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_jbDiscardAlgo_set(long jarg1, StreamInfo jarg1_, long jarg2);
  public final static native long StreamInfo_jbDiscardAlgo_get(long jarg1, StreamInfo jarg1_);
  public final static native void StreamInfo_rtcpSdesByeDisabled_set(long jarg1, StreamInfo jarg1_, boolean jarg2);
  public final static native boolean StreamInfo_rtcpSdesByeDisabled_get(long jarg1, StreamInfo jarg1_);
  public final static native long new_StreamInfo();
  public final static native void delete_StreamInfo(long jarg1);
  public final static native void StreamStat_rtcp_set(long jarg1, StreamStat jarg1_, long jarg2, RtcpStat jarg2_);
  public final static native long StreamStat_rtcp_get(long jarg1, StreamStat jarg1_);
  public final static native void StreamStat_jbuf_set(long jarg1, StreamStat jarg1_, long jarg2, JbufState jarg2_);
  public final static native long StreamStat_jbuf_get(long jarg1, StreamStat jarg1_);
  public final static native long new_StreamStat();
  public final static native void delete_StreamStat(long jarg1);
  public final static native void OnCallStateParam_e_set(long jarg1, OnCallStateParam jarg1_, long jarg2, SipEvent jarg2_);
  public final static native long OnCallStateParam_e_get(long jarg1, OnCallStateParam jarg1_);
  public final static native long new_OnCallStateParam();
  public final static native void delete_OnCallStateParam(long jarg1);
  public final static native void OnCallTsxStateParam_e_set(long jarg1, OnCallTsxStateParam jarg1_, long jarg2, SipEvent jarg2_);
  public final static native long OnCallTsxStateParam_e_get(long jarg1, OnCallTsxStateParam jarg1_);
  public final static native long new_OnCallTsxStateParam();
  public final static native void delete_OnCallTsxStateParam(long jarg1);
  public final static native long new_OnCallMediaStateParam();
  public final static native void delete_OnCallMediaStateParam(long jarg1);
  public final static native void OnCallSdpCreatedParam_sdp_set(long jarg1, OnCallSdpCreatedParam jarg1_, long jarg2, SdpSession jarg2_);
  public final static native long OnCallSdpCreatedParam_sdp_get(long jarg1, OnCallSdpCreatedParam jarg1_);
  public final static native void OnCallSdpCreatedParam_remSdp_set(long jarg1, OnCallSdpCreatedParam jarg1_, long jarg2, SdpSession jarg2_);
  public final static native long OnCallSdpCreatedParam_remSdp_get(long jarg1, OnCallSdpCreatedParam jarg1_);
  public final static native long new_OnCallSdpCreatedParam();
  public final static native void delete_OnCallSdpCreatedParam(long jarg1);
  public final static native void OnStreamPreCreateParam_streamIdx_set(long jarg1, OnStreamPreCreateParam jarg1_, long jarg2);
  public final static native long OnStreamPreCreateParam_streamIdx_get(long jarg1, OnStreamPreCreateParam jarg1_);
  public final static native void OnStreamPreCreateParam_streamInfo_set(long jarg1, OnStreamPreCreateParam jarg1_, long jarg2, StreamInfo jarg2_);
  public final static native long OnStreamPreCreateParam_streamInfo_get(long jarg1, OnStreamPreCreateParam jarg1_);
  public final static native long new_OnStreamPreCreateParam();
  public final static native void delete_OnStreamPreCreateParam(long jarg1);
  public final static native void OnStreamCreatedParam_stream_set(long jarg1, OnStreamCreatedParam jarg1_, long jarg2);
  public final static native long OnStreamCreatedParam_stream_get(long jarg1, OnStreamCreatedParam jarg1_);
  public final static native void OnStreamCreatedParam_streamIdx_set(long jarg1, OnStreamCreatedParam jarg1_, long jarg2);
  public final static native long OnStreamCreatedParam_streamIdx_get(long jarg1, OnStreamCreatedParam jarg1_);
  public final static native void OnStreamCreatedParam_destroyPort_set(long jarg1, OnStreamCreatedParam jarg1_, boolean jarg2);
  public final static native boolean OnStreamCreatedParam_destroyPort_get(long jarg1, OnStreamCreatedParam jarg1_);
  public final static native void OnStreamCreatedParam_pPort_set(long jarg1, OnStreamCreatedParam jarg1_, long jarg2);
  public final static native long OnStreamCreatedParam_pPort_get(long jarg1, OnStreamCreatedParam jarg1_);
  public final static native long new_OnStreamCreatedParam();
  public final static native void delete_OnStreamCreatedParam(long jarg1);
  public final static native void OnStreamDestroyedParam_stream_set(long jarg1, OnStreamDestroyedParam jarg1_, long jarg2);
  public final static native long OnStreamDestroyedParam_stream_get(long jarg1, OnStreamDestroyedParam jarg1_);
  public final static native void OnStreamDestroyedParam_streamIdx_set(long jarg1, OnStreamDestroyedParam jarg1_, long jarg2);
  public final static native long OnStreamDestroyedParam_streamIdx_get(long jarg1, OnStreamDestroyedParam jarg1_);
  public final static native long new_OnStreamDestroyedParam();
  public final static native void delete_OnStreamDestroyedParam(long jarg1);
  public final static native void OnDtmfDigitParam_method_set(long jarg1, OnDtmfDigitParam jarg1_, int jarg2);
  public final static native int OnDtmfDigitParam_method_get(long jarg1, OnDtmfDigitParam jarg1_);
  public final static native void OnDtmfDigitParam_digit_set(long jarg1, OnDtmfDigitParam jarg1_, String jarg2);
  public final static native String OnDtmfDigitParam_digit_get(long jarg1, OnDtmfDigitParam jarg1_);
  public final static native void OnDtmfDigitParam_duration_set(long jarg1, OnDtmfDigitParam jarg1_, long jarg2);
  public final static native long OnDtmfDigitParam_duration_get(long jarg1, OnDtmfDigitParam jarg1_);
  public final static native long new_OnDtmfDigitParam();
  public final static native void delete_OnDtmfDigitParam(long jarg1);
  public final static native void OnDtmfEventParam_method_set(long jarg1, OnDtmfEventParam jarg1_, int jarg2);
  public final static native int OnDtmfEventParam_method_get(long jarg1, OnDtmfEventParam jarg1_);
  public final static native void OnDtmfEventParam_timestamp_set(long jarg1, OnDtmfEventParam jarg1_, long jarg2);
  public final static native long OnDtmfEventParam_timestamp_get(long jarg1, OnDtmfEventParam jarg1_);
  public final static native void OnDtmfEventParam_digit_set(long jarg1, OnDtmfEventParam jarg1_, String jarg2);
  public final static native String OnDtmfEventParam_digit_get(long jarg1, OnDtmfEventParam jarg1_);
  public final static native void OnDtmfEventParam_duration_set(long jarg1, OnDtmfEventParam jarg1_, long jarg2);
  public final static native long OnDtmfEventParam_duration_get(long jarg1, OnDtmfEventParam jarg1_);
  public final static native void OnDtmfEventParam_flags_set(long jarg1, OnDtmfEventParam jarg1_, long jarg2);
  public final static native long OnDtmfEventParam_flags_get(long jarg1, OnDtmfEventParam jarg1_);
  public final static native long new_OnDtmfEventParam();
  public final static native void delete_OnDtmfEventParam(long jarg1);
  public final static native void OnCallTransferRequestParam_dstUri_set(long jarg1, OnCallTransferRequestParam jarg1_, String jarg2);
  public final static native String OnCallTransferRequestParam_dstUri_get(long jarg1, OnCallTransferRequestParam jarg1_);
  public final static native void OnCallTransferRequestParam_statusCode_set(long jarg1, OnCallTransferRequestParam jarg1_, int jarg2);
  public final static native int OnCallTransferRequestParam_statusCode_get(long jarg1, OnCallTransferRequestParam jarg1_);
  public final static native void OnCallTransferRequestParam_opt_set(long jarg1, OnCallTransferRequestParam jarg1_, long jarg2, CallSetting jarg2_);
  public final static native long OnCallTransferRequestParam_opt_get(long jarg1, OnCallTransferRequestParam jarg1_);
  public final static native void OnCallTransferRequestParam_newCall_set(long jarg1, OnCallTransferRequestParam jarg1_, long jarg2, Call jarg2_);
  public final static native long OnCallTransferRequestParam_newCall_get(long jarg1, OnCallTransferRequestParam jarg1_);
  public final static native long new_OnCallTransferRequestParam();
  public final static native void delete_OnCallTransferRequestParam(long jarg1);
  public final static native void OnCallTransferStatusParam_statusCode_set(long jarg1, OnCallTransferStatusParam jarg1_, int jarg2);
  public final static native int OnCallTransferStatusParam_statusCode_get(long jarg1, OnCallTransferStatusParam jarg1_);
  public final static native void OnCallTransferStatusParam_reason_set(long jarg1, OnCallTransferStatusParam jarg1_, String jarg2);
  public final static native String OnCallTransferStatusParam_reason_get(long jarg1, OnCallTransferStatusParam jarg1_);
  public final static native void OnCallTransferStatusParam_finalNotify_set(long jarg1, OnCallTransferStatusParam jarg1_, boolean jarg2);
  public final static native boolean OnCallTransferStatusParam_finalNotify_get(long jarg1, OnCallTransferStatusParam jarg1_);
  public final static native void OnCallTransferStatusParam_cont_set(long jarg1, OnCallTransferStatusParam jarg1_, boolean jarg2);
  public final static native boolean OnCallTransferStatusParam_cont_get(long jarg1, OnCallTransferStatusParam jarg1_);
  public final static native long new_OnCallTransferStatusParam();
  public final static native void delete_OnCallTransferStatusParam(long jarg1);
  public final static native void OnCallReplaceRequestParam_rdata_set(long jarg1, OnCallReplaceRequestParam jarg1_, long jarg2, SipRxData jarg2_);
  public final static native long OnCallReplaceRequestParam_rdata_get(long jarg1, OnCallReplaceRequestParam jarg1_);
  public final static native void OnCallReplaceRequestParam_statusCode_set(long jarg1, OnCallReplaceRequestParam jarg1_, int jarg2);
  public final static native int OnCallReplaceRequestParam_statusCode_get(long jarg1, OnCallReplaceRequestParam jarg1_);
  public final static native void OnCallReplaceRequestParam_reason_set(long jarg1, OnCallReplaceRequestParam jarg1_, String jarg2);
  public final static native String OnCallReplaceRequestParam_reason_get(long jarg1, OnCallReplaceRequestParam jarg1_);
  public final static native void OnCallReplaceRequestParam_opt_set(long jarg1, OnCallReplaceRequestParam jarg1_, long jarg2, CallSetting jarg2_);
  public final static native long OnCallReplaceRequestParam_opt_get(long jarg1, OnCallReplaceRequestParam jarg1_);
  public final static native void OnCallReplaceRequestParam_newCall_set(long jarg1, OnCallReplaceRequestParam jarg1_, long jarg2, Call jarg2_);
  public final static native long OnCallReplaceRequestParam_newCall_get(long jarg1, OnCallReplaceRequestParam jarg1_);
  public final static native long new_OnCallReplaceRequestParam();
  public final static native void delete_OnCallReplaceRequestParam(long jarg1);
  public final static native void OnCallReplacedParam_newCallId_set(long jarg1, OnCallReplacedParam jarg1_, int jarg2);
  public final static native int OnCallReplacedParam_newCallId_get(long jarg1, OnCallReplacedParam jarg1_);
  public final static native void OnCallReplacedParam_newCall_set(long jarg1, OnCallReplacedParam jarg1_, long jarg2, Call jarg2_);
  public final static native long OnCallReplacedParam_newCall_get(long jarg1, OnCallReplacedParam jarg1_);
  public final static native long new_OnCallReplacedParam();
  public final static native void delete_OnCallReplacedParam(long jarg1);
  public final static native void OnCallRxOfferParam_offer_set(long jarg1, OnCallRxOfferParam jarg1_, long jarg2, SdpSession jarg2_);
  public final static native long OnCallRxOfferParam_offer_get(long jarg1, OnCallRxOfferParam jarg1_);
  public final static native void OnCallRxOfferParam_statusCode_set(long jarg1, OnCallRxOfferParam jarg1_, int jarg2);
  public final static native int OnCallRxOfferParam_statusCode_get(long jarg1, OnCallRxOfferParam jarg1_);
  public final static native void OnCallRxOfferParam_opt_set(long jarg1, OnCallRxOfferParam jarg1_, long jarg2, CallSetting jarg2_);
  public final static native long OnCallRxOfferParam_opt_get(long jarg1, OnCallRxOfferParam jarg1_);
  public final static native long new_OnCallRxOfferParam();
  public final static native void delete_OnCallRxOfferParam(long jarg1);
  public final static native void OnCallRxReinviteParam_offer_set(long jarg1, OnCallRxReinviteParam jarg1_, long jarg2, SdpSession jarg2_);
  public final static native long OnCallRxReinviteParam_offer_get(long jarg1, OnCallRxReinviteParam jarg1_);
  public final static native void OnCallRxReinviteParam_rdata_set(long jarg1, OnCallRxReinviteParam jarg1_, long jarg2, SipRxData jarg2_);
  public final static native long OnCallRxReinviteParam_rdata_get(long jarg1, OnCallRxReinviteParam jarg1_);
  public final static native void OnCallRxReinviteParam_isAsync_set(long jarg1, OnCallRxReinviteParam jarg1_, boolean jarg2);
  public final static native boolean OnCallRxReinviteParam_isAsync_get(long jarg1, OnCallRxReinviteParam jarg1_);
  public final static native void OnCallRxReinviteParam_statusCode_set(long jarg1, OnCallRxReinviteParam jarg1_, int jarg2);
  public final static native int OnCallRxReinviteParam_statusCode_get(long jarg1, OnCallRxReinviteParam jarg1_);
  public final static native void OnCallRxReinviteParam_opt_set(long jarg1, OnCallRxReinviteParam jarg1_, long jarg2, CallSetting jarg2_);
  public final static native long OnCallRxReinviteParam_opt_get(long jarg1, OnCallRxReinviteParam jarg1_);
  public final static native long new_OnCallRxReinviteParam();
  public final static native void delete_OnCallRxReinviteParam(long jarg1);
  public final static native void OnCallTxOfferParam_opt_set(long jarg1, OnCallTxOfferParam jarg1_, long jarg2, CallSetting jarg2_);
  public final static native long OnCallTxOfferParam_opt_get(long jarg1, OnCallTxOfferParam jarg1_);
  public final static native long new_OnCallTxOfferParam();
  public final static native void delete_OnCallTxOfferParam(long jarg1);
  public final static native void OnCallRedirectedParam_targetUri_set(long jarg1, OnCallRedirectedParam jarg1_, String jarg2);
  public final static native String OnCallRedirectedParam_targetUri_get(long jarg1, OnCallRedirectedParam jarg1_);
  public final static native void OnCallRedirectedParam_e_set(long jarg1, OnCallRedirectedParam jarg1_, long jarg2, SipEvent jarg2_);
  public final static native long OnCallRedirectedParam_e_get(long jarg1, OnCallRedirectedParam jarg1_);
  public final static native long new_OnCallRedirectedParam();
  public final static native void delete_OnCallRedirectedParam(long jarg1);
  public final static native void OnCallMediaEventParam_medIdx_set(long jarg1, OnCallMediaEventParam jarg1_, long jarg2);
  public final static native long OnCallMediaEventParam_medIdx_get(long jarg1, OnCallMediaEventParam jarg1_);
  public final static native void OnCallMediaEventParam_ev_set(long jarg1, OnCallMediaEventParam jarg1_, long jarg2, MediaEvent jarg2_);
  public final static native long OnCallMediaEventParam_ev_get(long jarg1, OnCallMediaEventParam jarg1_);
  public final static native long new_OnCallMediaEventParam();
  public final static native void delete_OnCallMediaEventParam(long jarg1);
  public final static native void OnCallMediaTransportStateParam_medIdx_set(long jarg1, OnCallMediaTransportStateParam jarg1_, long jarg2);
  public final static native long OnCallMediaTransportStateParam_medIdx_get(long jarg1, OnCallMediaTransportStateParam jarg1_);
  public final static native void OnCallMediaTransportStateParam_state_set(long jarg1, OnCallMediaTransportStateParam jarg1_, int jarg2);
  public final static native int OnCallMediaTransportStateParam_state_get(long jarg1, OnCallMediaTransportStateParam jarg1_);
  public final static native void OnCallMediaTransportStateParam_status_set(long jarg1, OnCallMediaTransportStateParam jarg1_, int jarg2);
  public final static native int OnCallMediaTransportStateParam_status_get(long jarg1, OnCallMediaTransportStateParam jarg1_);
  public final static native void OnCallMediaTransportStateParam_sipErrorCode_set(long jarg1, OnCallMediaTransportStateParam jarg1_, int jarg2);
  public final static native int OnCallMediaTransportStateParam_sipErrorCode_get(long jarg1, OnCallMediaTransportStateParam jarg1_);
  public final static native long new_OnCallMediaTransportStateParam();
  public final static native void delete_OnCallMediaTransportStateParam(long jarg1);
  public final static native void OnCreateMediaTransportParam_mediaIdx_set(long jarg1, OnCreateMediaTransportParam jarg1_, long jarg2);
  public final static native long OnCreateMediaTransportParam_mediaIdx_get(long jarg1, OnCreateMediaTransportParam jarg1_);
  public final static native void OnCreateMediaTransportParam_mediaTp_set(long jarg1, OnCreateMediaTransportParam jarg1_, long jarg2);
  public final static native long OnCreateMediaTransportParam_mediaTp_get(long jarg1, OnCreateMediaTransportParam jarg1_);
  public final static native void OnCreateMediaTransportParam_flags_set(long jarg1, OnCreateMediaTransportParam jarg1_, long jarg2);
  public final static native long OnCreateMediaTransportParam_flags_get(long jarg1, OnCreateMediaTransportParam jarg1_);
  public final static native long new_OnCreateMediaTransportParam();
  public final static native void delete_OnCreateMediaTransportParam(long jarg1);
  public final static native void OnCreateMediaTransportSrtpParam_mediaIdx_set(long jarg1, OnCreateMediaTransportSrtpParam jarg1_, long jarg2);
  public final static native long OnCreateMediaTransportSrtpParam_mediaIdx_get(long jarg1, OnCreateMediaTransportSrtpParam jarg1_);
  public final static native void OnCreateMediaTransportSrtpParam_srtpUse_set(long jarg1, OnCreateMediaTransportSrtpParam jarg1_, int jarg2);
  public final static native int OnCreateMediaTransportSrtpParam_srtpUse_get(long jarg1, OnCreateMediaTransportSrtpParam jarg1_);
  public final static native void OnCreateMediaTransportSrtpParam_cryptos_set(long jarg1, OnCreateMediaTransportSrtpParam jarg1_, long jarg2, SrtpCryptoVector jarg2_);
  public final static native long OnCreateMediaTransportSrtpParam_cryptos_get(long jarg1, OnCreateMediaTransportSrtpParam jarg1_);
  public final static native long new_OnCreateMediaTransportSrtpParam();
  public final static native void delete_OnCreateMediaTransportSrtpParam(long jarg1);
  public final static native void CallOpParam_opt_set(long jarg1, CallOpParam jarg1_, long jarg2, CallSetting jarg2_);
  public final static native long CallOpParam_opt_get(long jarg1, CallOpParam jarg1_);
  public final static native void CallOpParam_statusCode_set(long jarg1, CallOpParam jarg1_, int jarg2);
  public final static native int CallOpParam_statusCode_get(long jarg1, CallOpParam jarg1_);
  public final static native void CallOpParam_reason_set(long jarg1, CallOpParam jarg1_, String jarg2);
  public final static native String CallOpParam_reason_get(long jarg1, CallOpParam jarg1_);
  public final static native void CallOpParam_options_set(long jarg1, CallOpParam jarg1_, long jarg2);
  public final static native long CallOpParam_options_get(long jarg1, CallOpParam jarg1_);
  public final static native void CallOpParam_txOption_set(long jarg1, CallOpParam jarg1_, long jarg2, SipTxOption jarg2_);
  public final static native long CallOpParam_txOption_get(long jarg1, CallOpParam jarg1_);
  public final static native void CallOpParam_sdp_set(long jarg1, CallOpParam jarg1_, long jarg2, SdpSession jarg2_);
  public final static native long CallOpParam_sdp_get(long jarg1, CallOpParam jarg1_);
  public final static native long new_CallOpParam__SWIG_0(boolean jarg1);
  public final static native long new_CallOpParam__SWIG_1();
  public final static native void delete_CallOpParam(long jarg1);
  public final static native void CallSendRequestParam_method_set(long jarg1, CallSendRequestParam jarg1_, String jarg2);
  public final static native String CallSendRequestParam_method_get(long jarg1, CallSendRequestParam jarg1_);
  public final static native void CallSendRequestParam_txOption_set(long jarg1, CallSendRequestParam jarg1_, long jarg2, SipTxOption jarg2_);
  public final static native long CallSendRequestParam_txOption_get(long jarg1, CallSendRequestParam jarg1_);
  public final static native long new_CallSendRequestParam();
  public final static native void delete_CallSendRequestParam(long jarg1);
  public final static native void CallVidSetStreamParam_medIdx_set(long jarg1, CallVidSetStreamParam jarg1_, int jarg2);
  public final static native int CallVidSetStreamParam_medIdx_get(long jarg1, CallVidSetStreamParam jarg1_);
  public final static native void CallVidSetStreamParam_dir_set(long jarg1, CallVidSetStreamParam jarg1_, int jarg2);
  public final static native int CallVidSetStreamParam_dir_get(long jarg1, CallVidSetStreamParam jarg1_);
  public final static native void CallVidSetStreamParam_capDev_set(long jarg1, CallVidSetStreamParam jarg1_, int jarg2);
  public final static native int CallVidSetStreamParam_capDev_get(long jarg1, CallVidSetStreamParam jarg1_);
  public final static native long new_CallVidSetStreamParam();
  public final static native void delete_CallVidSetStreamParam(long jarg1);
  public final static native void CallSendDtmfParam_method_set(long jarg1, CallSendDtmfParam jarg1_, int jarg2);
  public final static native int CallSendDtmfParam_method_get(long jarg1, CallSendDtmfParam jarg1_);
  public final static native void CallSendDtmfParam_duration_set(long jarg1, CallSendDtmfParam jarg1_, long jarg2);
  public final static native long CallSendDtmfParam_duration_get(long jarg1, CallSendDtmfParam jarg1_);
  public final static native void CallSendDtmfParam_digits_set(long jarg1, CallSendDtmfParam jarg1_, String jarg2);
  public final static native String CallSendDtmfParam_digits_get(long jarg1, CallSendDtmfParam jarg1_);
  public final static native long new_CallSendDtmfParam();
  public final static native void delete_CallSendDtmfParam(long jarg1);
  public final static native long new_Call__SWIG_0(long jarg1, Account jarg1_, int jarg2);
  public final static native long new_Call__SWIG_1(long jarg1, Account jarg1_);
  public final static native void delete_Call(long jarg1);
  public final static native long Call_getInfo(long jarg1, Call jarg1_) throws java.lang.Exception;
  public final static native boolean Call_isActive(long jarg1, Call jarg1_);
  public final static native int Call_getId(long jarg1, Call jarg1_);
  public final static native long Call_lookup(int jarg1);
  public final static native boolean Call_hasMedia(long jarg1, Call jarg1_);
  public final static native long Call_getMedia(long jarg1, Call jarg1_, long jarg2);
  public final static native long Call_getAudioMedia(long jarg1, Call jarg1_, int jarg2) throws java.lang.Exception;
  public final static native long Call_getEncodingVideoMedia(long jarg1, Call jarg1_, int jarg2) throws java.lang.Exception;
  public final static native long Call_getDecodingVideoMedia(long jarg1, Call jarg1_, int jarg2) throws java.lang.Exception;
  public final static native int Call_remoteHasCap(long jarg1, Call jarg1_, int jarg2, String jarg3, String jarg4);
  public final static native void Call_setUserData(long jarg1, Call jarg1_, long jarg2);
  public final static native long Call_getUserData(long jarg1, Call jarg1_);
  public final static native int Call_getRemNatType(long jarg1, Call jarg1_) throws java.lang.Exception;
  public final static native void Call_makeCall(long jarg1, Call jarg1_, String jarg2, long jarg3, CallOpParam jarg3_) throws java.lang.Exception;
  public final static native void Call_answer(long jarg1, Call jarg1_, long jarg2, CallOpParam jarg2_) throws java.lang.Exception;
  public final static native void Call_hangup(long jarg1, Call jarg1_, long jarg2, CallOpParam jarg2_) throws java.lang.Exception;
  public final static native void Call_setHold(long jarg1, Call jarg1_, long jarg2, CallOpParam jarg2_) throws java.lang.Exception;
  public final static native void Call_reinvite(long jarg1, Call jarg1_, long jarg2, CallOpParam jarg2_) throws java.lang.Exception;
  public final static native void Call_update(long jarg1, Call jarg1_, long jarg2, CallOpParam jarg2_) throws java.lang.Exception;
  public final static native void Call_xfer(long jarg1, Call jarg1_, String jarg2, long jarg3, CallOpParam jarg3_) throws java.lang.Exception;
  public final static native void Call_xferReplaces(long jarg1, Call jarg1_, long jarg2, Call jarg2_, long jarg3, CallOpParam jarg3_) throws java.lang.Exception;
  public final static native void Call_processRedirect(long jarg1, Call jarg1_, int jarg2) throws java.lang.Exception;
  public final static native void Call_dialDtmf(long jarg1, Call jarg1_, String jarg2) throws java.lang.Exception;
  public final static native void Call_sendDtmf(long jarg1, Call jarg1_, long jarg2, CallSendDtmfParam jarg2_) throws java.lang.Exception;
  public final static native void Call_sendInstantMessage(long jarg1, Call jarg1_, long jarg2, SendInstantMessageParam jarg2_) throws java.lang.Exception;
  public final static native void Call_sendTypingIndication(long jarg1, Call jarg1_, long jarg2, SendTypingIndicationParam jarg2_) throws java.lang.Exception;
  public final static native void Call_sendRequest(long jarg1, Call jarg1_, long jarg2, CallSendRequestParam jarg2_) throws java.lang.Exception;
  public final static native String Call_dump(long jarg1, Call jarg1_, boolean jarg2, String jarg3) throws java.lang.Exception;
  public final static native int Call_vidGetStreamIdx(long jarg1, Call jarg1_);
  public final static native boolean Call_vidStreamIsRunning(long jarg1, Call jarg1_, int jarg2, int jarg3);
  public final static native void Call_vidSetStream(long jarg1, Call jarg1_, int jarg2, long jarg3, CallVidSetStreamParam jarg3_) throws java.lang.Exception;
  public final static native void Call_audStreamModifyCodecParam(long jarg1, Call jarg1_, int jarg2, long jarg3, CodecParam jarg3_) throws java.lang.Exception;
  public final static native long Call_getStreamInfo(long jarg1, Call jarg1_, long jarg2) throws java.lang.Exception;
  public final static native long Call_getStreamStat(long jarg1, Call jarg1_, long jarg2) throws java.lang.Exception;
  public final static native long Call_getMedTransportInfo(long jarg1, Call jarg1_, long jarg2) throws java.lang.Exception;
  public final static native void Call_processMediaUpdate(long jarg1, Call jarg1_, long jarg2, OnCallMediaStateParam jarg2_);
  public final static native void Call_processStateChange(long jarg1, Call jarg1_, long jarg2, OnCallStateParam jarg2_);
  public final static native void Call_onCallState(long jarg1, Call jarg1_, long jarg2, OnCallStateParam jarg2_);
  public final static native void Call_onCallStateSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCallStateParam jarg2_);
  public final static native void Call_onCallTsxState(long jarg1, Call jarg1_, long jarg2, OnCallTsxStateParam jarg2_);
  public final static native void Call_onCallTsxStateSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCallTsxStateParam jarg2_);
  public final static native void Call_onCallMediaState(long jarg1, Call jarg1_, long jarg2, OnCallMediaStateParam jarg2_);
  public final static native void Call_onCallMediaStateSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCallMediaStateParam jarg2_);
  public final static native void Call_onCallSdpCreated(long jarg1, Call jarg1_, long jarg2, OnCallSdpCreatedParam jarg2_);
  public final static native void Call_onCallSdpCreatedSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCallSdpCreatedParam jarg2_);
  public final static native void Call_onStreamPreCreate(long jarg1, Call jarg1_, long jarg2, OnStreamPreCreateParam jarg2_);
  public final static native void Call_onStreamPreCreateSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnStreamPreCreateParam jarg2_);
  public final static native void Call_onStreamCreated(long jarg1, Call jarg1_, long jarg2, OnStreamCreatedParam jarg2_);
  public final static native void Call_onStreamCreatedSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnStreamCreatedParam jarg2_);
  public final static native void Call_onStreamDestroyed(long jarg1, Call jarg1_, long jarg2, OnStreamDestroyedParam jarg2_);
  public final static native void Call_onStreamDestroyedSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnStreamDestroyedParam jarg2_);
  public final static native void Call_onDtmfDigit(long jarg1, Call jarg1_, long jarg2, OnDtmfDigitParam jarg2_);
  public final static native void Call_onDtmfDigitSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnDtmfDigitParam jarg2_);
  public final static native void Call_onDtmfEvent(long jarg1, Call jarg1_, long jarg2, OnDtmfEventParam jarg2_);
  public final static native void Call_onDtmfEventSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnDtmfEventParam jarg2_);
  public final static native void Call_onCallTransferRequest(long jarg1, Call jarg1_, long jarg2, OnCallTransferRequestParam jarg2_);
  public final static native void Call_onCallTransferRequestSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCallTransferRequestParam jarg2_);
  public final static native void Call_onCallTransferStatus(long jarg1, Call jarg1_, long jarg2, OnCallTransferStatusParam jarg2_);
  public final static native void Call_onCallTransferStatusSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCallTransferStatusParam jarg2_);
  public final static native void Call_onCallReplaceRequest(long jarg1, Call jarg1_, long jarg2, OnCallReplaceRequestParam jarg2_);
  public final static native void Call_onCallReplaceRequestSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCallReplaceRequestParam jarg2_);
  public final static native void Call_onCallReplaced(long jarg1, Call jarg1_, long jarg2, OnCallReplacedParam jarg2_);
  public final static native void Call_onCallReplacedSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCallReplacedParam jarg2_);
  public final static native void Call_onCallRxOffer(long jarg1, Call jarg1_, long jarg2, OnCallRxOfferParam jarg2_);
  public final static native void Call_onCallRxOfferSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCallRxOfferParam jarg2_);
  public final static native void Call_onCallRxReinvite(long jarg1, Call jarg1_, long jarg2, OnCallRxReinviteParam jarg2_);
  public final static native void Call_onCallRxReinviteSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCallRxReinviteParam jarg2_);
  public final static native void Call_onCallTxOffer(long jarg1, Call jarg1_, long jarg2, OnCallTxOfferParam jarg2_);
  public final static native void Call_onCallTxOfferSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCallTxOfferParam jarg2_);
  public final static native void Call_onInstantMessage(long jarg1, Call jarg1_, long jarg2, OnInstantMessageParam jarg2_);
  public final static native void Call_onInstantMessageSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnInstantMessageParam jarg2_);
  public final static native void Call_onInstantMessageStatus(long jarg1, Call jarg1_, long jarg2, OnInstantMessageStatusParam jarg2_);
  public final static native void Call_onInstantMessageStatusSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnInstantMessageStatusParam jarg2_);
  public final static native void Call_onTypingIndication(long jarg1, Call jarg1_, long jarg2, OnTypingIndicationParam jarg2_);
  public final static native void Call_onTypingIndicationSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnTypingIndicationParam jarg2_);
  public final static native int Call_onCallRedirected(long jarg1, Call jarg1_, long jarg2, OnCallRedirectedParam jarg2_);
  public final static native int Call_onCallRedirectedSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCallRedirectedParam jarg2_);
  public final static native void Call_onCallMediaTransportState(long jarg1, Call jarg1_, long jarg2, OnCallMediaTransportStateParam jarg2_);
  public final static native void Call_onCallMediaTransportStateSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCallMediaTransportStateParam jarg2_);
  public final static native void Call_onCallMediaEvent(long jarg1, Call jarg1_, long jarg2, OnCallMediaEventParam jarg2_);
  public final static native void Call_onCallMediaEventSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCallMediaEventParam jarg2_);
  public final static native void Call_onCreateMediaTransport(long jarg1, Call jarg1_, long jarg2, OnCreateMediaTransportParam jarg2_);
  public final static native void Call_onCreateMediaTransportSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCreateMediaTransportParam jarg2_);
  public final static native void Call_onCreateMediaTransportSrtp(long jarg1, Call jarg1_, long jarg2, OnCreateMediaTransportSrtpParam jarg2_);
  public final static native void Call_onCreateMediaTransportSrtpSwigExplicitCall(long jarg1, Call jarg1_, long jarg2, OnCreateMediaTransportSrtpParam jarg2_);
  public final static native void Call_director_connect(Call obj, long cptr, boolean mem_own, boolean weak_global);
  public final static native void Call_change_ownership(Call obj, long cptr, boolean take_or_release);
  public final static native long new_JsonDocument();
  public final static native void delete_JsonDocument(long jarg1);
  public final static native void JsonDocument_loadFile(long jarg1, JsonDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native void JsonDocument_loadString(long jarg1, JsonDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native void JsonDocument_saveFile(long jarg1, JsonDocument jarg1_, String jarg2) throws java.lang.Exception;
  public final static native String JsonDocument_saveString(long jarg1, JsonDocument jarg1_) throws java.lang.Exception;
  public final static native long JsonDocument_getRootContainer(long jarg1, JsonDocument jarg1_);
  public final static native void OnNatDetectionCompleteParam_status_set(long jarg1, OnNatDetectionCompleteParam jarg1_, int jarg2);
  public final static native int OnNatDetectionCompleteParam_status_get(long jarg1, OnNatDetectionCompleteParam jarg1_);
  public final static native void OnNatDetectionCompleteParam_reason_set(long jarg1, OnNatDetectionCompleteParam jarg1_, String jarg2);
  public final static native String OnNatDetectionCompleteParam_reason_get(long jarg1, OnNatDetectionCompleteParam jarg1_);
  public final static native void OnNatDetectionCompleteParam_natType_set(long jarg1, OnNatDetectionCompleteParam jarg1_, int jarg2);
  public final static native int OnNatDetectionCompleteParam_natType_get(long jarg1, OnNatDetectionCompleteParam jarg1_);
  public final static native void OnNatDetectionCompleteParam_natTypeName_set(long jarg1, OnNatDetectionCompleteParam jarg1_, String jarg2);
  public final static native String OnNatDetectionCompleteParam_natTypeName_get(long jarg1, OnNatDetectionCompleteParam jarg1_);
  public final static native long new_OnNatDetectionCompleteParam();
  public final static native void delete_OnNatDetectionCompleteParam(long jarg1);
  public final static native void OnNatCheckStunServersCompleteParam_userData_set(long jarg1, OnNatCheckStunServersCompleteParam jarg1_, long jarg2);
  public final static native long OnNatCheckStunServersCompleteParam_userData_get(long jarg1, OnNatCheckStunServersCompleteParam jarg1_);
  public final static native void OnNatCheckStunServersCompleteParam_status_set(long jarg1, OnNatCheckStunServersCompleteParam jarg1_, int jarg2);
  public final static native int OnNatCheckStunServersCompleteParam_status_get(long jarg1, OnNatCheckStunServersCompleteParam jarg1_);
  public final static native void OnNatCheckStunServersCompleteParam_name_set(long jarg1, OnNatCheckStunServersCompleteParam jarg1_, String jarg2);
  public final static native String OnNatCheckStunServersCompleteParam_name_get(long jarg1, OnNatCheckStunServersCompleteParam jarg1_);
  public final static native void OnNatCheckStunServersCompleteParam_addr_set(long jarg1, OnNatCheckStunServersCompleteParam jarg1_, String jarg2);
  public final static native String OnNatCheckStunServersCompleteParam_addr_get(long jarg1, OnNatCheckStunServersCompleteParam jarg1_);
  public final static native long new_OnNatCheckStunServersCompleteParam();
  public final static native void delete_OnNatCheckStunServersCompleteParam(long jarg1);
  public final static native void OnTimerParam_userData_set(long jarg1, OnTimerParam jarg1_, long jarg2);
  public final static native long OnTimerParam_userData_get(long jarg1, OnTimerParam jarg1_);
  public final static native void OnTimerParam_msecDelay_set(long jarg1, OnTimerParam jarg1_, long jarg2);
  public final static native long OnTimerParam_msecDelay_get(long jarg1, OnTimerParam jarg1_);
  public final static native long new_OnTimerParam();
  public final static native void delete_OnTimerParam(long jarg1);
  public final static native void SslCertName_type_set(long jarg1, SslCertName jarg1_, int jarg2);
  public final static native int SslCertName_type_get(long jarg1, SslCertName jarg1_);
  public final static native void SslCertName_name_set(long jarg1, SslCertName jarg1_, String jarg2);
  public final static native String SslCertName_name_get(long jarg1, SslCertName jarg1_);
  public final static native long new_SslCertName();
  public final static native void delete_SslCertName(long jarg1);
  public final static native void SslCertInfo_version_set(long jarg1, SslCertInfo jarg1_, long jarg2);
  public final static native long SslCertInfo_version_get(long jarg1, SslCertInfo jarg1_);
  public final static native void SslCertInfo_serialNo_set(long jarg1, SslCertInfo jarg1_, long jarg2);
  public final static native long SslCertInfo_serialNo_get(long jarg1, SslCertInfo jarg1_);
  public final static native void SslCertInfo_subjectCn_set(long jarg1, SslCertInfo jarg1_, String jarg2);
  public final static native String SslCertInfo_subjectCn_get(long jarg1, SslCertInfo jarg1_);
  public final static native void SslCertInfo_subjectInfo_set(long jarg1, SslCertInfo jarg1_, String jarg2);
  public final static native String SslCertInfo_subjectInfo_get(long jarg1, SslCertInfo jarg1_);
  public final static native void SslCertInfo_issuerCn_set(long jarg1, SslCertInfo jarg1_, String jarg2);
  public final static native String SslCertInfo_issuerCn_get(long jarg1, SslCertInfo jarg1_);
  public final static native void SslCertInfo_issuerInfo_set(long jarg1, SslCertInfo jarg1_, String jarg2);
  public final static native String SslCertInfo_issuerInfo_get(long jarg1, SslCertInfo jarg1_);
  public final static native void SslCertInfo_validityStart_set(long jarg1, SslCertInfo jarg1_, long jarg2, TimeVal jarg2_);
  public final static native long SslCertInfo_validityStart_get(long jarg1, SslCertInfo jarg1_);
  public final static native void SslCertInfo_validityEnd_set(long jarg1, SslCertInfo jarg1_, long jarg2, TimeVal jarg2_);
  public final static native long SslCertInfo_validityEnd_get(long jarg1, SslCertInfo jarg1_);
  public final static native void SslCertInfo_validityGmt_set(long jarg1, SslCertInfo jarg1_, boolean jarg2);
  public final static native boolean SslCertInfo_validityGmt_get(long jarg1, SslCertInfo jarg1_);
  public final static native void SslCertInfo_subjectAltName_set(long jarg1, SslCertInfo jarg1_, long jarg2, SslCertNameVector jarg2_);
  public final static native long SslCertInfo_subjectAltName_get(long jarg1, SslCertInfo jarg1_);
  public final static native void SslCertInfo_raw_set(long jarg1, SslCertInfo jarg1_, String jarg2);
  public final static native String SslCertInfo_raw_get(long jarg1, SslCertInfo jarg1_);
  public final static native long new_SslCertInfo();
  public final static native boolean SslCertInfo_isEmpty(long jarg1, SslCertInfo jarg1_);
  public final static native void delete_SslCertInfo(long jarg1);
  public final static native void TlsInfo_established_set(long jarg1, TlsInfo jarg1_, boolean jarg2);
  public final static native boolean TlsInfo_established_get(long jarg1, TlsInfo jarg1_);
  public final static native void TlsInfo_protocol_set(long jarg1, TlsInfo jarg1_, long jarg2);
  public final static native long TlsInfo_protocol_get(long jarg1, TlsInfo jarg1_);
  public final static native void TlsInfo_cipher_set(long jarg1, TlsInfo jarg1_, int jarg2);
  public final static native int TlsInfo_cipher_get(long jarg1, TlsInfo jarg1_);
  public final static native void TlsInfo_cipherName_set(long jarg1, TlsInfo jarg1_, String jarg2);
  public final static native String TlsInfo_cipherName_get(long jarg1, TlsInfo jarg1_);
  public final static native void TlsInfo_localAddr_set(long jarg1, TlsInfo jarg1_, String jarg2);
  public final static native String TlsInfo_localAddr_get(long jarg1, TlsInfo jarg1_);
  public final static native void TlsInfo_remoteAddr_set(long jarg1, TlsInfo jarg1_, String jarg2);
  public final static native String TlsInfo_remoteAddr_get(long jarg1, TlsInfo jarg1_);
  public final static native void TlsInfo_localCertInfo_set(long jarg1, TlsInfo jarg1_, long jarg2, SslCertInfo jarg2_);
  public final static native long TlsInfo_localCertInfo_get(long jarg1, TlsInfo jarg1_);
  public final static native void TlsInfo_remoteCertInfo_set(long jarg1, TlsInfo jarg1_, long jarg2, SslCertInfo jarg2_);
  public final static native long TlsInfo_remoteCertInfo_get(long jarg1, TlsInfo jarg1_);
  public final static native void TlsInfo_verifyStatus_set(long jarg1, TlsInfo jarg1_, long jarg2);
  public final static native long TlsInfo_verifyStatus_get(long jarg1, TlsInfo jarg1_);
  public final static native void TlsInfo_verifyMsgs_set(long jarg1, TlsInfo jarg1_, long jarg2, StringVector jarg2_);
  public final static native long TlsInfo_verifyMsgs_get(long jarg1, TlsInfo jarg1_);
  public final static native long new_TlsInfo();
  public final static native boolean TlsInfo_isEmpty(long jarg1, TlsInfo jarg1_);
  public final static native void delete_TlsInfo(long jarg1);
  public final static native void OnTransportStateParam_hnd_set(long jarg1, OnTransportStateParam jarg1_, long jarg2);
  public final static native long OnTransportStateParam_hnd_get(long jarg1, OnTransportStateParam jarg1_);
  public final static native void OnTransportStateParam_type_set(long jarg1, OnTransportStateParam jarg1_, String jarg2);
  public final static native String OnTransportStateParam_type_get(long jarg1, OnTransportStateParam jarg1_);
  public final static native void OnTransportStateParam_state_set(long jarg1, OnTransportStateParam jarg1_, int jarg2);
  public final static native int OnTransportStateParam_state_get(long jarg1, OnTransportStateParam jarg1_);
  public final static native void OnTransportStateParam_lastError_set(long jarg1, OnTransportStateParam jarg1_, int jarg2);
  public final static native int OnTransportStateParam_lastError_get(long jarg1, OnTransportStateParam jarg1_);
  public final static native void OnTransportStateParam_tlsInfo_set(long jarg1, OnTransportStateParam jarg1_, long jarg2, TlsInfo jarg2_);
  public final static native long OnTransportStateParam_tlsInfo_get(long jarg1, OnTransportStateParam jarg1_);
  public final static native long new_OnTransportStateParam();
  public final static native void delete_OnTransportStateParam(long jarg1);
  public final static native void OnSelectAccountParam_rdata_set(long jarg1, OnSelectAccountParam jarg1_, long jarg2, SipRxData jarg2_);
  public final static native long OnSelectAccountParam_rdata_get(long jarg1, OnSelectAccountParam jarg1_);
  public final static native void OnSelectAccountParam_accountIndex_set(long jarg1, OnSelectAccountParam jarg1_, int jarg2);
  public final static native int OnSelectAccountParam_accountIndex_get(long jarg1, OnSelectAccountParam jarg1_);
  public final static native long new_OnSelectAccountParam();
  public final static native void delete_OnSelectAccountParam(long jarg1);
  public final static native void IpChangeParam_restartListener_set(long jarg1, IpChangeParam jarg1_, boolean jarg2);
  public final static native boolean IpChangeParam_restartListener_get(long jarg1, IpChangeParam jarg1_);
  public final static native void IpChangeParam_restartLisDelay_set(long jarg1, IpChangeParam jarg1_, long jarg2);
  public final static native long IpChangeParam_restartLisDelay_get(long jarg1, IpChangeParam jarg1_);
  public final static native long new_IpChangeParam();
  public final static native void delete_IpChangeParam(long jarg1);
  public final static native void RegProgressParam_isRegister_set(long jarg1, RegProgressParam jarg1_, boolean jarg2);
  public final static native boolean RegProgressParam_isRegister_get(long jarg1, RegProgressParam jarg1_);
  public final static native void RegProgressParam_code_set(long jarg1, RegProgressParam jarg1_, int jarg2);
  public final static native int RegProgressParam_code_get(long jarg1, RegProgressParam jarg1_);
  public final static native long new_RegProgressParam();
  public final static native void delete_RegProgressParam(long jarg1);
  public final static native void OnIpChangeProgressParam_op_set(long jarg1, OnIpChangeProgressParam jarg1_, int jarg2);
  public final static native int OnIpChangeProgressParam_op_get(long jarg1, OnIpChangeProgressParam jarg1_);
  public final static native void OnIpChangeProgressParam_status_set(long jarg1, OnIpChangeProgressParam jarg1_, int jarg2);
  public final static native int OnIpChangeProgressParam_status_get(long jarg1, OnIpChangeProgressParam jarg1_);
  public final static native void OnIpChangeProgressParam_transportId_set(long jarg1, OnIpChangeProgressParam jarg1_, int jarg2);
  public final static native int OnIpChangeProgressParam_transportId_get(long jarg1, OnIpChangeProgressParam jarg1_);
  public final static native void OnIpChangeProgressParam_accId_set(long jarg1, OnIpChangeProgressParam jarg1_, int jarg2);
  public final static native int OnIpChangeProgressParam_accId_get(long jarg1, OnIpChangeProgressParam jarg1_);
  public final static native void OnIpChangeProgressParam_callId_set(long jarg1, OnIpChangeProgressParam jarg1_, int jarg2);
  public final static native int OnIpChangeProgressParam_callId_get(long jarg1, OnIpChangeProgressParam jarg1_);
  public final static native void OnIpChangeProgressParam_regInfo_set(long jarg1, OnIpChangeProgressParam jarg1_, long jarg2, RegProgressParam jarg2_);
  public final static native long OnIpChangeProgressParam_regInfo_get(long jarg1, OnIpChangeProgressParam jarg1_);
  public final static native long new_OnIpChangeProgressParam();
  public final static native void delete_OnIpChangeProgressParam(long jarg1);
  public final static native void OnMediaEventParam_ev_set(long jarg1, OnMediaEventParam jarg1_, long jarg2, MediaEvent jarg2_);
  public final static native long OnMediaEventParam_ev_get(long jarg1, OnMediaEventParam jarg1_);
  public final static native long new_OnMediaEventParam();
  public final static native void delete_OnMediaEventParam(long jarg1);
  public final static native void DigestChallenge_realm_set(long jarg1, DigestChallenge jarg1_, String jarg2);
  public final static native String DigestChallenge_realm_get(long jarg1, DigestChallenge jarg1_);
  public final static native void DigestChallenge_otherParam_set(long jarg1, DigestChallenge jarg1_, long jarg2, StringToStringMap jarg2_);
  public final static native long DigestChallenge_otherParam_get(long jarg1, DigestChallenge jarg1_);
  public final static native void DigestChallenge_domain_set(long jarg1, DigestChallenge jarg1_, String jarg2);
  public final static native String DigestChallenge_domain_get(long jarg1, DigestChallenge jarg1_);
  public final static native void DigestChallenge_nonce_set(long jarg1, DigestChallenge jarg1_, String jarg2);
  public final static native String DigestChallenge_nonce_get(long jarg1, DigestChallenge jarg1_);
  public final static native void DigestChallenge_opaque_set(long jarg1, DigestChallenge jarg1_, String jarg2);
  public final static native String DigestChallenge_opaque_get(long jarg1, DigestChallenge jarg1_);
  public final static native void DigestChallenge_stale_set(long jarg1, DigestChallenge jarg1_, int jarg2);
  public final static native int DigestChallenge_stale_get(long jarg1, DigestChallenge jarg1_);
  public final static native void DigestChallenge_algorithm_set(long jarg1, DigestChallenge jarg1_, String jarg2);
  public final static native String DigestChallenge_algorithm_get(long jarg1, DigestChallenge jarg1_);
  public final static native void DigestChallenge_qop_set(long jarg1, DigestChallenge jarg1_, String jarg2);
  public final static native String DigestChallenge_qop_get(long jarg1, DigestChallenge jarg1_);
  public final static native long new_DigestChallenge();
  public final static native void delete_DigestChallenge(long jarg1);
  public final static native void DigestCredential_realm_set(long jarg1, DigestCredential jarg1_, String jarg2);
  public final static native String DigestCredential_realm_get(long jarg1, DigestCredential jarg1_);
  public final static native void DigestCredential_otherParam_set(long jarg1, DigestCredential jarg1_, long jarg2, StringToStringMap jarg2_);
  public final static native long DigestCredential_otherParam_get(long jarg1, DigestCredential jarg1_);
  public final static native void DigestCredential_username_set(long jarg1, DigestCredential jarg1_, String jarg2);
  public final static native String DigestCredential_username_get(long jarg1, DigestCredential jarg1_);
  public final static native void DigestCredential_nonce_set(long jarg1, DigestCredential jarg1_, String jarg2);
  public final static native String DigestCredential_nonce_get(long jarg1, DigestCredential jarg1_);
  public final static native void DigestCredential_uri_set(long jarg1, DigestCredential jarg1_, String jarg2);
  public final static native String DigestCredential_uri_get(long jarg1, DigestCredential jarg1_);
  public final static native void DigestCredential_response_set(long jarg1, DigestCredential jarg1_, String jarg2);
  public final static native String DigestCredential_response_get(long jarg1, DigestCredential jarg1_);
  public final static native void DigestCredential_algorithm_set(long jarg1, DigestCredential jarg1_, String jarg2);
  public final static native String DigestCredential_algorithm_get(long jarg1, DigestCredential jarg1_);
  public final static native void DigestCredential_cnonce_set(long jarg1, DigestCredential jarg1_, String jarg2);
  public final static native String DigestCredential_cnonce_get(long jarg1, DigestCredential jarg1_);
  public final static native void DigestCredential_opaque_set(long jarg1, DigestCredential jarg1_, String jarg2);
  public final static native String DigestCredential_opaque_get(long jarg1, DigestCredential jarg1_);
  public final static native void DigestCredential_qop_set(long jarg1, DigestCredential jarg1_, String jarg2);
  public final static native String DigestCredential_qop_get(long jarg1, DigestCredential jarg1_);
  public final static native void DigestCredential_nc_set(long jarg1, DigestCredential jarg1_, String jarg2);
  public final static native String DigestCredential_nc_get(long jarg1, DigestCredential jarg1_);
  public final static native long new_DigestCredential();
  public final static native void delete_DigestCredential(long jarg1);
  public final static native void OnCredAuthParam_digestChallenge_set(long jarg1, OnCredAuthParam jarg1_, long jarg2, DigestChallenge jarg2_);
  public final static native long OnCredAuthParam_digestChallenge_get(long jarg1, OnCredAuthParam jarg1_);
  public final static native void OnCredAuthParam_credentialInfo_set(long jarg1, OnCredAuthParam jarg1_, long jarg2, AuthCredInfo jarg2_);
  public final static native long OnCredAuthParam_credentialInfo_get(long jarg1, OnCredAuthParam jarg1_);
  public final static native void OnCredAuthParam_method_set(long jarg1, OnCredAuthParam jarg1_, String jarg2);
  public final static native String OnCredAuthParam_method_get(long jarg1, OnCredAuthParam jarg1_);
  public final static native void OnCredAuthParam_digestCredential_set(long jarg1, OnCredAuthParam jarg1_, long jarg2, DigestCredential jarg2_);
  public final static native long OnCredAuthParam_digestCredential_get(long jarg1, OnCredAuthParam jarg1_);
  public final static native long new_OnCredAuthParam();
  public final static native void delete_OnCredAuthParam(long jarg1);
  public final static native void UaConfig_maxCalls_set(long jarg1, UaConfig jarg1_, long jarg2);
  public final static native long UaConfig_maxCalls_get(long jarg1, UaConfig jarg1_);
  public final static native void UaConfig_threadCnt_set(long jarg1, UaConfig jarg1_, long jarg2);
  public final static native long UaConfig_threadCnt_get(long jarg1, UaConfig jarg1_);
  public final static native void UaConfig_mainThreadOnly_set(long jarg1, UaConfig jarg1_, boolean jarg2);
  public final static native boolean UaConfig_mainThreadOnly_get(long jarg1, UaConfig jarg1_);
  public final static native void UaConfig_nameserver_set(long jarg1, UaConfig jarg1_, long jarg2, StringVector jarg2_);
  public final static native long UaConfig_nameserver_get(long jarg1, UaConfig jarg1_);
  public final static native void UaConfig_outboundProxies_set(long jarg1, UaConfig jarg1_, long jarg2, StringVector jarg2_);
  public final static native long UaConfig_outboundProxies_get(long jarg1, UaConfig jarg1_);
  public final static native void UaConfig_userAgent_set(long jarg1, UaConfig jarg1_, String jarg2);
  public final static native String UaConfig_userAgent_get(long jarg1, UaConfig jarg1_);
  public final static native void UaConfig_stunServer_set(long jarg1, UaConfig jarg1_, long jarg2, StringVector jarg2_);
  public final static native long UaConfig_stunServer_get(long jarg1, UaConfig jarg1_);
  public final static native void UaConfig_stunTryIpv6_set(long jarg1, UaConfig jarg1_, boolean jarg2);
  public final static native boolean UaConfig_stunTryIpv6_get(long jarg1, UaConfig jarg1_);
  public final static native void UaConfig_stunIgnoreFailure_set(long jarg1, UaConfig jarg1_, boolean jarg2);
  public final static native boolean UaConfig_stunIgnoreFailure_get(long jarg1, UaConfig jarg1_);
  public final static native void UaConfig_natTypeInSdp_set(long jarg1, UaConfig jarg1_, int jarg2);
  public final static native int UaConfig_natTypeInSdp_get(long jarg1, UaConfig jarg1_);
  public final static native void UaConfig_mwiUnsolicitedEnabled_set(long jarg1, UaConfig jarg1_, boolean jarg2);
  public final static native boolean UaConfig_mwiUnsolicitedEnabled_get(long jarg1, UaConfig jarg1_);
  public final static native void UaConfig_enableUpnp_set(long jarg1, UaConfig jarg1_, boolean jarg2);
  public final static native boolean UaConfig_enableUpnp_get(long jarg1, UaConfig jarg1_);
  public final static native void UaConfig_upnpIfName_set(long jarg1, UaConfig jarg1_, String jarg2);
  public final static native String UaConfig_upnpIfName_get(long jarg1, UaConfig jarg1_);
  public final static native long new_UaConfig();
  public final static native void UaConfig_readObject(long jarg1, UaConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void UaConfig_writeObject(long jarg1, UaConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void delete_UaConfig(long jarg1);
  public final static native void LogEntry_level_set(long jarg1, LogEntry jarg1_, int jarg2);
  public final static native int LogEntry_level_get(long jarg1, LogEntry jarg1_);
  public final static native void LogEntry_msg_set(long jarg1, LogEntry jarg1_, String jarg2);
  public final static native String LogEntry_msg_get(long jarg1, LogEntry jarg1_);
  public final static native void LogEntry_threadId_set(long jarg1, LogEntry jarg1_, int jarg2);
  public final static native int LogEntry_threadId_get(long jarg1, LogEntry jarg1_);
  public final static native void LogEntry_threadName_set(long jarg1, LogEntry jarg1_, String jarg2);
  public final static native String LogEntry_threadName_get(long jarg1, LogEntry jarg1_);
  public final static native long new_LogEntry();
  public final static native void delete_LogEntry(long jarg1);
  public final static native void delete_LogWriter(long jarg1);
  public final static native void LogWriter_write(long jarg1, LogWriter jarg1_, long jarg2, LogEntry jarg2_);
  public final static native long new_LogWriter();
  public final static native void LogWriter_director_connect(LogWriter obj, long cptr, boolean mem_own, boolean weak_global);
  public final static native void LogWriter_change_ownership(LogWriter obj, long cptr, boolean take_or_release);
  public final static native void LogConfig_msgLogging_set(long jarg1, LogConfig jarg1_, long jarg2);
  public final static native long LogConfig_msgLogging_get(long jarg1, LogConfig jarg1_);
  public final static native void LogConfig_level_set(long jarg1, LogConfig jarg1_, long jarg2);
  public final static native long LogConfig_level_get(long jarg1, LogConfig jarg1_);
  public final static native void LogConfig_consoleLevel_set(long jarg1, LogConfig jarg1_, long jarg2);
  public final static native long LogConfig_consoleLevel_get(long jarg1, LogConfig jarg1_);
  public final static native void LogConfig_decor_set(long jarg1, LogConfig jarg1_, long jarg2);
  public final static native long LogConfig_decor_get(long jarg1, LogConfig jarg1_);
  public final static native void LogConfig_filename_set(long jarg1, LogConfig jarg1_, String jarg2);
  public final static native String LogConfig_filename_get(long jarg1, LogConfig jarg1_);
  public final static native void LogConfig_fileFlags_set(long jarg1, LogConfig jarg1_, long jarg2);
  public final static native long LogConfig_fileFlags_get(long jarg1, LogConfig jarg1_);
  public final static native void LogConfig_writer_set(long jarg1, LogConfig jarg1_, long jarg2, LogWriter jarg2_);
  public final static native long LogConfig_writer_get(long jarg1, LogConfig jarg1_);
  public final static native long new_LogConfig();
  public final static native void LogConfig_readObject(long jarg1, LogConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void LogConfig_writeObject(long jarg1, LogConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void delete_LogConfig(long jarg1);
  public final static native void MediaConfig_clockRate_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_clockRate_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_sndClockRate_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_sndClockRate_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_channelCount_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_channelCount_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_audioFramePtime_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_audioFramePtime_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_maxMediaPorts_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_maxMediaPorts_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_hasIoqueue_set(long jarg1, MediaConfig jarg1_, boolean jarg2);
  public final static native boolean MediaConfig_hasIoqueue_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_threadCnt_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_threadCnt_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_quality_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_quality_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_ptime_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_ptime_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_noVad_set(long jarg1, MediaConfig jarg1_, boolean jarg2);
  public final static native boolean MediaConfig_noVad_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_ilbcMode_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_ilbcMode_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_txDropPct_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_txDropPct_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_rxDropPct_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_rxDropPct_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_ecOptions_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_ecOptions_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_ecTailLen_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_ecTailLen_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_sndRecLatency_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_sndRecLatency_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_sndPlayLatency_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_sndPlayLatency_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_jbInit_set(long jarg1, MediaConfig jarg1_, int jarg2);
  public final static native int MediaConfig_jbInit_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_jbMinPre_set(long jarg1, MediaConfig jarg1_, int jarg2);
  public final static native int MediaConfig_jbMinPre_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_jbMaxPre_set(long jarg1, MediaConfig jarg1_, int jarg2);
  public final static native int MediaConfig_jbMaxPre_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_jbMax_set(long jarg1, MediaConfig jarg1_, int jarg2);
  public final static native int MediaConfig_jbMax_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_jbDiscardAlgo_set(long jarg1, MediaConfig jarg1_, long jarg2);
  public final static native long MediaConfig_jbDiscardAlgo_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_sndAutoCloseTime_set(long jarg1, MediaConfig jarg1_, int jarg2);
  public final static native int MediaConfig_sndAutoCloseTime_get(long jarg1, MediaConfig jarg1_);
  public final static native void MediaConfig_vidPreviewEnableNative_set(long jarg1, MediaConfig jarg1_, boolean jarg2);
  public final static native boolean MediaConfig_vidPreviewEnableNative_get(long jarg1, MediaConfig jarg1_);
  public final static native long new_MediaConfig();
  public final static native void MediaConfig_readObject(long jarg1, MediaConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void MediaConfig_writeObject(long jarg1, MediaConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void delete_MediaConfig(long jarg1);
  public final static native void EpConfig_uaConfig_set(long jarg1, EpConfig jarg1_, long jarg2, UaConfig jarg2_);
  public final static native long EpConfig_uaConfig_get(long jarg1, EpConfig jarg1_);
  public final static native void EpConfig_logConfig_set(long jarg1, EpConfig jarg1_, long jarg2, LogConfig jarg2_);
  public final static native long EpConfig_logConfig_get(long jarg1, EpConfig jarg1_);
  public final static native void EpConfig_medConfig_set(long jarg1, EpConfig jarg1_, long jarg2, MediaConfig jarg2_);
  public final static native long EpConfig_medConfig_get(long jarg1, EpConfig jarg1_);
  public final static native void EpConfig_readObject(long jarg1, EpConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native void EpConfig_writeObject(long jarg1, EpConfig jarg1_, long jarg2, ContainerNode jarg2_) throws java.lang.Exception;
  public final static native long new_EpConfig();
  public final static native void delete_EpConfig(long jarg1);
  public final static native void PendingJob_execute(long jarg1, PendingJob jarg1_, boolean jarg2);
  public final static native void delete_PendingJob(long jarg1);
  public final static native long Endpoint_instance() throws java.lang.Exception;
  public final static native long new_Endpoint();
  public final static native void delete_Endpoint(long jarg1);
  public final static native long Endpoint_libVersion(long jarg1, Endpoint jarg1_);
  public final static native void Endpoint_libCreate(long jarg1, Endpoint jarg1_) throws java.lang.Exception;
  public final static native int Endpoint_libGetState(long jarg1, Endpoint jarg1_);
  public final static native void Endpoint_libInit(long jarg1, Endpoint jarg1_, long jarg2, EpConfig jarg2_) throws java.lang.Exception;
  public final static native void Endpoint_libStart(long jarg1, Endpoint jarg1_) throws java.lang.Exception;
  public final static native void Endpoint_libRegisterThread(long jarg1, Endpoint jarg1_, String jarg2) throws java.lang.Exception;
  public final static native boolean Endpoint_libIsThreadRegistered(long jarg1, Endpoint jarg1_);
  public final static native void Endpoint_libStopWorkerThreads(long jarg1, Endpoint jarg1_);
  public final static native int Endpoint_libHandleEvents(long jarg1, Endpoint jarg1_, long jarg2);
  public final static native void Endpoint_libDestroy___SWIG_0(long jarg1, Endpoint jarg1_, long jarg2) throws java.lang.Exception;
  public final static native void Endpoint_libDestroy___SWIG_1(long jarg1, Endpoint jarg1_) throws java.lang.Exception;
  public final static native String Endpoint_utilStrError(long jarg1, Endpoint jarg1_, int jarg2);
  public final static native void Endpoint_utilLogWrite__SWIG_0(long jarg1, Endpoint jarg1_, int jarg2, String jarg3, String jarg4);
  public final static native void Endpoint_utilLogWrite__SWIG_1(long jarg1, Endpoint jarg1_, long jarg2, LogEntry jarg2_);
  public final static native int Endpoint_utilVerifySipUri(long jarg1, Endpoint jarg1_, String jarg2);
  public final static native int Endpoint_utilVerifyUri(long jarg1, Endpoint jarg1_, String jarg2);
  public final static native long Endpoint_utilTimerSchedule(long jarg1, Endpoint jarg1_, long jarg2, long jarg3) throws java.lang.Exception;
  public final static native void Endpoint_utilTimerCancel(long jarg1, Endpoint jarg1_, long jarg2);
  public final static native void Endpoint_utilAddPendingJob(long jarg1, Endpoint jarg1_, long jarg2, PendingJob jarg2_);
  public final static native long Endpoint_utilSslGetAvailableCiphers(long jarg1, Endpoint jarg1_) throws java.lang.Exception;
  public final static native void Endpoint_natDetectType(long jarg1, Endpoint jarg1_) throws java.lang.Exception;
  public final static native int Endpoint_natGetType(long jarg1, Endpoint jarg1_) throws java.lang.Exception;
  public final static native void Endpoint_natUpdateStunServers(long jarg1, Endpoint jarg1_, long jarg2, StringVector jarg2_, boolean jarg3) throws java.lang.Exception;
  public final static native void Endpoint_natCheckStunServers(long jarg1, Endpoint jarg1_, long jarg2, StringVector jarg2_, boolean jarg3, long jarg4) throws java.lang.Exception;
  public final static native void Endpoint_natCancelCheckStunServers__SWIG_0(long jarg1, Endpoint jarg1_, long jarg2, boolean jarg3) throws java.lang.Exception;
  public final static native void Endpoint_natCancelCheckStunServers__SWIG_1(long jarg1, Endpoint jarg1_, long jarg2) throws java.lang.Exception;
  public final static native int Endpoint_transportCreate(long jarg1, Endpoint jarg1_, int jarg2, long jarg3, TransportConfig jarg3_) throws java.lang.Exception;
  public final static native long Endpoint_transportEnum(long jarg1, Endpoint jarg1_) throws java.lang.Exception;
  public final static native long Endpoint_transportGetInfo(long jarg1, Endpoint jarg1_, int jarg2) throws java.lang.Exception;
  public final static native void Endpoint_transportSetEnable(long jarg1, Endpoint jarg1_, int jarg2, boolean jarg3) throws java.lang.Exception;
  public final static native void Endpoint_transportClose(long jarg1, Endpoint jarg1_, int jarg2) throws java.lang.Exception;
  public final static native void Endpoint_transportShutdown(long jarg1, Endpoint jarg1_, long jarg2) throws java.lang.Exception;
  public final static native void Endpoint_hangupAllCalls(long jarg1, Endpoint jarg1_);
  public final static native void Endpoint_mediaAdd(long jarg1, Endpoint jarg1_, long jarg2, AudioMedia jarg2_);
  public final static native void Endpoint_mediaRemove(long jarg1, Endpoint jarg1_, long jarg2, AudioMedia jarg2_);
  public final static native boolean Endpoint_mediaExists(long jarg1, Endpoint jarg1_, long jarg2, AudioMedia jarg2_);
  public final static native long Endpoint_mediaMaxPorts(long jarg1, Endpoint jarg1_);
  public final static native long Endpoint_mediaActivePorts(long jarg1, Endpoint jarg1_);
  public final static native long Endpoint_mediaEnumPorts2(long jarg1, Endpoint jarg1_) throws java.lang.Exception;
  public final static native long Endpoint_mediaEnumVidPorts(long jarg1, Endpoint jarg1_) throws java.lang.Exception;
  public final static native long Endpoint_audDevManager(long jarg1, Endpoint jarg1_);
  public final static native long Endpoint_vidDevManager(long jarg1, Endpoint jarg1_);
  public final static native long Endpoint_codecEnum2(long jarg1, Endpoint jarg1_) throws java.lang.Exception;
  public final static native void Endpoint_codecSetPriority(long jarg1, Endpoint jarg1_, String jarg2, short jarg3) throws java.lang.Exception;
  public final static native long Endpoint_codecGetParam(long jarg1, Endpoint jarg1_, String jarg2) throws java.lang.Exception;
  public final static native void Endpoint_codecSetParam(long jarg1, Endpoint jarg1_, String jarg2, long jarg3, CodecParam jarg3_) throws java.lang.Exception;
  public final static native long Endpoint_videoCodecEnum2(long jarg1, Endpoint jarg1_) throws java.lang.Exception;
  public final static native void Endpoint_videoCodecSetPriority(long jarg1, Endpoint jarg1_, String jarg2, short jarg3) throws java.lang.Exception;
  public final static native long Endpoint_getVideoCodecParam(long jarg1, Endpoint jarg1_, String jarg2) throws java.lang.Exception;
  public final static native void Endpoint_setVideoCodecParam(long jarg1, Endpoint jarg1_, String jarg2, long jarg3, VidCodecParam jarg3_) throws java.lang.Exception;
  public final static native void Endpoint_resetVideoCodecParam(long jarg1, Endpoint jarg1_, String jarg2) throws java.lang.Exception;
  public final static native long Endpoint_srtpCryptoEnum(long jarg1, Endpoint jarg1_) throws java.lang.Exception;
  public final static native void Endpoint_handleIpChange(long jarg1, Endpoint jarg1_, long jarg2, IpChangeParam jarg2_) throws java.lang.Exception;
  public final static native void Endpoint_onNatDetectionComplete(long jarg1, Endpoint jarg1_, long jarg2, OnNatDetectionCompleteParam jarg2_);
  public final static native void Endpoint_onNatDetectionCompleteSwigExplicitEndpoint(long jarg1, Endpoint jarg1_, long jarg2, OnNatDetectionCompleteParam jarg2_);
  public final static native void Endpoint_onNatCheckStunServersComplete(long jarg1, Endpoint jarg1_, long jarg2, OnNatCheckStunServersCompleteParam jarg2_);
  public final static native void Endpoint_onNatCheckStunServersCompleteSwigExplicitEndpoint(long jarg1, Endpoint jarg1_, long jarg2, OnNatCheckStunServersCompleteParam jarg2_);
  public final static native void Endpoint_onTransportState(long jarg1, Endpoint jarg1_, long jarg2, OnTransportStateParam jarg2_);
  public final static native void Endpoint_onTransportStateSwigExplicitEndpoint(long jarg1, Endpoint jarg1_, long jarg2, OnTransportStateParam jarg2_);
  public final static native void Endpoint_onTimer(long jarg1, Endpoint jarg1_, long jarg2, OnTimerParam jarg2_);
  public final static native void Endpoint_onTimerSwigExplicitEndpoint(long jarg1, Endpoint jarg1_, long jarg2, OnTimerParam jarg2_);
  public final static native void Endpoint_onSelectAccount(long jarg1, Endpoint jarg1_, long jarg2, OnSelectAccountParam jarg2_);
  public final static native void Endpoint_onSelectAccountSwigExplicitEndpoint(long jarg1, Endpoint jarg1_, long jarg2, OnSelectAccountParam jarg2_);
  public final static native void Endpoint_onIpChangeProgress(long jarg1, Endpoint jarg1_, long jarg2, OnIpChangeProgressParam jarg2_);
  public final static native void Endpoint_onIpChangeProgressSwigExplicitEndpoint(long jarg1, Endpoint jarg1_, long jarg2, OnIpChangeProgressParam jarg2_);
  public final static native void Endpoint_onMediaEvent(long jarg1, Endpoint jarg1_, long jarg2, OnMediaEventParam jarg2_);
  public final static native void Endpoint_onMediaEventSwigExplicitEndpoint(long jarg1, Endpoint jarg1_, long jarg2, OnMediaEventParam jarg2_);
  public final static native int Endpoint_onCredAuth(long jarg1, Endpoint jarg1_, long jarg2, OnCredAuthParam jarg2_);
  public final static native int Endpoint_onCredAuthSwigExplicitEndpoint(long jarg1, Endpoint jarg1_, long jarg2, OnCredAuthParam jarg2_);
  public final static native void Endpoint_director_connect(Endpoint obj, long cptr, boolean mem_own, boolean weak_global);
  public final static native void Endpoint_change_ownership(Endpoint obj, long cptr, boolean take_or_release);
  public final static native long AuthCredInfo_SWIGUpcast(long jarg1);
  public final static native long TlsConfig_SWIGUpcast(long jarg1);
  public final static native long TransportConfig_SWIGUpcast(long jarg1);
  public final static native long MediaFormatAudio_SWIGUpcast(long jarg1);
  public final static native long MediaFormatVideo_SWIGUpcast(long jarg1);
  public final static native long AudioMedia_SWIGUpcast(long jarg1);
  public final static native long AudioMediaPlayer_SWIGUpcast(long jarg1);
  public final static native long AudioMediaRecorder_SWIGUpcast(long jarg1);
  public final static native long ToneDesc_SWIGUpcast(long jarg1);
  public final static native long ToneDigit_SWIGUpcast(long jarg1);
  public final static native long ToneGenerator_SWIGUpcast(long jarg1);
  public final static native long ExtraAudioDevice_SWIGUpcast(long jarg1);
  public final static native long VideoMedia_SWIGUpcast(long jarg1);
  public final static native long BuddyConfig_SWIGUpcast(long jarg1);
  public final static native long AccountRegConfig_SWIGUpcast(long jarg1);
  public final static native long AccountSipConfig_SWIGUpcast(long jarg1);
  public final static native long AccountCallConfig_SWIGUpcast(long jarg1);
  public final static native long AccountPresConfig_SWIGUpcast(long jarg1);
  public final static native long AccountMwiConfig_SWIGUpcast(long jarg1);
  public final static native long AccountNatConfig_SWIGUpcast(long jarg1);
  public final static native long SrtpOpt_SWIGUpcast(long jarg1);
  public final static native long RtcpFbConfig_SWIGUpcast(long jarg1);
  public final static native long AccountMediaConfig_SWIGUpcast(long jarg1);
  public final static native long AccountVideoConfig_SWIGUpcast(long jarg1);
  public final static native long AccountConfig_SWIGUpcast(long jarg1);
  public final static native long JsonDocument_SWIGUpcast(long jarg1);
  public final static native long UaConfig_SWIGUpcast(long jarg1);
  public final static native long LogConfig_SWIGUpcast(long jarg1);
  public final static native long MediaConfig_SWIGUpcast(long jarg1);
  public final static native long EpConfig_SWIGUpcast(long jarg1);

  public static void SwigDirector_AudioMediaPlayer_onEof2(AudioMediaPlayer jself) {
    jself.onEof2();
  }
  public static void SwigDirector_Buddy_onBuddyState(Buddy jself) {
    jself.onBuddyState();
  }
  public static void SwigDirector_Buddy_onBuddyEvSubState(Buddy jself, long prm) {
    jself.onBuddyEvSubState(new OnBuddyEvSubStateParam(prm, false));
  }
  public static boolean SwigDirector_FindBuddyMatch_match(FindBuddyMatch jself, String token, long buddy) {
    return jself.match(token, new Buddy(buddy, false));
  }
  public static void SwigDirector_Account_onIncomingCall(Account jself, long prm) {
    jself.onIncomingCall(new OnIncomingCallParam(prm, false));
  }
  public static void SwigDirector_Account_onRegStarted(Account jself, long prm) {
    jself.onRegStarted(new OnRegStartedParam(prm, false));
  }
  public static void SwigDirector_Account_onRegState(Account jself, long prm) {
    jself.onRegState(new OnRegStateParam(prm, false));
  }
  public static void SwigDirector_Account_onIncomingSubscribe(Account jself, long prm) {
    jself.onIncomingSubscribe(new OnIncomingSubscribeParam(prm, false));
  }
  public static void SwigDirector_Account_onInstantMessage(Account jself, long prm) {
    jself.onInstantMessage(new OnInstantMessageParam(prm, false));
  }
  public static void SwigDirector_Account_onInstantMessageStatus(Account jself, long prm) {
    jself.onInstantMessageStatus(new OnInstantMessageStatusParam(prm, false));
  }
  public static void SwigDirector_Account_onTypingIndication(Account jself, long prm) {
    jself.onTypingIndication(new OnTypingIndicationParam(prm, false));
  }
  public static void SwigDirector_Account_onMwiInfo(Account jself, long prm) {
    jself.onMwiInfo(new OnMwiInfoParam(prm, false));
  }
  public static void SwigDirector_Call_onCallState(Call jself, long prm) {
    jself.onCallState(new OnCallStateParam(prm, false));
  }
  public static void SwigDirector_Call_onCallTsxState(Call jself, long prm) {
    jself.onCallTsxState(new OnCallTsxStateParam(prm, false));
  }
  public static void SwigDirector_Call_onCallMediaState(Call jself, long prm) {
    jself.onCallMediaState(new OnCallMediaStateParam(prm, false));
  }
  public static void SwigDirector_Call_onCallSdpCreated(Call jself, long prm) {
    jself.onCallSdpCreated(new OnCallSdpCreatedParam(prm, false));
  }
  public static void SwigDirector_Call_onStreamPreCreate(Call jself, long prm) {
    jself.onStreamPreCreate(new OnStreamPreCreateParam(prm, false));
  }
  public static void SwigDirector_Call_onStreamCreated(Call jself, long prm) {
    jself.onStreamCreated(new OnStreamCreatedParam(prm, false));
  }
  public static void SwigDirector_Call_onStreamDestroyed(Call jself, long prm) {
    jself.onStreamDestroyed(new OnStreamDestroyedParam(prm, false));
  }
  public static void SwigDirector_Call_onDtmfDigit(Call jself, long prm) {
    jself.onDtmfDigit(new OnDtmfDigitParam(prm, false));
  }
  public static void SwigDirector_Call_onDtmfEvent(Call jself, long prm) {
    jself.onDtmfEvent(new OnDtmfEventParam(prm, false));
  }
  public static void SwigDirector_Call_onCallTransferRequest(Call jself, long prm) {
    jself.onCallTransferRequest(new OnCallTransferRequestParam(prm, false));
  }
  public static void SwigDirector_Call_onCallTransferStatus(Call jself, long prm) {
    jself.onCallTransferStatus(new OnCallTransferStatusParam(prm, false));
  }
  public static void SwigDirector_Call_onCallReplaceRequest(Call jself, long prm) {
    jself.onCallReplaceRequest(new OnCallReplaceRequestParam(prm, false));
  }
  public static void SwigDirector_Call_onCallReplaced(Call jself, long prm) {
    jself.onCallReplaced(new OnCallReplacedParam(prm, false));
  }
  public static void SwigDirector_Call_onCallRxOffer(Call jself, long prm) {
    jself.onCallRxOffer(new OnCallRxOfferParam(prm, false));
  }
  public static void SwigDirector_Call_onCallRxReinvite(Call jself, long prm) {
    jself.onCallRxReinvite(new OnCallRxReinviteParam(prm, false));
  }
  public static void SwigDirector_Call_onCallTxOffer(Call jself, long prm) {
    jself.onCallTxOffer(new OnCallTxOfferParam(prm, false));
  }
  public static void SwigDirector_Call_onInstantMessage(Call jself, long prm) {
    jself.onInstantMessage(new OnInstantMessageParam(prm, false));
  }
  public static void SwigDirector_Call_onInstantMessageStatus(Call jself, long prm) {
    jself.onInstantMessageStatus(new OnInstantMessageStatusParam(prm, false));
  }
  public static void SwigDirector_Call_onTypingIndication(Call jself, long prm) {
    jself.onTypingIndication(new OnTypingIndicationParam(prm, false));
  }
  public static int SwigDirector_Call_onCallRedirected(Call jself, long prm) {
    return jself.onCallRedirected(new OnCallRedirectedParam(prm, false));
  }
  public static void SwigDirector_Call_onCallMediaTransportState(Call jself, long prm) {
    jself.onCallMediaTransportState(new OnCallMediaTransportStateParam(prm, false));
  }
  public static void SwigDirector_Call_onCallMediaEvent(Call jself, long prm) {
    jself.onCallMediaEvent(new OnCallMediaEventParam(prm, false));
  }
  public static void SwigDirector_Call_onCreateMediaTransport(Call jself, long prm) {
    jself.onCreateMediaTransport(new OnCreateMediaTransportParam(prm, false));
  }
  public static void SwigDirector_Call_onCreateMediaTransportSrtp(Call jself, long prm) {
    jself.onCreateMediaTransportSrtp(new OnCreateMediaTransportSrtpParam(prm, false));
  }
  public static void SwigDirector_LogWriter_write(LogWriter jself, long entry) {
    jself.write(new LogEntry(entry, false));
  }
  public static void SwigDirector_Endpoint_onNatDetectionComplete(Endpoint jself, long prm) {
    jself.onNatDetectionComplete(new OnNatDetectionCompleteParam(prm, false));
  }
  public static void SwigDirector_Endpoint_onNatCheckStunServersComplete(Endpoint jself, long prm) {
    jself.onNatCheckStunServersComplete(new OnNatCheckStunServersCompleteParam(prm, false));
  }
  public static void SwigDirector_Endpoint_onTransportState(Endpoint jself, long prm) {
    jself.onTransportState(new OnTransportStateParam(prm, false));
  }
  public static void SwigDirector_Endpoint_onTimer(Endpoint jself, long prm) {
    jself.onTimer(new OnTimerParam(prm, false));
  }
  public static void SwigDirector_Endpoint_onSelectAccount(Endpoint jself, long prm) {
    jself.onSelectAccount(new OnSelectAccountParam(prm, false));
  }
  public static void SwigDirector_Endpoint_onIpChangeProgress(Endpoint jself, long prm) {
    jself.onIpChangeProgress(new OnIpChangeProgressParam(prm, false));
  }
  public static void SwigDirector_Endpoint_onMediaEvent(Endpoint jself, long prm) {
    jself.onMediaEvent(new OnMediaEventParam(prm, false));
  }
  public static int SwigDirector_Endpoint_onCredAuth(Endpoint jself, long prm) {
    return jself.onCredAuth(new OnCredAuthParam(prm, false));
  }

  private final static native void swig_module_init();
  static {
    swig_module_init();
  }
}
