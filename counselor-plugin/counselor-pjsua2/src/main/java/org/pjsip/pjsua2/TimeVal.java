/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class TimeVal {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected TimeVal(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(TimeVal obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_TimeVal(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setSec(int value) {
    pjsua2JNI.TimeVal_sec_set(swigCPtr, this, value);
  }

  public int getSec() {
    return pjsua2JNI.TimeVal_sec_get(swigCPtr, this);
  }

  public void setMsec(int value) {
    pjsua2JNI.TimeVal_msec_set(swigCPtr, this, value);
  }

  public int getMsec() {
    return pjsua2JNI.TimeVal_msec_get(swigCPtr, this);
  }

  public TimeVal() {
    this(pjsua2JNI.new_TimeVal(), true);
  }

}
