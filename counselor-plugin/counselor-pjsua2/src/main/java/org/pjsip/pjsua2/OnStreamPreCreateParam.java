/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class OnStreamPreCreateParam {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected OnStreamPreCreateParam(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(OnStreamPreCreateParam obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_OnStreamPreCreateParam(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setStreamIdx(long value) {
    pjsua2JNI.OnStreamPreCreateParam_streamIdx_set(swigCPtr, this, value);
  }

  public long getStreamIdx() {
    return pjsua2JNI.OnStreamPreCreateParam_streamIdx_get(swigCPtr, this);
  }

  public void setStreamInfo(StreamInfo value) {
    pjsua2JNI.OnStreamPreCreateParam_streamInfo_set(swigCPtr, this, StreamInfo.getCPtr(value), value);
  }

  public StreamInfo getStreamInfo() {
    long cPtr = pjsua2JNI.OnStreamPreCreateParam_streamInfo_get(swigCPtr, this);
    return (cPtr == 0) ? null : new StreamInfo(cPtr, false);
  }

  public OnStreamPreCreateParam() {
    this(pjsua2JNI.new_OnStreamPreCreateParam(), true);
  }

}
