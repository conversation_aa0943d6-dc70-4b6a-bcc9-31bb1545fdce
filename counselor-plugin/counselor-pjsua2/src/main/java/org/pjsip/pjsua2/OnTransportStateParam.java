/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class OnTransportStateParam {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected OnTransportStateParam(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(OnTransportStateParam obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_OnTransportStateParam(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setHnd(SWIGTYPE_p_void value) {
    pjsua2JNI.OnTransportStateParam_hnd_set(swigCPtr, this, SWIGTYPE_p_void.getCPtr(value));
  }

  public SWIGTYPE_p_void getHnd() {
    long cPtr = pjsua2JNI.OnTransportStateParam_hnd_get(swigCPtr, this);
    return (cPtr == 0) ? null : new SWIGTYPE_p_void(cPtr, false);
  }

  public void setType(String value) {
    pjsua2JNI.OnTransportStateParam_type_set(swigCPtr, this, value);
  }

  public String getType() {
    return pjsua2JNI.OnTransportStateParam_type_get(swigCPtr, this);
  }

  public void setState(int value) {
    pjsua2JNI.OnTransportStateParam_state_set(swigCPtr, this, value);
  }

  public int getState() {
    return pjsua2JNI.OnTransportStateParam_state_get(swigCPtr, this);
  }

  public void setLastError(int value) {
    pjsua2JNI.OnTransportStateParam_lastError_set(swigCPtr, this, value);
  }

  public int getLastError() {
    return pjsua2JNI.OnTransportStateParam_lastError_get(swigCPtr, this);
  }

  public void setTlsInfo(TlsInfo value) {
    pjsua2JNI.OnTransportStateParam_tlsInfo_set(swigCPtr, this, TlsInfo.getCPtr(value), value);
  }

  public TlsInfo getTlsInfo() {
    long cPtr = pjsua2JNI.OnTransportStateParam_tlsInfo_get(swigCPtr, this);
    return (cPtr == 0) ? null : new TlsInfo(cPtr, false);
  }

  public OnTransportStateParam() {
    this(pjsua2JNI.new_OnTransportStateParam(), true);
  }

}
