/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjsip_event_id_e {
  public final static int PJSIP_EVENT_UNKNOWN = 0;
  public final static int PJSIP_EVENT_TIMER = PJSIP_EVENT_UNKNOWN + 1;
  public final static int PJSIP_EVENT_TX_MSG = PJSIP_EVENT_TIMER + 1;
  public final static int PJSIP_EVENT_RX_MSG = PJSIP_EVENT_TX_MSG + 1;
  public final static int PJSIP_EVENT_TRANSPORT_ERROR = PJSIP_EVENT_RX_MSG + 1;
  public final static int PJSIP_EVENT_TSX_STATE = PJSIP_EVENT_TRANSPORT_ERROR + 1;
  public final static int PJSIP_EVENT_USER = PJSIP_EVENT_TSX_STATE + 1;
}

