/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class MediaFormat {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected MediaFormat(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(MediaFormat obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_MediaFormat(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public void setId(long value) {
    pjsua2JNI.MediaFormat_id_set(swigCPtr, this, value);
  }

  public long getId() {
    return pjsua2JNI.MediaFormat_id_get(swigCPtr, this);
  }

  public void setType(int value) {
    pjsua2JNI.MediaFormat_type_set(swigCPtr, this, value);
  }

  public int getType() {
    return pjsua2JNI.MediaFormat_type_get(swigCPtr, this);
  }

  public MediaFormat() {
    this(pjsua2JNI.new_MediaFormat(), true);
  }

}
