/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class SrtpOpt extends PersistentObject {
  private transient long swigCPtr;

  protected SrtpOpt(long cPtr, boolean cMemoryOwn) {
    super(pjsua2JNI.SrtpOpt_SWIGUpcast(cPtr), cMemoryOwn);
    swigCPtr = cPtr;
  }

  protected static long getCPtr(SrtpOpt obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_SrtpOpt(swigCPtr);
      }
      swigCPtr = 0;
    }
    super.delete();
  }

  public void setCryptos(SrtpCryptoVector value) {
    pjsua2JNI.SrtpOpt_cryptos_set(swigCPtr, this, SrtpCryptoVector.getCPtr(value), value);
  }

  public SrtpCryptoVector getCryptos() {
    long cPtr = pjsua2JNI.SrtpOpt_cryptos_get(swigCPtr, this);
    return (cPtr == 0) ? null : new SrtpCryptoVector(cPtr, false);
  }

  public void setKeyings(IntVector value) {
    pjsua2JNI.SrtpOpt_keyings_set(swigCPtr, this, IntVector.getCPtr(value), value);
  }

  public IntVector getKeyings() {
    long cPtr = pjsua2JNI.SrtpOpt_keyings_get(swigCPtr, this);
    return (cPtr == 0) ? null : new IntVector(cPtr, false);
  }

  public SrtpOpt() {
    this(pjsua2JNI.new_SrtpOpt(), true);
  }

  public void readObject(ContainerNode node) throws java.lang.Exception {
    pjsua2JNI.SrtpOpt_readObject(swigCPtr, this, ContainerNode.getCPtr(node), node);
  }

  public void writeObject(ContainerNode node) throws java.lang.Exception {
    pjsua2JNI.SrtpOpt_writeObject(swigCPtr, this, ContainerNode.getCPtr(node), node);
  }

}
