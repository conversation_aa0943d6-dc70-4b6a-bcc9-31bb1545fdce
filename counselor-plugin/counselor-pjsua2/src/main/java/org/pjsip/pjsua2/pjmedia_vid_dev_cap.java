/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjmedia_vid_dev_cap {
  public final static int PJMEDIA_VID_DEV_CAP_FORMAT = 1;
  public final static int PJMEDIA_VID_DEV_CAP_INPUT_SCALE = 2;
  public final static int PJMEDIA_VID_DEV_CAP_OUTPUT_WINDOW = 4;
  public final static int PJMEDIA_VID_DEV_CAP_OUTPUT_RESIZE = 8;
  public final static int PJMEDIA_VID_DEV_CAP_OUTPUT_POSITION = 16;
  public final static int PJMEDIA_VID_DEV_CAP_OUTPUT_HIDE = 32;
  public final static int PJMEDIA_VID_DEV_CAP_INPUT_PREVIEW = 64;
  public final static int PJMEDIA_VID_DEV_CAP_ORIENTATION = 128;
  public final static int PJMEDIA_VID_DEV_CAP_SWITCH = 256;
  public final static int PJMEDIA_VID_DEV_CAP_OUTPUT_WINDOW_FLAGS = 512;
  public final static int PJMEDIA_VID_DEV_CAP_OUTPUT_FULLSCREEN = 1024;
  public final static int PJMEDIA_VID_DEV_CAP_MAX = 16384;
}

