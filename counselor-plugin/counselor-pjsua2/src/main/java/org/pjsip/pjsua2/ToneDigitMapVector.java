/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public class ToneDigitMapVector extends java.util.AbstractList<ToneDigitMapDigit> implements java.util.RandomAccess {
  private transient long swigCPtr;
  protected transient boolean swigCMemOwn;

  protected ToneDigitMapVector(long cPtr, boolean cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = cPtr;
  }

  protected static long getCPtr(ToneDigitMapVector obj) {
    return (obj == null) ? 0 : obj.swigCPtr;
  }

  @SuppressWarnings("deprecation")
  protected void finalize() {
    delete();
  }

  public synchronized void delete() {
    if (swigCPtr != 0) {
      if (swigCMemOwn) {
        swigCMemOwn = false;
        pjsua2JNI.delete_ToneDigitMapVector(swigCPtr);
      }
      swigCPtr = 0;
    }
  }

  public ToneDigitMapVector(ToneDigitMapDigit[] initialElements) {
    this();
    reserve(initialElements.length);

    for (ToneDigitMapDigit element : initialElements) {
      add(element);
    }
  }

  public ToneDigitMapVector(Iterable<ToneDigitMapDigit> initialElements) {
    this();
    for (ToneDigitMapDigit element : initialElements) {
      add(element);
    }
  }

  public ToneDigitMapDigit get(int index) {
    return doGet(index);
  }

  public ToneDigitMapDigit set(int index, ToneDigitMapDigit e) {
    return doSet(index, e);
  }

  public boolean add(ToneDigitMapDigit e) {
    modCount++;
    doAdd(e);
    return true;
  }

  public void add(int index, ToneDigitMapDigit e) {
    modCount++;
    doAdd(index, e);
  }

  public ToneDigitMapDigit remove(int index) {
    modCount++;
    return doRemove(index);
  }

  protected void removeRange(int fromIndex, int toIndex) {
    modCount++;
    doRemoveRange(fromIndex, toIndex);
  }

  public int size() {
    return doSize();
  }

  public ToneDigitMapVector() {
    this(pjsua2JNI.new_ToneDigitMapVector__SWIG_0(), true);
  }

  public ToneDigitMapVector(ToneDigitMapVector other) {
    this(pjsua2JNI.new_ToneDigitMapVector__SWIG_1(ToneDigitMapVector.getCPtr(other), other), true);
  }

  public long capacity() {
    return pjsua2JNI.ToneDigitMapVector_capacity(swigCPtr, this);
  }

  public void reserve(long n) {
    pjsua2JNI.ToneDigitMapVector_reserve(swigCPtr, this, n);
  }

  public boolean isEmpty() {
    return pjsua2JNI.ToneDigitMapVector_isEmpty(swigCPtr, this);
  }

  public void clear() {
    pjsua2JNI.ToneDigitMapVector_clear(swigCPtr, this);
  }

  public ToneDigitMapVector(int count, ToneDigitMapDigit value) {
    this(pjsua2JNI.new_ToneDigitMapVector__SWIG_2(count, ToneDigitMapDigit.getCPtr(value), value), true);
  }

  private int doSize() {
    return pjsua2JNI.ToneDigitMapVector_doSize(swigCPtr, this);
  }

  private void doAdd(ToneDigitMapDigit x) {
    pjsua2JNI.ToneDigitMapVector_doAdd__SWIG_0(swigCPtr, this, ToneDigitMapDigit.getCPtr(x), x);
  }

  private void doAdd(int index, ToneDigitMapDigit x) {
    pjsua2JNI.ToneDigitMapVector_doAdd__SWIG_1(swigCPtr, this, index, ToneDigitMapDigit.getCPtr(x), x);
  }

  private ToneDigitMapDigit doRemove(int index) {
    return new ToneDigitMapDigit(pjsua2JNI.ToneDigitMapVector_doRemove(swigCPtr, this, index), true);
  }

  private ToneDigitMapDigit doGet(int index) {
    return new ToneDigitMapDigit(pjsua2JNI.ToneDigitMapVector_doGet(swigCPtr, this, index), false);
  }

  private ToneDigitMapDigit doSet(int index, ToneDigitMapDigit val) {
    return new ToneDigitMapDigit(pjsua2JNI.ToneDigitMapVector_doSet(swigCPtr, this, index, ToneDigitMapDigit.getCPtr(val), val), true);
  }

  private void doRemoveRange(int fromIndex, int toIndex) {
    pjsua2JNI.ToneDigitMapVector_doRemoveRange(swigCPtr, this, fromIndex, toIndex);
  }

}
