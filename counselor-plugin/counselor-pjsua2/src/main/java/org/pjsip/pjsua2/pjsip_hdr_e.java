/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pjsip_hdr_e {
  public final static int PJSIP_H_ACCEPT = 0;
  public final static int PJSIP_H_ACCEPT_ENCODING_UNIMP = PJSIP_H_ACCEPT + 1;
  public final static int PJSIP_H_ACCEPT_LANGUAGE_UNIMP = PJSIP_H_ACCEPT_ENCODING_UNIMP + 1;
  public final static int PJSIP_H_ALERT_INFO_UNIMP = PJSIP_H_ACCEPT_LANGUAGE_UNIMP + 1;
  public final static int PJSIP_H_ALLOW = PJSIP_H_ALERT_INFO_UNIMP + 1;
  public final static int PJSIP_H_AUTHENTICATION_INFO_UNIMP = PJSIP_H_ALLOW + 1;
  public final static int PJSIP_H_AUTHORIZATION = PJSIP_H_AUTHENTICATION_INFO_UNIMP + 1;
  public final static int PJSIP_H_CALL_ID = PJSIP_H_AUTHORIZATION + 1;
  public final static int PJSIP_H_CALL_INFO_UNIMP = PJSIP_H_CALL_ID + 1;
  public final static int PJSIP_H_CONTACT = PJSIP_H_CALL_INFO_UNIMP + 1;
  public final static int PJSIP_H_CONTENT_DISPOSITION_UNIMP = PJSIP_H_CONTACT + 1;
  public final static int PJSIP_H_CONTENT_ENCODING_UNIMP = PJSIP_H_CONTENT_DISPOSITION_UNIMP + 1;
  public final static int PJSIP_H_CONTENT_LANGUAGE_UNIMP = PJSIP_H_CONTENT_ENCODING_UNIMP + 1;
  public final static int PJSIP_H_CONTENT_LENGTH = PJSIP_H_CONTENT_LANGUAGE_UNIMP + 1;
  public final static int PJSIP_H_CONTENT_TYPE = PJSIP_H_CONTENT_LENGTH + 1;
  public final static int PJSIP_H_CSEQ = PJSIP_H_CONTENT_TYPE + 1;
  public final static int PJSIP_H_DATE_UNIMP = PJSIP_H_CSEQ + 1;
  public final static int PJSIP_H_ERROR_INFO_UNIMP = PJSIP_H_DATE_UNIMP + 1;
  public final static int PJSIP_H_EXPIRES = PJSIP_H_ERROR_INFO_UNIMP + 1;
  public final static int PJSIP_H_FROM = PJSIP_H_EXPIRES + 1;
  public final static int PJSIP_H_IN_REPLY_TO_UNIMP = PJSIP_H_FROM + 1;
  public final static int PJSIP_H_MAX_FORWARDS = PJSIP_H_IN_REPLY_TO_UNIMP + 1;
  public final static int PJSIP_H_MIME_VERSION_UNIMP = PJSIP_H_MAX_FORWARDS + 1;
  public final static int PJSIP_H_MIN_EXPIRES = PJSIP_H_MIME_VERSION_UNIMP + 1;
  public final static int PJSIP_H_ORGANIZATION_UNIMP = PJSIP_H_MIN_EXPIRES + 1;
  public final static int PJSIP_H_PRIORITY_UNIMP = PJSIP_H_ORGANIZATION_UNIMP + 1;
  public final static int PJSIP_H_PROXY_AUTHENTICATE = PJSIP_H_PRIORITY_UNIMP + 1;
  public final static int PJSIP_H_PROXY_AUTHORIZATION = PJSIP_H_PROXY_AUTHENTICATE + 1;
  public final static int PJSIP_H_PROXY_REQUIRE_UNIMP = PJSIP_H_PROXY_AUTHORIZATION + 1;
  public final static int PJSIP_H_RECORD_ROUTE = PJSIP_H_PROXY_REQUIRE_UNIMP + 1;
  public final static int PJSIP_H_REPLY_TO_UNIMP = PJSIP_H_RECORD_ROUTE + 1;
  public final static int PJSIP_H_REQUIRE = PJSIP_H_REPLY_TO_UNIMP + 1;
  public final static int PJSIP_H_RETRY_AFTER = PJSIP_H_REQUIRE + 1;
  public final static int PJSIP_H_ROUTE = PJSIP_H_RETRY_AFTER + 1;
  public final static int PJSIP_H_SERVER_UNIMP = PJSIP_H_ROUTE + 1;
  public final static int PJSIP_H_SUBJECT_UNIMP = PJSIP_H_SERVER_UNIMP + 1;
  public final static int PJSIP_H_SUPPORTED = PJSIP_H_SUBJECT_UNIMP + 1;
  public final static int PJSIP_H_TIMESTAMP_UNIMP = PJSIP_H_SUPPORTED + 1;
  public final static int PJSIP_H_TO = PJSIP_H_TIMESTAMP_UNIMP + 1;
  public final static int PJSIP_H_UNSUPPORTED = PJSIP_H_TO + 1;
  public final static int PJSIP_H_USER_AGENT_UNIMP = PJSIP_H_UNSUPPORTED + 1;
  public final static int PJSIP_H_VIA = PJSIP_H_USER_AGENT_UNIMP + 1;
  public final static int PJSIP_H_WARNING_UNIMP = PJSIP_H_VIA + 1;
  public final static int PJSIP_H_WWW_AUTHENTICATE = PJSIP_H_WARNING_UNIMP + 1;
  public final static int PJSIP_H_OTHER = PJSIP_H_WWW_AUTHENTICATE + 1;
}

