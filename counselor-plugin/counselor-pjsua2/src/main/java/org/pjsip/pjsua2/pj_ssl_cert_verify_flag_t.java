/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (http://www.swig.org).
 * Version 4.0.1
 *
 * Do not make changes to this file unless you know what you are doing--modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */

package org.pjsip.pjsua2;

public final class pj_ssl_cert_verify_flag_t {
  public final static int PJ_SSL_CERT_ESUCCESS = 0;
  public final static int PJ_SSL_CERT_EISSUER_NOT_FOUND = 1 << 0;
  public final static int PJ_SSL_CERT_EUNTRUSTED = 1 << 1;
  public final static int PJ_SSL_CERT_EVALIDITY_PERIOD = 1 << 2;
  public final static int PJ_SSL_CERT_EINVALID_FORMAT = 1 << 3;
  public final static int PJ_SSL_CERT_EINVALID_PURPOSE = 1 << 4;
  public final static int PJ_SSL_CERT_EISSUER_MISMATCH = 1 << 5;
  public final static int PJ_SSL_CERT_ECRL_FAILURE = 1 << 6;
  public final static int PJ_SSL_CERT_EREVOKED = 1 << 7;
  public final static int PJ_SSL_CERT_ECHAIN_TOO_LONG = 1 << 8;
  public final static int PJ_SSL_CERT_EIDENTITY_NOT_MATCH = 1 << 30;
  public final static int PJ_SSL_CERT_EUNKNOWN = 1 << 31;
}

