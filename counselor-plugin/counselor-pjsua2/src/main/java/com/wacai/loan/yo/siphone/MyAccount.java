package com.wacai.loan.yo.siphone;

import java.util.*;
import org.pjsip.pjsua2.*;

class MyAccount extends Account
{
    public ArrayList<MyBuddy> buddyList;
    public AccountConfig cfg;
    
    MyAccount(final AccountConfig cfg) {
        this.buddyList = new ArrayList<>();
        this.cfg = cfg;
    }
    
    public MyBuddy addBuddy(final BuddyConfig buddyConfig) {
        MyBuddy myBuddy = new MyBuddy(buddyConfig);
        try {
            myBuddy.create(this, buddyConfig);
        }
        catch (Exception ex) {
            myBuddy.delete();
            myBuddy = null;
        }
        if (myBuddy != null) {
            this.buddyList.add(myBuddy);
            if (buddyConfig.getSubscribe()) {
                try {
                    myBuddy.subscribePresence(true);
                }
                catch (Exception ex2) {

                }
            }
        }
        return myBuddy;
    }
    
    public void delBuddy(final MyBuddy myBuddy) {
        this.buddyList.remove(myBuddy);
        myBuddy.delete();
    }
    
    public void delBuddy(final int n) {
        final MyBuddy myBuddy = this.buddyList.get(n);
        this.buddyList.remove(n);
        myBuddy.delete();
    }
    
    public void onRegState(final OnRegStateParam onRegStateParam) {
        MyApp.observer.notifyRegState(onRegStateParam.getCode(), onRegStateParam.getReason(), onRegStateParam.getExpiration());
    }
    
    public void onIncomingCall(final OnIncomingCallParam onIncomingCallParam) {
        System.out.println("======== Incoming call ======== ");
        MyApp.observer.notifyIncomingCall(new MyCall(this, onIncomingCallParam.getCallId()));
    }
    
    public void onInstantMessage(final OnInstantMessageParam onInstantMessageParam) {
        System.out.println("======== Incoming pager ======== ");
        System.out.println("From     : " + onInstantMessageParam.getFromUri());
        System.out.println("To       : " + onInstantMessageParam.getToUri());
        System.out.println("Contact  : " + onInstantMessageParam.getContactUri());
        System.out.println("Mimetype : " + onInstantMessageParam.getContentType());
        System.out.println("Body     : " + onInstantMessageParam.getMsgBody());
    }
}
