package com.wacai.loan.yo.siphone;

import java.util.*;
import org.pjsip.pjsua2.*;

class MyAccountConfig
{
    public AccountConfig accCfg;
    public ArrayList<BuddyConfig> buddyCfgs;
    
    MyAccountConfig() {
        this.accCfg = new AccountConfig();
        this.buddyCfgs = new ArrayList<BuddyConfig>();
    }
    
    public void readObject(final ContainerNode containerNode) {
        try {
            final ContainerNode container = containerNode.readContainer("Account");
            this.accCfg.readObject(container);
            final ContainerNode array = container.readArray("buddies");
            this.buddyCfgs.clear();
            while (array.hasUnread()) {
                final BuddyConfig buddyConfig = new BuddyConfig();
                buddyConfig.readObject(array);
                this.buddyCfgs.add(buddyConfig);
            }
        }
        catch (Exception ex) {}
    }
    
    public void writeObject(final ContainerNode containerNode) {
        try {
            final ContainerNode writeNewContainer = containerNode.writeNewContainer("Account");
            this.accCfg.writeObject(writeNewContainer);
            final ContainerNode writeNewArray = writeNewContainer.writeNewArray("buddies");
            for (int i = 0; i < this.buddyCfgs.size(); ++i) {
                this.buddyCfgs.get(i).writeObject(writeNewArray);
            }
        }
        catch (Exception ex) {}
    }
}
