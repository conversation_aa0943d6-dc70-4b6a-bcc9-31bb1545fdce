package com.wacai.loan.yo.siphone;

import org.pjsip.pjsua2.*;

class MyBuddy extends Buddy
{
    public BuddyConfig cfg;
    
    MyBuddy(final BuddyConfig cfg) {
        this.cfg = cfg;
    }
    
    String getStatusText() {
        BuddyInfo info;
        try {
            info = this.getInfo();
        }
        catch (Exception ex) {
            return "?";
        }
        String statusText = "";
        if (info.getSubState() == pjsip_evsub_state.PJSIP_EVSUB_STATE_ACTIVE) {
            if (info.getPresStatus().getStatus() == pjsua_buddy_status.PJSUA_BUDDY_STATUS_ONLINE) {
                statusText = info.getPresStatus().getStatusText();
                if (statusText == null || statusText.length() == 0) {
                    statusText = "Online";
                }
            }
            else if (info.getPresStatus().getStatus() == pjsua_buddy_status.PJSUA_BUDDY_STATUS_OFFLINE) {
                statusText = "Offline";
            }
            else {
                statusText = "Unknown";
            }
        }
        return statusText;
    }
    
    public void onBuddyState() {
        MyApp.observer.notifyBuddyState(this);
    }
}
