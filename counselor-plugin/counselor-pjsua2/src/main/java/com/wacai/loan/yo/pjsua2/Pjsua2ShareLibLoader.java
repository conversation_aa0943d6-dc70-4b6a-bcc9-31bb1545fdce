package com.wacai.loan.yo.pjsua2;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.stream.Stream;
import java.io.FileOutputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.SystemUtils;

/**
 * <AUTHOR> <br>
 * <EMAIL> <br>
 * Jul 23, 2018 19:08
 */
@Slf4j
public class Pjsua2ShareLibLoader {
    private static volatile boolean LOADED = false;
    private static File LIB_DIR;

    /**
     * load PJSUA2 share libraries
     */
    public static synchronized void load() {
        log.info("Load PJSUA2 share libraries.");
        if (!LOADED) {
            LIB_DIR = new File(FileUtils.getTempDirectory(),
                "PJSUA2_LIB_" + System.currentTimeMillis());
            LIB_DIR.mkdir();

            if (SystemUtils.IS_OS_MAC) {
                log.info("Mac platform found.");
                loadMac();
            }
            else if (SystemUtils.IS_OS_LINUX) {
                log.info("Linux platform found.");
                loadLinux();
            }
            else {
                throw new IllegalStateException(
                    "Unsupported platform");
            }
            LOADED = true;
            log.info("PJSUA2 share libraries loaded.");
        }
        else {
            log.info("PJSUA2 libraries have been loaded, ignore.");
        }
    }

    /**
     * offload PJSUA2 share libraries
     */
    public static synchronized void offload() {
        log.info("Offload PJSUA2 share libraries.");
        if (LOADED) {
            log.info("Remove lib dir [{}].", LIB_DIR);
            FileUtils.deleteQuietly(LIB_DIR);
        }
        else {
            log.info("PJSUA2 libraries have not been loaded, ignore.");
        }
    }

    static void loadLinux() {
        load(new String[] {
            "/lib/Linux/libpjsua2-jni.so"
        });
    }

    static void loadMac() {
        load(new String[] {
            "/lib/MacOS/libpjsua2-jni.dylib"
        });
    }

    static String copyTemp(final String path) {
        log.info("Write temp share library [{}].", path);
        try (final InputStream libInputStream =
             Pjsua2ShareLibLoader.class.getResourceAsStream(path)) {

            final File libFile = new File(LIB_DIR, FilenameUtils.getName(path));
            log.info("Write share lib [{}] to file [{}].", path, libFile);

            try (final FileOutputStream libOutputStream =
                new FileOutputStream(libFile)) {
                IOUtils.copy(libInputStream, libOutputStream);
            }
            return libFile.getAbsolutePath();
        }
        catch (IOException e) {
            throw new IllegalStateException(
                "Write share library [" + path + "] error caused", e);
        }
    }

    static void load(final String[] paths) {
        Stream.of(paths).map(Pjsua2ShareLibLoader::copyTemp).forEach(System::load);
    }
}
