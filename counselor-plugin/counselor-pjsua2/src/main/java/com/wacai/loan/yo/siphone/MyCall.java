package com.wacai.loan.yo.siphone;

import org.pjsip.pjsua2.*;

class MyCall extends Call
{
    public VideoWindow vidWin;
    public VideoPreview vidPrev;
    
    MyCall(final MyAccount myAccount, final int n) {
        super((Account)myAccount, n);
        this.vidWin = null;
    }
    
    public void onCallState(final OnCallStateParam onCallStateParam) {
        MyApp.observer.notifyCallState(this);
        try {
            if (this.getInfo().getState() == pjsip_inv_state.PJSIP_INV_STATE_DISCONNECTED) {
                MyApp.ep.utilLogWrite(3, "MyCall", this.dump(true, ""));
                this.delete();
            }
        }
        catch (Exception ex) {}
    }
    
    public void onCallMediaState(final OnCallMediaStateParam onCallMediaStateParam) {
        CallInfo info;
        try {
            info = this.getInfo();
        }
        catch (Exception ex) {
            return;
        }
        final CallMediaInfoVector media = info.getMedia();
        for (int n = 0; n < media.size(); ++n) {
            final CallMediaInfo value = media.get(n);
            if (value.getType() == pjmedia_type.PJMEDIA_TYPE_AUDIO && (value.getStatus() == pjsua_call_media_status.PJSUA_CALL_MEDIA_ACTIVE || value.getStatus() == pjsua_call_media_status.PJSUA_CALL_MEDIA_REMOTE_HOLD)) {
                final AudioMedia typecastFromMedia = AudioMedia.typecastFromMedia(this.getMedia((long)n));
                try {
                    MyApp.ep.audDevManager().getCaptureDevMedia().startTransmit(typecastFromMedia);
                    typecastFromMedia.startTransmit(MyApp.ep.audDevManager().getPlaybackDevMedia());
                }
                catch (Exception ex2) {}
            }
            else if (value.getType() == pjmedia_type.PJMEDIA_TYPE_VIDEO && value.getStatus() == pjsua_call_media_status.PJSUA_CALL_MEDIA_ACTIVE && value.getVideoIncomingWindowId() != pjsua2.INVALID_ID) {
                this.vidWin = new VideoWindow(value.getVideoIncomingWindowId());
                this.vidPrev = new VideoPreview(value.getVideoCapDev());
            }
        }
        MyApp.observer.notifyCallMediaState(this);
    }
}
