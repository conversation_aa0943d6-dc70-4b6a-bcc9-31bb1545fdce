package com.wacai.loan.yo.siphone;

import java.util.*;
import java.io.*;

import com.wacai.loan.yo.pjsua2.Pjsua2ShareLibLoader;
import org.pjsip.pjsua2.*;

class MyApp {

    static {
//        try {
//            System.loadLibrary("openh264");
//        }
//        catch (UnsatisfiedLinkError unsatisfiedLinkError) {
//            System.out.println("UnsatisfiedLinkError: " + unsatisfiedLinkError.getMessage());
//            System.out.println("This could be safely ignored if you don't need video.");
//        }
        // System.loadLibrary("pjsua2");
        // System.out.println("Library loaded");

        Pjsua2ShareLibLoader.load();
        MyApp.ep = new Endpoint();
    }

    public static Endpoint ep;
    public static MyAppObserver observer;
    public ArrayList<MyAccount> accList;
    private ArrayList<MyAccountConfig> accCfgs;
    private EpConfig epConfig;
    private TransportConfig sipTpConfig;
    private String appDir;
    private MyLogWriter logWriter;
    private final String configName = "pjsua2.json";
    private final int SIP_PORT = 6000;
    private final int LOG_LEVEL = 5;
    
    MyApp() {
        this.accList = new ArrayList<>();
        this.accCfgs = new ArrayList<>();
        this.epConfig = new EpConfig();
        this.sipTpConfig = new TransportConfig();
    }
    
    public void init(final MyAppObserver myAppObserver, final String s) {
        this.init(myAppObserver, s, false);
    }
    
    public void init(final MyAppObserver observer, final String appDir, final boolean mainThread) {
        MyApp.observer = observer;
        this.appDir = appDir;
        try {
            MyApp.ep.libCreate();
        }
        catch (Exception ex4) {
            return;
        }
        final String string = this.appDir + "/" + "pjsua2.json";
        if (new File(string).exists()) {
            this.loadConfig(string);
        }
        else {
            this.sipTpConfig.setPort(6000L);
        }
        this.epConfig.getLogConfig().setLevel(LOG_LEVEL);
        this.epConfig.getLogConfig().setConsoleLevel(LOG_LEVEL);
        final LogConfig logConfig = this.epConfig.getLogConfig();
        logConfig.setWriter(this.logWriter = new MyLogWriter());
        logConfig.setDecor(logConfig.getDecor() & ~(pj_log_decoration.PJ_LOG_HAS_CR | pj_log_decoration.PJ_LOG_HAS_NEWLINE));
        final UaConfig uaConfig = this.epConfig.getUaConfig();
        uaConfig.setUserAgent("Pjsua2 Android " + MyApp.ep.libVersion().getFull());

        if (mainThread) {
            uaConfig.setThreadCnt(0L);
            uaConfig.setMainThreadOnly(true);
        }

        try {
            MyApp.ep.libInit(this.epConfig);
        }
        catch (Exception ex5) {
            return;
        }
        try {
            MyApp.ep.transportCreate(pjsip_transport_type_e.PJSIP_TRANSPORT_UDP, this.sipTpConfig);
        }
        catch (Exception ex) {
            System.out.println(ex);
        }
        try {
            MyApp.ep.transportCreate(pjsip_transport_type_e.PJSIP_TRANSPORT_TCP, this.sipTpConfig);
        }
        catch (Exception ex2) {
            System.out.println(ex2);
        }
        try {
            this.sipTpConfig.setPort(6001L);
            MyApp.ep.transportCreate(pjsip_transport_type_e.PJSIP_TRANSPORT_TLS, this.sipTpConfig);
        }
        catch (Exception ex3) {
            System.out.println(ex3);
        }
        this.sipTpConfig.setPort(6000L);
        for (int i = 0; i < this.accCfgs.size(); ++i) {
            final MyAccountConfig myAccountConfig = this.accCfgs.get(i);
            myAccountConfig.accCfg.getNatConfig().setIceEnabled(true);
            myAccountConfig.accCfg.getVideoConfig().setAutoTransmitOutgoing(true);
            myAccountConfig.accCfg.getVideoConfig().setAutoShowIncoming(true);
            final MyAccount addAcc = this.addAcc(myAccountConfig.accCfg);
            if (addAcc != null) {
                for (int j = 0; j < myAccountConfig.buddyCfgs.size(); ++j) {
                    addAcc.addBuddy(myAccountConfig.buddyCfgs.get(j));
                }
            }
        }
        try {
            MyApp.ep.libStart();
        }
        catch (Exception ex6) {}
    }
    
    public MyAccount addAcc(final AccountConfig accountConfig) {
        final MyAccount myAccount = new MyAccount(accountConfig);
        try {
            myAccount.create(accountConfig);
        }
        catch (Exception ex) {
            return null;
        }
        this.accList.add(myAccount);
        return myAccount;
    }
    
    public void delAcc(final MyAccount myAccount) {
        this.accList.remove(myAccount);
    }
    
    private void loadConfig(final String s) {
        final JsonDocument jsonDocument = new JsonDocument();
        try {
            jsonDocument.loadFile(s);
            final ContainerNode rootContainer = jsonDocument.getRootContainer();
            this.epConfig.readObject(rootContainer);
            this.sipTpConfig.readObject(rootContainer.readContainer("SipTransport"));
            this.accCfgs.clear();
            final ContainerNode array = rootContainer.readArray("accounts");
            while (array.hasUnread()) {
                final MyAccountConfig myAccountConfig = new MyAccountConfig();
                myAccountConfig.readObject(array);
                this.accCfgs.add(myAccountConfig);
            }
        }
        catch (Exception ex) {
            System.out.println(ex);
        }
        jsonDocument.delete();
    }
    
    private void buildAccConfigs() {
        this.accCfgs.clear();
        for (int i = 0; i < this.accList.size(); ++i) {
            final MyAccount myAccount = this.accList.get(i);
            final MyAccountConfig myAccountConfig = new MyAccountConfig();
            myAccountConfig.accCfg = myAccount.cfg;
            myAccountConfig.buddyCfgs.clear();
            for (int j = 0; j < myAccount.buddyList.size(); ++j) {
                myAccountConfig.buddyCfgs.add(myAccount.buddyList.get(j).cfg);
            }
            this.accCfgs.add(myAccountConfig);
        }
    }
    
    private void saveConfig(final String s) {
        final JsonDocument jsonDocument = new JsonDocument();
        try {
            jsonDocument.writeObject(this.epConfig);
            this.sipTpConfig.writeObject(jsonDocument.writeNewContainer("SipTransport"));
            this.buildAccConfigs();
            final ContainerNode writeNewArray = jsonDocument.writeNewArray("accounts");
            for (int i = 0; i < this.accCfgs.size(); ++i) {
                this.accCfgs.get(i).writeObject(writeNewArray);
            }
            jsonDocument.saveFile(s);
        }
        catch (Exception ex) {}
        jsonDocument.delete();
    }
    
    public void handleNetworkChange() {
        try {
            System.out.println("Network change detected");
            MyApp.ep.handleIpChange(new IpChangeParam());
        }
        catch (Exception ex) {
            System.out.println(ex);
        }
    }
    
    public void deinit() {
        this.saveConfig(this.appDir + "/" + "pjsua2.json");
        Runtime.getRuntime().gc();
        try {
            MyApp.ep.libDestroy();
        }
        catch (Exception ex) {}
        MyApp.ep.delete();
        MyApp.ep = null;
    }
    
}
