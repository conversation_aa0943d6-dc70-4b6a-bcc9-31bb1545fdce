package com.wacai.loan.yo.siphone;

import org.pjsip.pjsua2.*;

public class SipPhone {

    static {
        SipPhone.app = new MyApp();
        SipPhone.observer = new MyObserver();
        SipPhone.account = null;
        SipPhone.accCfg = null;
    }

    private static MyApp app;
    private static MyAppObserver observer;
    private static MyAccount account;
    private static AccountConfig accCfg;
    
    private static void runWorker() {
        try {
            SipPhone.app.init(SipPhone.observer, SipPhone.class.getResource("/").getFile(), true);
        }
        catch (Exception ex) {
            System.out.println(ex);
            SipPhone.app.deinit();
            System.exit(-1);
        }
        if (SipPhone.app.accList.size() == 0) {
            (SipPhone.accCfg = new AccountConfig()).setIdUri("sip:7037@************");
            SipPhone.accCfg.getRegConfig().setRegistrarUri("sip:************");
            SipPhone.account = SipPhone.app.addAcc(SipPhone.accCfg);
        }
        else {
            SipPhone.account = SipPhone.app.accList.get(0);
            SipPhone.accCfg = SipPhone.account.cfg;
        }
        try {
            SipPhone.account.modify(SipPhone.accCfg);
        }
        catch (Exception ex2) {}
        while (!Thread.currentThread().isInterrupted()) {
            MyApp.ep.libHandleEvents(10L);
            try {
                Thread.currentThread();
                Thread.sleep(50L);
                continue;
            }
            catch (InterruptedException ex3) {}
            break;
        }
        SipPhone.app.deinit();
    }
    
    public static void main(final String[] array) {
        Runtime.getRuntime().addShutdownHook(new MyShutdownHook(Thread.currentThread()));
        runWorker();
    }
    
}
