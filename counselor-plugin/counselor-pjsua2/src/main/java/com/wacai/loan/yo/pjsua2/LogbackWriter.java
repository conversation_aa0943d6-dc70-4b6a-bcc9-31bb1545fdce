package com.wacai.loan.yo.pjsua2;

import org.pjsip.pjsua2.LogEntry;
import org.pjsip.pjsua2.LogWriter;
import org.slf4j.LoggerFactory;
import lombok.extern.slf4j.Slf4j;
import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import lombok.RequiredArgsConstructor;
import ch.qos.logback.core.util.FileSize;
import ch.qos.logback.core.ConsoleAppender;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.rolling.RollingFileAppender;
import ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP;
import ch.qos.logback.core.rolling.TimeBasedRollingPolicy;
import org.springframework.beans.factory.InitializingBean;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * @date Mar 06, 2020 14:11:55
 */
@Slf4j
@RequiredArgsConstructor
public class LogbackWriter extends LogWriter implements InitializingBean {
    private final String logfile;
    private final int consoleLogLevel;

    @Value("${pjsip.log.rolling-size:64MB}")
    private String fileRollingSize;

    /**
     * {@inheritDoc}
     */
    @Override
    public void write(final LogEntry entry) {
        log.info("[" + entry.getThreadName() + "] " + entry.getMsg());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void afterPropertiesSet() {
        final LoggerContext loggerContext =
            (LoggerContext) LoggerFactory.getILoggerFactory();

        final Logger logger = (Logger) log;
        logger.setLevel(Level.TRACE);

        final PatternLayoutEncoder encoder =
            new PatternLayoutEncoder();
        encoder.setContext(loggerContext);
        encoder.setPattern("%d{yyyy-MM-dd HH:mm:ss,SSS} %msg%n");
        encoder.start();

        final RollingFileAppender<ILoggingEvent> fileAppender =
            new RollingFileAppender<>();
        fileAppender.setContext(loggerContext);
        fileAppender.setFile(logfile);
        fileAppender.setEncoder(encoder);

        final TimeBasedRollingPolicy tbrp =
            new TimeBasedRollingPolicy();
        tbrp.setContext(loggerContext);
        tbrp.setFileNamePattern(
            logfile.replaceAll("\\.log$", "-%d{yyyy-MM-dd}.%i.log"));
        tbrp.setMaxHistory(4);
        final SizeAndTimeBasedFNATP fnatp = new SizeAndTimeBasedFNATP();
        fnatp.setMaxFileSize(FileSize.valueOf(fileRollingSize));
        tbrp.setTimeBasedFileNamingAndTriggeringPolicy(fnatp);
        tbrp.setParent(fileAppender);

        fileAppender.setRollingPolicy(tbrp);
        tbrp.start();
        fileAppender.start();

        logger.addAppender(fileAppender);

        if (consoleLogLevel > 0) {
            final ConsoleAppender<ILoggingEvent> consoleAppender =
                new ConsoleAppender<>();
            consoleAppender.setContext(loggerContext);

            final PatternLayoutEncoder consoleEncoder =
                new PatternLayoutEncoder();
            consoleEncoder.setContext(loggerContext);
            consoleEncoder.setPattern(
                "%magenta(%d{yyyy-MM-dd HH:mm:ss,SSS}) %green([PJSUA2]) %msg%n");
            consoleEncoder.start();

            consoleAppender.setEncoder(consoleEncoder);
            consoleAppender.setWithJansi(true);
            consoleAppender.start();
            logger.addAppender(consoleAppender);
        }

        /* set to true if root should log too */
        logger.setAdditive(false);
    }
}
