package com.wacai.loan.yo.siphone;

import org.pjsip.pjsua2.*;

class MyObserver implements MyAppObserver
{
    private MyCall currentCall;
    
    @Override
    public void notifyRegState(final int pjsip_status_code, final String s, final long n) {
    }
    
    @Override
    public void notifyIncomingCall(final MyCall currentCall) {
        final CallOpParam callOpParam = new CallOpParam();
        callOpParam.setStatusCode(pjsip_status_code.PJSIP_SC_OK);
        try {
            (this.currentCall = currentCall).answer(callOpParam);
        }
        catch (Exception ex) {
            System.out.println(ex);
        }
    }
    
    @Override
    public void notifyCallMediaState(final MyCall myCall) {
    }
    
    @Override
    public void notifyCallState(final MyCall myCall) {
        if (currentCall == null || myCall.getId() != currentCall.getId()) {
            return;
        }
        CallInfo info;
        try {
            info = myCall.getInfo();
        }
        catch (Exception ex) {
            info = null;
        }
        if (info.getState() == pjsip_inv_state.PJSIP_INV_STATE_DISCONNECTED) {
            currentCall = null;
        }
    }
    
    @Override
    public void notifyBuddyState(final MyBuddy myBuddy) {
    }
    
    @Override
    public void notifyChangeNetwork() {
    }
}
