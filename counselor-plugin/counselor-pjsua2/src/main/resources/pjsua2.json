{"EpConfig": {"UaConfig": {"maxCalls": 4, "threadCnt": 0, "mainThreadOnly": true, "nameserver": [], "userAgent": "Pjsua2 Android 2.8", "stunServer": [], "stunTryIpv6": false, "stunIgnoreFailure": true, "natTypeInSdp": 1, "mwiUnsolicitedEnabled": true}, "LogConfig": {"msgLogging": 1, "level": 4, "consoleLevel": 4, "decor": 25200, "filename": "", "fileFlags": 0}, "MediaConfig": {"clockRate": 16000, "sndClockRate": 0, "channelCount": 1, "audioFramePtime": 20, "maxMediaPorts": 2560, "hasIoqueue": true, "threadCnt": 1, "quality": 8, "ptime": 0, "noVad": false, "ilbcMode": 30, "txDropPct": 0, "rxDropPct": 0, "ecOptions": 0, "ecTailLen": 200, "sndRecLatency": 100, "sndPlayLatency": 140, "jbInit": -1, "jbMinPre": -1, "jbMaxPre": -1, "jbMax": -1, "sndAutoCloseTime": 1, "vidPreviewEnableNative": true}}, "SipTransport": {"TransportConfig": {"port": 6000, "portRange": 0, "publicAddress": "", "boundAddress": "", "qosType": 0, "qosParams": {"qos.flags": 0, "qos.dscp_val": 0, "qos.so_prio": 0, "qos.wmm_prio": 0}, "TlsConfig": {"CaListFile": "", "certFile": "", "privKeyFile": "", "password": "", "CaBuf": "", "certBuf": "", "privKeyBuf": "", "method": 0, "ciphers": [], "verifyServer": false, "verifyClient": false, "requireClientCert": false, "msecTimeout": 0, "qosType": 0, "qosParams": {"qos.flags": 0, "qos.dscp_val": 0, "qos.so_prio": 0, "qos.wmm_prio": 0}, "qosIgnoreError": true}}}, "accounts": [{"AccountConfig": {"priority": 0, "idUri": "sip:<EMAIL>", "AccountRegConfig": {"registrarUri": "sip:pjsip.org", "registerOnAdd": true, "timeoutSec": 300, "retryIntervalSec": 300, "firstRetryIntervalSec": 0, "randomRetryIntervalSec": 10, "delayBeforeRefreshSec": 5, "dropCallsOnFail": false, "unregWaitMsec": 4000, "proxyUse": 3, "contactParams": "", "headers": []}, "AccountSipConfig": {"proxies": ["sip:pjsip.org;transport=tcp"], "contactForced": "", "contactParams": "", "contactUriParams": "", "authInitialEmpty": false, "authInitialAlgorithm": "", "transportId": -1, "authCreds": [{"scheme": "Digest", "realm": "*", "username": "test", "dataType": 0, "data": "passwd", "akaK": "", "akaOp": "", "akaAmf": ""}]}, "AccountCallConfig": {"holdType": 0, "prackUse": 0, "timerUse": 1, "timerMinSESec": 90, "timerSessExpiresSec": 1800}, "AccountPresConfig": {"publishEnabled": false, "publishQueue": true, "publishShutdownWaitMsec": 2000, "pidfTupleId": "", "headers": []}, "AccountMwiConfig": {"enabled": false, "expirationSec": 3600}, "AccountNatConfig": {"sipStunUse": 0, "mediaStunUse": 2, "nat64Opt": 0, "iceEnabled": false, "iceMaxHostCands": -1, "iceAggressiveNomination": true, "iceNominatedCheckDelayMsec": 400, "iceWaitNominationTimeoutMsec": 10000, "iceNoRtcp": false, "iceAlwaysUpdate": true, "turnEnabled": false, "turnServer": "", "turnConnType": 17, "turnUserName": "", "turnPasswordType": 0, "turnPassword": "", "contactRewriteUse": 1, "contactRewriteMethod": 6, "viaRewriteUse": 1, "sdpNatRewriteUse": 0, "sipOutboundUse": 1, "sipOutboundInstanceId": "", "sipOutboundRegId": "", "udpKaIntervalSec": 15, "udpKaData": "\r\n", "contactUseSrcPort": 1}, "AccountMediaConfig": {"lockCodecEnabled": true, "streamKaEnabled": false, "srtpUse": 0, "srtpSecureSignaling": 1, "SrtpOpt": {"cryptos": [], "keyings": []}, "ipv6Use": 0, "TransportConfig": {"port": 4000, "portRange": 0, "publicAddress": "", "boundAddress": "", "qosType": 0, "qosParams": {"qos.flags": 0, "qos.dscp_val": 0, "qos.so_prio": 0, "qos.wmm_prio": 0}, "TlsConfig": {"CaListFile": "", "certFile": "", "privKeyFile": "", "password": "", "CaBuf": "", "certBuf": "", "privKeyBuf": "", "method": 0, "ciphers": [], "verifyServer": false, "verifyClient": false, "requireClientCert": false, "msecTimeout": 0, "qosType": 0, "qosParams": {"qos.flags": 0, "qos.dscp_val": 0, "qos.so_prio": 0, "qos.wmm_prio": 0}, "qosIgnoreError": true}}, "rtcpMuxEnabled": false}, "AccountVideoConfig": {"autoShowIncoming": false, "autoTransmitOutgoing": false, "windowFlags": 0, "defaultCaptureDevice": -1, "defaultRenderDevice": -2, "rateControlMethod": 0, "rateControlBandwidth": 0, "startKeyframeCount": 0, "startKeyframeInterval": 0}}, "buddies": []}, {"AccountConfig": {"priority": 0, "idUri": "sip:<EMAIL>", "AccountRegConfig": {"registrarUri": "sip:pjsip.org", "registerOnAdd": true, "timeoutSec": 300, "retryIntervalSec": 300, "firstRetryIntervalSec": 0, "randomRetryIntervalSec": 10, "delayBeforeRefreshSec": 5, "dropCallsOnFail": false, "unregWaitMsec": 4000, "proxyUse": 3, "contactParams": "", "headers": []}, "AccountSipConfig": {"proxies": ["sip:pjsip.org;transport=tcp"], "contactForced": "", "contactParams": "", "contactUriParams": "", "authInitialEmpty": false, "authInitialAlgorithm": "", "transportId": -1, "authCreds": [{"scheme": "Digest", "realm": "*", "username": "test", "dataType": 0, "data": "passwd", "akaK": "", "akaOp": "", "akaAmf": ""}]}, "AccountCallConfig": {"holdType": 0, "prackUse": 0, "timerUse": 1, "timerMinSESec": 90, "timerSessExpiresSec": 1800}, "AccountPresConfig": {"publishEnabled": false, "publishQueue": true, "publishShutdownWaitMsec": 2000, "pidfTupleId": "", "headers": []}, "AccountMwiConfig": {"enabled": false, "expirationSec": 3600}, "AccountNatConfig": {"sipStunUse": 0, "mediaStunUse": 2, "nat64Opt": 0, "iceEnabled": false, "iceMaxHostCands": -1, "iceAggressiveNomination": true, "iceNominatedCheckDelayMsec": 400, "iceWaitNominationTimeoutMsec": 10000, "iceNoRtcp": false, "iceAlwaysUpdate": true, "turnEnabled": false, "turnServer": "", "turnConnType": 17, "turnUserName": "", "turnPasswordType": 0, "turnPassword": "", "contactRewriteUse": 1, "contactRewriteMethod": 6, "viaRewriteUse": 1, "sdpNatRewriteUse": 0, "sipOutboundUse": 1, "sipOutboundInstanceId": "", "sipOutboundRegId": "", "udpKaIntervalSec": 15, "udpKaData": "\r\n", "contactUseSrcPort": 1}, "AccountMediaConfig": {"lockCodecEnabled": true, "streamKaEnabled": false, "srtpUse": 0, "srtpSecureSignaling": 1, "SrtpOpt": {"cryptos": [], "keyings": []}, "ipv6Use": 0, "TransportConfig": {"port": 4000, "portRange": 0, "publicAddress": "", "boundAddress": "", "qosType": 0, "qosParams": {"qos.flags": 0, "qos.dscp_val": 0, "qos.so_prio": 0, "qos.wmm_prio": 0}, "TlsConfig": {"CaListFile": "", "certFile": "", "privKeyFile": "", "password": "", "CaBuf": "", "certBuf": "", "privKeyBuf": "", "method": 0, "ciphers": [], "verifyServer": false, "verifyClient": false, "requireClientCert": false, "msecTimeout": 0, "qosType": 0, "qosParams": {"qos.flags": 0, "qos.dscp_val": 0, "qos.so_prio": 0, "qos.wmm_prio": 0}, "qosIgnoreError": true}}, "rtcpMuxEnabled": false}, "AccountVideoConfig": {"autoShowIncoming": false, "autoTransmitOutgoing": false, "windowFlags": 0, "defaultCaptureDevice": -1, "defaultRenderDevice": -2, "rateControlMethod": 0, "rateControlBandwidth": 0, "startKeyframeCount": 0, "startKeyframeInterval": 0}}, "buddies": []}]}