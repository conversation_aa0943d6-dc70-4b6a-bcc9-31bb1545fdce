<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">

	<property name="log.level" value="DEBUG" />
	<property name="log.dir" value="${project.basedir}/.logs" />

	<appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
		<withJansi>true</withJansi>
		<encoder>
			<pattern>%magenta(%d{"yyyy-MM-dd HH:mm:ss,SSS"}) [%thread] %highlight(%-5level) %cyan(%logger{15}) - %msg %n</pattern>
		</encoder>
	</appender>

	<appender name="File" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${log.dir}/${project.artifactId}.log</file>
		<encoder>
			<pattern>%d{"yyyy-MM-dd HH:mm:ss,SSS"} [%thread] %-5level %logger{15} %msg %n</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${log.dir}/%d{yyyy-MM, aux}/${project.artifactId}-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>32MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
	</appender>

	<root level="${log.level}">
		<appender-ref ref="Console" />
		<appender-ref ref="File" />
	</root>
</configuration>

