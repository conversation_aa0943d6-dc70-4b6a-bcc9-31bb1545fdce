package com.wacai.loan.yo.pjsua2;

import org.junit.*;
import java.util.Scanner;
import org.pjsip.pjsua2.*;
import lombok.SneakyThrows;
import org.junit.runner.RunWith;
import lombok.extern.slf4j.Slf4j;
import static org.pjsip.pjsua2.pj_log_decoration.*;
import org.springframework.test.context.ContextConfiguration;
import javax.annotation.Resource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import static org.pjsip.pjsua2.pjmedia_file_player_option.PJMEDIA_FILE_NO_LOOP;

import javax.annotation.Resource;

/**
 * <AUTHOR> <br>
 * <EMAIL> <br>
 * Jul 30, 2018 17:47:31
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath*:spring/spring-*.xml")
public class TestDriver {
    @Resource
    private Endpoint endpoint;
    @Resource
    private EpConfig endpointConfig;
    @Resource
    private TransportConfig transportConfig;

    @BeforeClass
    public static void setup() {
        Pjsua2ShareLibLoader.load();
    }

    @AfterClass
    public static void shutdown() {
        Pjsua2ShareLibLoader.offload();
    }

    @Before
    @SneakyThrows
    public void init() {
        endpoint.libCreate();
        // Initialize endpoint
        endpoint.libInit(endpointConfig);
        endpoint.transportCreate(
            pjsip_transport_type_e.PJSIP_TRANSPORT_UDP,
            transportConfig);

        // Start the library
        endpoint.libStart();
    }

    @After
    public void destroy() {
        try {
            log.info("Destroy PJSUA2 lib.");
            endpoint.libDestroy();
        }
        catch (Exception e) {
            log.error("Destroy PJSUA2 lib error caused.", e);
        }
        finally {
            endpointConfig.delete();
            transportConfig.delete();
            endpoint.delete();
        }
    }

    @Test
    @SneakyThrows
    public void run() {
        endpoint.libRegisterThread(Thread.currentThread().getName());
        final AudioMediaPlayer player = new AudioMediaPlayer() {
            /**
             * {@inheritDoc}
             */
            @Override
            public void onEof2() {
                log.info("On player EOF.");
                // delete();
            }
        };
        player.createPlayer(TestDriver.class.getResource("/sample.wav").getFile(), PJMEDIA_FILE_NO_LOOP);
        log.info("Player [{}] [{}].", player.getRxLevel(), player.getTxLevel());
        final AudioMedia playbackDevMedia = endpoint.audDevManager().getPlaybackDevMedia();
        log.info("Audio device [{}] [{}].", playbackDevMedia.getRxLevel(), playbackDevMedia.getTxLevel());
        final AudioMediaTransmitParam param = new AudioMediaTransmitParam();
        log.info("Param level [{}].", param.getLevel());
        param.setLevel(4.0F);
        player.startTransmit2(playbackDevMedia, param);

        final CodecInfoVector2 codecInfos = endpoint.codecEnum2();
        for (final CodecInfo codecInfo : codecInfos) {
            final String codecId = codecInfo.getCodecId();
            codecInfo.setPriority((short) 0);
            log.info("Codec [{}].", codecId);
        }

        new Scanner(System.in).next();
    }

    @Test
    @SneakyThrows
    public void testLog() {
        endpoint.libRegisterThread(Thread.currentThread().getName());
        final CodecInfoVector2 codecInfos = endpoint.codecEnum2();
        for (final CodecInfo codecInfo : codecInfos) {
            log.info("Codec [{}] [{}].", codecInfo.getCodecId(), codecInfo.getPriority());
        }
    }

    @Test
    public void testLogDector() {
        final LogConfig logConfig = endpointConfig.getLogConfig();
        final long decor = logConfig.getDecor();
        log.info("default decor: [{}].", decor);
        log.info("no cr & nl decor: [{}].",
            decor & ~(pj_log_decoration.PJ_LOG_HAS_CR |
                pj_log_decoration.PJ_LOG_HAS_NEWLINE |
                pj_log_decoration.PJ_LOG_HAS_TIME |
                pj_log_decoration.PJ_LOG_HAS_MICRO_SEC |
                PJ_LOG_HAS_INDENT |
                PJ_LOG_HAS_SPACE |
                PJ_LOG_HAS_THREAD_SWC
            ));
    }
}
