package com.wacai.loan.counselor.jedis;

import com.wacai.loan.redis.client.RedisClient;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018/9/14
 */
@Component
@Slf4j
public class JedisService {

    /**
     * jedis 客户端
     */
    @Resource
    private RedisClient redisClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 删除给定的一个或多个 key， 不存在的 key 会被忽略
     */
    public boolean del(String key) {
        return redisClient.del(key);
    }

    /**
     * 为给定 key 设置生存时间，当 key 过期时(生存时间为 0 )，它会被自动删除。
     *
     * @param seconds 过期时间
     */
    public boolean expire(String key, int seconds) {
        return redisClient.expire(key, seconds);
    }

    /**
     * 判断一个 key 是否存在
     */
    public boolean exists(String key) {
        return redisClient.exists(key);
    }

    /**
     * 将 key 的值设为 value ，当且仅当 key 不存在。
     * 若给定的 key 已经存在，则 SETNX 不做任何动作。
     * 设置过期时间
     * <p>
     *
     * @param seconds 过期时间
     */
    public boolean setNx(String key, String value, int seconds) {
        return redisClient.setNx(key, value, seconds);
    }


    /**
     * 将值 value 关联到 key ，并设置有效时间 seconds (以秒为单位)。
     * 如果 key 已经存在， SETEX 命令将覆写旧值
     * 推荐使用带有效时间
     *
     * @param seconds 秒
     */
    public boolean set(String key, String value, int seconds) {
        return redisClient.set(key, value, seconds);
    }

    public void rPush(String key, String value) {
        stringRedisTemplate.opsForList().rightPush(key, value);
    }

    public List<String> getList(String key, int num) {
        Long listLength = stringRedisTemplate.opsForList().size(key);
        long startIndex = -num;
        if (listLength < num) {
            startIndex = -listLength;
        }
        long end = -1;
        return stringRedisTemplate.opsForList().range(key, startIndex, end);
    }

    /**
     * 返回 key 所关联的字符串值
     *
     * @return 当 key 不存在时，返回 null ，否则，返回 key 的值。
     */
    public String get(String key) {
        return redisClient.get(key);
    }


    /**
     * 将 key 中储存的数字值增一
     * 如果 key 不存在，那么 key 的值会先被初始化为 0 ，然后再执行 INCR 操作
     *
     * @return DECR 命令之后 key 的值
     * @操作失败
     */
    public Long incr(String key) {
        return redisClient.incr(key);
    }

}
