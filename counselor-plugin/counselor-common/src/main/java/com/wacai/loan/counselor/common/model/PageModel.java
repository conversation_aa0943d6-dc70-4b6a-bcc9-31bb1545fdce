package com.wacai.loan.counselor.common.model;

import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
public class PageModel<T> {

    private PageInfo pageInfo;
    /**
     * 数据
     */
    private List<T> data;

    public PageModel() {
        data = Collections.emptyList();
    }

    @Data
    public class PageInfo {

        /**
         * pageIndex
         */
        private int current;

        /**
         * pageSize
         */
        private int pageSize;

        private long total;
    }

}


