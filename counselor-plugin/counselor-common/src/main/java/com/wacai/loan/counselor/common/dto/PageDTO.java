package com.wacai.loan.counselor.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/16
 */
@Data
public class PageDTO<T> implements Serializable {

    private static final long serialVersionUID = 1L;
    private List<T> data;
    private PageInfo pageInfo;

    public static <T> PageDTO<T> emptyPage(){
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.setData(Collections.emptyList());
        PageInfo pageInfo = new PageInfo();
        pageDTO.setPageInfo(pageInfo);
        return pageDTO;
    }

    public static <T> PageDTO<T> build(List<T> data, PageInfo pageInfo) {
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.setData(data);
        pageDTO.setPageInfo(pageInfo);
        return pageDTO;
    }

    public static class PageInfo {
        int current;
        int pageSize;
        long total;

        public PageInfo() {
        }

        public PageInfo(int current, long total, int pageSize) {
            this.current = current;
            this.total = total;
            this.pageSize = pageSize;
        }

        public int getCurrent() {
            return current;
        }

        public void setCurrent(int current) {
            this.current = current;
        }

        public long getTotal() {
            return total;
        }

        public void setTotal(long total) {
            this.total = total;
        }

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }
    }
}
