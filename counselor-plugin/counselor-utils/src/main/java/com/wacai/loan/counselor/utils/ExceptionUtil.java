/*
 * @Author: shunhua
 * @Date: 2024-01-05 18:03:41
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-18 11:21:59
 * @Description: 
 */
package com.wacai.loan.counselor.utils;

/**
 * <AUTHOR> <br>
 * <EMAIL> <br>
 * Aug 01, 2018 10:16
 */
public class ExceptionUtil {

    /**
     * keepThrow exception
     * @param e throwable
     * @return exception runtime exception
     */
    public static RuntimeException keepThrow(final Throwable e) {
        if (e instanceof RuntimeException) {
            throw (RuntimeException) e;
        }

        if (e instanceof Error) {
            throw (Error) e;
        }

        throw new RuntimeException(e);
    }
}
