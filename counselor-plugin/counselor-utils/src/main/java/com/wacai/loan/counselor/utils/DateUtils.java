package com.wacai.loan.counselor.utils;

import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by xiaofeidao on 2018/10/3.
 */
public class DateUtils {

    public enum TimeType {
        DEFAULT("yyyy-MM-dd|HH:mm:ss"),
        yyyyMMddHH("yyyyMMddHH"),
        yyyyMMddHHmmssSSS("yyyyMMddHHmmssSSS"),
        yyyyMMddHHmmss("yyyyMMddHHmmss"),
        yyyyMMdd("yyyyMMdd"),
        yyyy_MM_dd("yyyy-MM-dd"),
        yyyySlashMMSlashdd("yyyy/MM/dd"),
        HHmmss("HHmmss"),
        yyyy_MM_ddHHmmSS("yyyy-MM-dd HH:mm:SS"),
        yyyy_MM_ddHHmmss("yyyy-MM-dd HH:mm:ss"),
        yyyy_MM("yyyy-MM"),
        yyyy("yyyy");

        String type;

        private TimeType(String type) {
            this.type = type;
        }

        public String getType() {
            return type;
        }
    }

    /**
     * Instance SimpleDateFormat
     *
     * @param timeType
     * @return
     */
    public static SimpleDateFormat getDateFormat(TimeType timeType) {
        if (timeType == null) {
            timeType = TimeType.DEFAULT;
        }
        return new SimpleDateFormat(timeType.getType());
    }

    public static String formatDate(Date date, TimeType timeType) {
        if (date == null) {
            return "";
        }
        return getDateFormat(timeType).format(date);
    }

    public static Date parseDate(String date, TimeType timeType) {
        SimpleDateFormat sdf = getDateFormat(timeType);
        try {
            return sdf.parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Calendar getDayStart(Calendar now) {
        now.set(Calendar.HOUR_OF_DAY, 0);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);
        return now;
    }

    public static String getTodayStr() {
        Date todayEnd = getTodayStart();
        return formatDate(todayEnd, TimeType.yyyyMMdd);
    }

    public static Date getTodayStart() {
        Calendar now = Calendar.getInstance();
        return DateUtils.getDayStart(now).getTime();
    }

    public static String getTodayStartStr() {
        Date todayStart = getTodayStart();
        return formatDate(todayStart, TimeType.yyyy_MM_ddHHmmSS);
    }

    public static String getTodayStartStr(TimeType timeType) {
        Date todayStart = getTodayStart();
        return formatDate(todayStart, timeType);
    }

    public static Date addDate(Date date, int day) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, day);
        return cal.getTime();
    }

    public static Date addDate(Date date, long min) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MINUTE, (int) min);
        return cal.getTime();
    }

    public static Date addMilliSecondDate(Date date, int milliSecond) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MILLISECOND, milliSecond);
        return cal.getTime();
    }

    public static Date addDateBySecond(Date date, long second) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.SECOND, (int) second);
        return cal.getTime();
    }

    public static Date getTodayEnd() {
        Calendar now = Calendar.getInstance();
        return DateUtils.getDayEnd(now).getTime();
    }

    public static String getTodayEndStr() {
        Date todayEnd = getTodayEnd();
        return formatDate(todayEnd, TimeType.yyyy_MM_ddHHmmSS);
    }

    public static Date getYesterday() {
        Calendar cal = Calendar.getInstance();
        cal.set(5, cal.get(5) - 1);
        return cal.getTime();
    }

    public static Date getYesterdayStart() {
        Calendar cal = Calendar.getInstance();
        cal.set(5, cal.get(5) - 1);
        return getDayStart(cal).getTime();
    }

    public static String getYesterdayStartStr() {
        Date yesterdayStart = getYesterdayStart();
        return formatDate(yesterdayStart, TimeType.yyyy_MM_ddHHmmSS);
    }

    public static Date getTomorrowStart() {
        Calendar cal = Calendar.getInstance();
        cal.set(5, cal.get(5) + 1);
        return getDayStart(cal).getTime();
    }

    public static String getTomorrowStartStr() {
        Date tomorrowStart = getTomorrowStart();
        return formatDate(tomorrowStart, TimeType.yyyy_MM_ddHHmmSS);
    }

    public static Date getYesterdayMiddle() {
        Calendar now = Calendar.getInstance();
        now.set(Calendar.DAY_OF_MONTH, now.get(Calendar.DAY_OF_MONTH) - 1);
        now.set(Calendar.HOUR_OF_DAY, 12);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);
        return now.getTime();
    }

    public static Date getTodaySpecificHour(int hour) {
        Calendar now = Calendar.getInstance();
        now.set(Calendar.HOUR_OF_DAY, hour);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);
        return now.getTime();
    }


    public static Calendar getDayEnd(Calendar now) {
        now.add(Calendar.DAY_OF_YEAR, 1);
        now.set(Calendar.HOUR_OF_DAY, 0);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);
        now.add(Calendar.MILLISECOND, -1);
        return now;
    }

    public static Date parseDateFromTimestamp(String timestamp) {
        if (StringUtils.isBlank(timestamp)) {
            return null;
        }
        return new Date(Long.parseLong(timestamp));
    }

    public static Integer calcDaysPastDue(Date dueDate) {
        if (dueDate == null) {
            return null;
        }
        ZoneId defaultZoneId = ZoneId.systemDefault();
        LocalDate now = new Date().toInstant().atZone(defaultZoneId).toLocalDate();
        LocalDate localDate = dueDate.toInstant().atZone(defaultZoneId).toLocalDate();
        Long overdueDays = ChronoUnit.DAYS.between(localDate, now);
        return overdueDays.intValue();
    }

    public static Date parseDateUnKnowFormat(String dateStr) {
        Date date = null;
        try {
            date = getDateFormat(TimeType.DEFAULT).parse(dateStr);
        } catch (Exception e) {
        }
        if (date != null) {
            return date;
        }
        try {
            date = getDateFormat(TimeType.yyyy_MM_ddHHmmSS).parse(dateStr);
        } catch (Exception e) {
        }
        if (date != null) {
            return date;
        }
        try {
            date = getDateFormat(TimeType.yyyy_MM_dd).parse(dateStr);
        } catch (Exception e) {
        }
        if (date != null) {
            return date;
        }
        try {
            date = getDateFormat(TimeType.yyyySlashMMSlashdd).parse(dateStr);
        } catch (Exception e) {
        }
        if (date != null) {
            return date;
        }
        return null;
    }

}
