package com.wacai.loan.counselor.utils;

import org.apache.commons.lang3.time.DateUtils;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2019/4/27
 */
public class ConvertValueUtils {

    private static Map<String, Method> map = new HashMap<>();

    static {
        try {
            initFieldType(Double.class.getTypeName(), "switchDouble");
            initFieldType(double.class.getTypeName(), "switchDouble");
            initFieldType(Float.class.getTypeName(), "switchFloat");
            initFieldType(float.class.getTypeName(), "switchFloat");
            initFieldType(Short.class.getTypeName(), "switchShort");
            initFieldType(short.class.getTypeName(), "switchShort");
            initFieldType(Boolean.class.getTypeName(), "switchBoolean");
            initFieldType(boolean.class.getTypeName(), "switchBoolean");
            initFieldType(Integer.class.getTypeName(), "switchInt");
            initFieldType(int.class.getTypeName(), "switchInt");
            initFieldType(Long.class.getTypeName(), "switchLong");
            initFieldType(long.class.getTypeName(), "switchLong");
            initFieldType(Date.class.getTypeName(), "switchDate");
            initFieldType(BigDecimal.class.getTypeName(), "switchBigDecimal");
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
    }

    private static void initFieldType(String clazzName, String methodName) throws NoSuchMethodException {
        Method method = ConvertValueUtils.class.getDeclaredMethod(methodName, String.class);
        map.put(clazzName, method);
    }

    public static Object convert(String type, String value) {
        try {
            Method method = map.get(type);
            if (method == null) {
                return value;
            }
            return method.invoke(null, value);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static double switchDouble(String value) {
        return Double.parseDouble(value);
    }

    public static int switchInt(String value) {
        return Integer.parseInt(value);
    }

    public static long switchLong(String value) {
        return Long.parseLong(value);
    }

    public static boolean switchBoolean(String value) {
        return Boolean.parseBoolean(value);
    }

    public static short switchShort(String value) {
        return Short.parseShort(value);
    }

    public static float switchFloat(String value) {
        return Float.parseFloat(value);
    }

    public static Date switchDate(String value) throws ParseException {
        if(value.length() == 10){
            return DateUtils.parseDate(value, "yyyy-MM-dd");
        }
        return DateUtils.parseDate(value, "yyyy-MM-dd HH:mm:ss");
    }

    public static BigDecimal switchBigDecimal(String value) {
        return  new BigDecimal(value);
    }

}
