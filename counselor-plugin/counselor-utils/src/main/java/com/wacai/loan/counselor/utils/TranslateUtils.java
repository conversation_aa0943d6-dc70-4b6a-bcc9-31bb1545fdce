package com.wacai.loan.counselor.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2018/3/5
 */
@Slf4j
public class TranslateUtils {

    private static final BigDecimal HUNDRED = BigDecimal.valueOf(100);

    public static String batchNo = "";
    public static String operator = "";
    public static Boolean LOAN_EXCEL_DONE = false;
    public static Boolean REPAYMENT_EXCEL_DONE = false;
    public static AtomicInteger LOANSUCCESS = new AtomicInteger(0);
    public static AtomicInteger LOANERROR = new AtomicInteger(0);
    public static AtomicInteger REPAYMENTSUCCESS = new AtomicInteger(0);
    public static AtomicInteger REPAYMENTERROR = new AtomicInteger(0);

    public static final String INIT = "INIT";

    public static final String SUCCESS = "SUCCESS";

    public static final String ERROR = "ERROR";

    private static TranslateUtils translateUtils;

    private static final int SHORT_IDNO_LENGTH = 15;

    private static final int LONG_IDNO_LENGTH = 18;

    private static final int DIVISOR = 2;


    public static Gender getGender(String idNo) {
        if (StringUtils.isBlank(idNo)) {
            return null;
        }
        String gender;
        if (idNo.length() == SHORT_IDNO_LENGTH) {
            gender = idNo.substring(14, 15);
        } else if (idNo.length() == LONG_IDNO_LENGTH) {
            gender = idNo.substring(16, 17);
        } else {
            return null;
        }
        if (Integer.parseInt(gender) % DIVISOR == 0) {
            return Gender.FEMALE;
        } else {
            return Gender.MALE;
        }
    }

    public static String getGenderFromIdno(String idNo) {
        Gender gender = getGender(idNo);
        return Optional.ofNullable(gender).map(Enum::name).orElse(null);
    }

    public static String getBirthdayFromIdno(String idno) {
        return getYearByIdCard(idno) + "-" + getMonthByIdCard(idno) + "-" + getDayByIdCard(idno);
    }

    /**
     * 根据身份编号获取生日年
     *
     * @param idCard 身份编号
     * @return 生日(yyyy)
     */
    private static Short getYearByIdCard(String idCard) {
        return Short.valueOf(idCard.substring(6, 10));
    }

    /**
     * 根据身份编号获取生日月
     *
     * @param idCard 身份编号
     * @return 生日(MM)
     */
    private static Short getMonthByIdCard(String idCard) {
        return Short.valueOf(idCard.substring(10, 12));
    }

    /**
     * 根据身份编号获取生日天
     *
     * @param idCard 身份编号
     * @return 生日(dd)
     */
    private static Short getDayByIdCard(String idCard) {
        return Short.valueOf(idCard.substring(12, 14));
    }

    public static Integer strToInteger(String str) {
        return Optional.ofNullable(str).filter(s -> StringUtils.isNotEmpty(str))
                .map(e->new BigDecimal(e).intValue()).orElse(null);
    }

    public static BigDecimal strToBigDecimal(String str) {
        return Optional.ofNullable(str).filter(s -> StringUtils.isNotEmpty(str))
                .map(BigDecimal::new).orElse(BigDecimal.ZERO);
    }

    /**
     * strToBigDecimal:带分隔符的字符串转换为BigDecimal
     *
     * @param str
     * @param separator
     * @return
     * <AUTHOR>
     * @date 2020-01-17 09:49:43
     */
    public static BigDecimal strToBigDecimal(String str, String separator) {
        if (StringUtils.isBlank(str) || "null".equals(str)) {
            return BigDecimal.ZERO;
        }
        BigDecimal result = BigDecimal.ZERO;
        try {
            String replaced = str.replace(separator, "");
            result = new BigDecimal(replaced);
        } catch (NumberFormatException e) {
            log.info("strToBigDecimal: str = {}, separator = {}", str, separator, e);
        }
        return result;
    }

    /**
     * 成员变量String的字符串 、
     *
     * @param object
     * @throws IllegalAccessException
     */
    public static void translateNull(Object object) {
        try {
            Field[] fields = object.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                if (field.getType().isAssignableFrom(String.class)) {
                    String str = (String) field.get(object);
                    if (StringUtils.isBlank(str)) {
                        field.set(object, "");
                    }
                }
            }
        } catch (Exception e) {

        }
    }

    /**
     * 科学计数法的手机号转成手机号
     * 15.04385935E+9
     * 15043859350
     *
     * @param number
     * @return
     */
    public static String scienNumToMobile(String number) {
        if (StringUtils.isBlank(number)) {
            return number;
        }
        if (number.contains("E")) {
            String str = number.substring(0, number.indexOf("E")).replaceAll("\\.", "");
            while (str.length() < 11) {
                str += "0";
            }
            return str;
        } else {
            return number;
        }
    }

}
