package com.wacai.loan.counselor.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegexUtils {

    private static Pattern UNDERLINE_PATTERN = Pattern.compile("_([a-z])");

    public static String underlineToHump(String str) {
        //正则匹配下划线及后一个字符，删除下划线并将匹配的字符转成大写
        Matcher matcher = UNDERLINE_PATTERN.matcher(str);
        StringBuffer sb = new StringBuffer(str);
        if (matcher.find()) {
            sb = new StringBuffer();
            //将当前匹配的子串替换成指定字符串，并且将替换后的子串及之前到上次匹配的子串之后的字符串添加到StringBuffer对象中
            //正则之前的字符和被替换的字符
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
            //把之后的字符串也添加到StringBuffer对象中
            matcher.appendTail(sb);
        } else {
            //去除除字母之外的前面带的下划线
            return sb.toString().replaceAll("_", "");
        }
        return underlineToHump(sb.toString());
    }

    /**
     * 驼峰转下划线。
     *
     * @return
     */
    public static String underscoreName(String camel) {
        char[] chars = camel.toCharArray();
        StringBuilder sb = new StringBuilder();
        for (int i = 0, j = 0; i < chars.length; i++, j++) {
            char s = chars[i];
            if (s + 1 > 65 && s + 1 < 91) {
                char _ = 95;
                sb.append(_);
                j++;
                sb.append(s);
                continue;
            }
            sb.append(s);
        }
        return sb.toString().toUpperCase();

    }
}
