package com.wacai.loan.counselor.utils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019/4/27
 */
public class ReflectionUtils {

    /**
     * 循环向上转型, 获取对象的 DeclaredFields
     *
     * @return 父类中的属性对象
     */
    public static Map<String, Field> getDeclaredFields(Class<?> clazz) {
        Map<String, Field> map = new HashMap<String, Field>();
        for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
            try {
                Field[] fields = clazz.getDeclaredFields();
                for (Field f : fields) {
                    f.setAccessible(true);
                    map.put(f.getName(), f);
                }
            } catch (Exception e) {
                //这里甚么都不要做！并且这里的异常必须这样写，不能抛出去。
                //如果这里的异常打印或者往外抛，则就不会执行clazz = clazz.getSuperclass(),最后就不会进入到父类中了
            }
        }
        return map;
    }

    /**
     * 循环向上转型, 获取对象的 DeclaredFields
     *
     * @return 父类中的属性对象
     */
    public static Map<String, Method> getDeclaredMethods(Class<?> clazz) {
        Map<String, Method> map = new HashMap<>();
        for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
            try {
                Method[] methods = clazz.getDeclaredMethods();
                for (Method m : methods) {
                    m.setAccessible(true);
                    map.put(m.getName(), m);
                }
            } catch (Exception e) {
                //这里甚么都不要做！并且这里的异常必须这样写，不能抛出去。
                //如果这里的异常打印或者往外抛，则就不会执行clazz = clazz.getSuperclass(),最后就不会进入到父类中了
            }
        }
        return map;
    }

    /**
     * 循环向上转型, 获取对象的 DeclaredField
     *
     * @return 父类中的属性对象
     */
    public static Field getDeclaredField(Class<?> clazz, String filedName) {
        for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
            try {
                Field field = clazz.getDeclaredField(filedName);
                field.setAccessible(true);
                return field;
            } catch (Exception e) {
                //这里甚么都不要做！并且这里的异常必须这样写，不能抛出去。
                //如果这里的异常打印或者往外抛，则就不会执行clazz = clazz.getSuperclass(),最后就不会进入到父类中了
            }
        }
        return null;
    }

    /**
     * 循环向上转型, 获取对象的 DeclaredField
     *
     * @return 父类中的属性对象
     */
    public static Method getDeclaredMethod(Class<?> clazz, String name, Class<?>... paramTypes) {
        for (; clazz != null && clazz != Object.class; clazz = clazz.getSuperclass()) {
            try {
                return clazz.getDeclaredMethod(name, paramTypes);
            } catch (Exception e) {
                //这里甚么都不要做！并且这里的异常必须这样写，不能抛出去。
                //如果这里的异常打印或者往外抛，则就不会执行clazz = clazz.getSuperclass(),最后就不会进入到父类中了
            }
        }
        return null;
    }

    /**
     * 循环向上转型, 获取对象的 DeclaredField
     *
     * @return 父类中的属性对象
     */
    public static List<Method> getDeclaredMethodsWithAnnotation(Class<?> clazz, Class<? extends Annotation> clazzAnnotation) {
        Map<String, Method> methodMap = getDeclaredMethods(clazz);
        Set<Map.Entry<String, Method>> set = methodMap.entrySet();
        List<Method> list = new ArrayList<>();
        for (Map.Entry<String, Method> entry : set) {
            Annotation annotation = entry.getValue().getAnnotation(clazzAnnotation);
            if (annotation == null) {
                continue;
            }
            list.add(entry.getValue());
        }
        return list;
    }
}
