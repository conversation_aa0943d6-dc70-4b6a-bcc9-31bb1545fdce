<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
        http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.2</version>
        <relativePath />
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.wacai.loan</groupId>
    <artifactId>counselor-voice</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>counselor-voice</name>
    <description>counselor</description>

    <properties>
        <counselor.version>0.0.1-SNAPSHOT</counselor.version>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>utf-8</project.build.sourceEncoding>
        <spring.version>5.0.4.RELEASE</spring.version>
        <counselor.aspectj>1.8.13</counselor.aspectj>
        <prophet.spring>2.2.1-amc-FIXBUG</prophet.spring>
        <elasticsearch.version>5.6.16</elasticsearch.version>
        <start-class></start-class>
    </properties>

    <organization>
        <name>Wacai</name>
        <url>http://www.wacai.com</url>
    </organization>

    <modules>
        <module>counselor-plugin</module>
        <module>counselor-voice-starter</module>
        <module>counselor-service</module>
    </modules>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.theokanning.openai-gpt3-java</groupId>
                <artifactId>api</artifactId>
                <version>0.11.0</version>
            </dependency>

            <dependency>
                <groupId>com.theokanning.openai-gpt3-java</groupId>
                <artifactId>client</artifactId>
                <version>0.11.0</version>
            </dependency>

            <dependency>
                <groupId>com.theokanning.openai-gpt3-java</groupId>
                <artifactId>service</artifactId>
                <version>0.11.0</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>counselor-service</artifactId>
                <version>${counselor.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>counselor-pjsua2</artifactId>
                <version>${counselor.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>counselor-common-service</artifactId>
                <version>${counselor.version}</version>
            </dependency>


            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>counselor-lock-jedis</artifactId>
                <version>${counselor.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>counselor-common</artifactId>
                <version>${counselor.version}</version>
            </dependency>
            
            
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>counselor-jedis</artifactId>
                <version>${counselor.version}</version>
            </dependency>
            
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>counselor-utils</artifactId>
                <version>${counselor.version}</version>
            </dependency>
            



            <!--            <dependency>-->
            <!--                <groupId>org.aspectj</groupId>-->
            <!--                <artifactId>aspectjrt</artifactId>-->
            <!--                <version>${counselor.aspectj}</version>-->
            <!--            </dependency>-->

            <dependency>
                <groupId>com.dyuproject.protostuff</groupId>
                <artifactId>protostuff-core</artifactId>
                <version>1.0.8</version>
            </dependency>
            <dependency>
                <groupId>com.dyuproject.protostuff</groupId>
                <artifactId>protostuff-runtime</artifactId>
                <version>1.0.8</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>1.7.25</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>4.5.5</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
                <version>1.9.3</version>
            </dependency>

            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>redis-client-sdk</artifactId>
                <version>0.0.7</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-json</artifactId>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.1.1</version>
            </dependency>

            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.22.0-GA</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>org.mvel</groupId>-->
            <!--                <artifactId>mvel2</artifactId>-->
            <!--                <version>2.2.0.Final</version>-->
            <!--            </dependency>-->

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>18.0</version>
            </dependency>
            <!-- ElasticSearch, Dubbo, Genesys SDK均使用netty，统一使用此版本 -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty</artifactId>
                <version>3.10.6.Final</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.1.3</version>
            </dependency>

            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient</artifactId>
                <version>0.12.0</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_servlet</artifactId>
                <version>0.12.0</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.45</version>
                <!--<version>6.0.6</version>-->
            </dependency>
            <!-- /Database -->

            <dependency>
                <groupId>javax.persistence</groupId>
                <artifactId>persistence-api</artifactId>
                <version>1.0.2</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.25</version>
            </dependency>

            <dependency>
                <groupId>javax.el</groupId>
                <artifactId>javax.el-api</artifactId>
                <version>3.0.0</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>javax.servlet.jsp.jstl</groupId>-->
            <!--                <artifactId>jstl-api</artifactId>-->
            <!--                <version>1.2</version>-->
            <!--            </dependency>-->
            <!-- /JavaX -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.16.14</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>2.0.9</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.5</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-jpa</artifactId>
                <version>2.7.2</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.10</version>
            </dependency>
            <dependency>
                <artifactId>commons-fileupload</artifactId>
                <groupId>commons-fileupload</groupId>
                <version>1.3.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.10.0</version>
            </dependency>
            <dependency>
                <groupId>com.wacai</groupId>
                <artifactId>middleware-toolkit</artifactId>
                <version>1.2.7</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.coderplus.maven.plugins</groupId>
                <artifactId>copy-rename-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <configuration>
                        <addResources>true</addResources>
                        <mainClass>${start-class}</mainClass>
                        <layout>ZIP</layout>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>2.6.1</version>
                    <executions>
                        <execution>
                            <id>remove-config-file</id>
                            <goals>
                                <goal>clean</goal>
                            </goals>
                            <phase>validate</phase>
                            <configuration>
                                <excludeDefaultDirectories>true</excludeDefaultDirectories>
                                <filesets>
                                    <fileset>
                                        <directory>src/main/resources/spring</directory>
                                        <includes>
                                            <include>config.properties</include>
                                        </includes>
                                    </fileset>
                                    <fileset>
                                        <directory>src/main/resources</directory>
                                        <includes>
                                            <include>application.properties</include>
                                            <include>application.yaml</include>
                                            <include>log4j2.xml</include>
                                            <include>logback.xml</include>
                                        </includes>
                                    </fileset>
                                </filesets>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.coderplus.maven.plugins</groupId>
                    <artifactId>copy-rename-maven-plugin</artifactId>
                    <version>1.0</version>
                    <executions>
                        <execution>
                            <id>copy-config-props-file</id>
                            <phase>generate-resources</phase>
                            <goals>
                                <goal>copy</goal>
                            </goals>
                            <configuration>
                                <sourceFile>src/main/resources/config_${project.activeProfiles[0].id}.properties</sourceFile>
                                <destinationFile>src/main/resources/spring/config.properties</destinationFile>
                            </configuration>
                        </execution>
                        <execution>
                            <id>copy-application-props-file</id>
                            <phase>generate-resources</phase>
                            <goals>
                                <goal>copy</goal>
                            </goals>
                            <configuration>
                                <sourceFile>src/main/resources/application_${project.activeProfiles[0].id}.properties</sourceFile>
                                <destinationFile>src/main/resources/application.properties</destinationFile>
                            </configuration>
                        </execution>
                        <execution>
                            <id>copy-application-yaml-file</id>
                            <phase>generate-resources</phase>
                            <goals>
                                <goal>copy</goal>
                            </goals>
                            <configuration>
                                <sourceFile>src/main/resources/application_${project.activeProfiles[0].id}.yaml</sourceFile>
                                <destinationFile>src/main/resources/application.yaml</destinationFile>
                            </configuration>
                        </execution>
                        <execution>
                            <id>copy-log4j2-xml-file</id>
                            <phase>generate-resources</phase>
                            <goals>
                                <goal>copy</goal>
                            </goals>
                            <configuration>
                                <sourceFile>src/main/resources/log4j2_${project.activeProfiles[0].id}.xml</sourceFile>
                                <destinationFile>src/main/resources/log4j2.xml</destinationFile>
                            </configuration>
                        </execution>
                        <execution>
                            <id>copy-logback-xml-file</id>
                            <phase>generate-resources</phase>
                            <goals>
                                <goal>copy</goal>
                            </goals>
                            <configuration>
                                <sourceFile>src/main/resources/logback_${project.activeProfiles[0].id}.xml</sourceFile>
                                <destinationFile>src/main/resources/logback.xml</destinationFile>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.7</version>
                    <configuration>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.7.0</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <compilerArgs>
                            <!--<arg>-verbose</arg>-->
                            <!--<arg>-Xlint:all,-options,-path</arg>-->
                            <arg>-Xlint:unchecked</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.4</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <phase>install</phase>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>2.10</version>
                    <executions>
                        <execution>
                            <id>copy-dependencies</id>
                            <phase>package</phase>
                            <goals>
                                <goal>copy-dependencies</goal>
                            </goals>
                            <configuration>
                                <outputDirectory>
                                    target/lib
                                </outputDirectory>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.6</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <addClasspath>true</addClasspath>
                                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            </manifest>
                            <manifestEntries>
                                <Built-By>Wacai</Built-By>
                            </manifestEntries>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-help-plugin</artifactId>
                    <version>2.2</version>
                </plugin>
                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.apache.maven.plugins</groupId>
                                        <artifactId>maven-clean-plugin</artifactId>
                                        <versionRange>
                                            [2.6.1,)
                                        </versionRange>
                                        <goals>
                                            <goal>clean</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore />
                                    </action>
                                </pluginExecution>
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>1.6.0</version>
                </plugin>
                <plugin>
                    <groupId>pl.project13.maven</groupId>
                    <artifactId>git-commit-id-plugin</artifactId>
                    <version>2.2.4</version>
                </plugin>

                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.7.9</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>report</id>
                            <phase>prepare-package</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.wacai.ops</groupId>
                    <artifactId>dependency-check-maven-plugin</artifactId>
                    <version>1.0.1</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <!-- filter resources -->
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*Mapper.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>config_*.properties</exclude>
                    <exclude>application_*.properties</exclude>
                    <exclude>application_*.yaml</exclude>
                    <exclude>log4j2_*.xml</exclude>
                    <exclude>logback_*.xml</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                    <include>**/*.txt</include>
                </includes>
                <excludes>
                    <exclude>config_*.properties</exclude>
                    <exclude>application_*.properties</exclude>
                    <exclude>application_*.yaml</exclude>
                    <exclude>log4j2_*.xml</exclude>
                    <exclude>logback_*.xml</exclude>
                </excludes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>false</filtering>
            </testResource>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.txt</include>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
            </testResource>
        </testResources>
    </build>

    <profiles>
        <profile>
            <id>init-git-hooks</id>
            <activation>
                <file>
                    <exists>.git-hooks/init.sh</exists>
                </file>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>copy-git-hooks</id>
                                <phase>validate</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <successCodes>
                                        <code>0</code>
                                        <code>1</code>
                                    </successCodes>
                                    <executable>bash</executable>
                                    <commandlineArgs>${project.basedir}/.git-hooks/init.sh</commandlineArgs>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>