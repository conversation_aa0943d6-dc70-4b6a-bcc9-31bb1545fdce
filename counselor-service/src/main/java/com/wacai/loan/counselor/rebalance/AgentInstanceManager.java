package com.wacai.loan.counselor.rebalance;

import static com.wacai.loan.counselor.opt.base.alarm.AlarmTemplateCst.ACCOUNT_ALLOT_ERROR;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.PriorityQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wacai.loan.counselor.opt.base.alarm.AlarmService;
import com.wacai.loan.counselor.opt.base.alarm.NotifierContext;
import com.wacai.loan.counselor.opt.sip.dataobject.agent.AgentDTO;
import com.wacai.loan.counselor.rebalance.zookeeper.Contants;
import com.wacai.loan.counselor.rebalance.zookeeper.ZookeeperRegistry;
import com.wacai.loan.counselor.utils.InetAddressUtil;
import com.wacai.loan.counselor.utils.Utils;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-22 11:04
 */
@Slf4j
@Service
public class AgentInstanceManager {

    @Resource
    private LinkupAccountService linkupAccountService;

    @Value("${spring.env.active:}")
    private String env;

    @Value("${voice.cluster.number:1}")
    private Integer clusterNumber;

    @Resource
    private ZookeeperRegistry zookeeperRegistry;

    @Resource
    private AlarmService alarmService;
    ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);

    public void leaderAllot() {
        // 周期检测
        executor.scheduleAtFixedRate(() -> allotAgent(), 30L, 5 * 60, TimeUnit.SECONDS);
    }

    public void setZkInstanceData(List<AgentDTO> agentDTOS) {
        zookeeperRegistry.setData(
                StringUtils.join(Contants.INSTANCE, Utils.zkPath(env, Contants.ZK_TYPE), "/",
                        InetAddressUtil.getIpAddress()),
                JSON.toJSONString(agentDTOS).getBytes(StandardCharsets.UTF_8));
    }

    private void allotAgent() {

        try {
            List<String> children = zookeeperRegistry.getChildren(
                    StringUtils.join(Contants.INSTANCE, Utils.zkPath(env, Contants.ZK_TYPE)));
            int count = 1;
            // 等待所有节点都启动完成 或者最多等待2分钟
            while (children.size() < clusterNumber || count >= 12) {
                TimeUnit.SECONDS.sleep(10);
                count++;
                children = zookeeperRegistry.getChildren(
                        StringUtils.join(Contants.INSTANCE, Utils.zkPath(env, Contants.ZK_TYPE)));
            }

            // query from db directly
            List<AgentDTO> accounts = linkupAccountService.listRobotAgent();

            Map<String, List<AgentDTO>> map = Maps.newHashMap();
            children.stream().forEach(x -> {
                String data = zookeeperRegistry.getData(StringUtils.join(Contants.INSTANCE,
                        Utils.zkPath(env, Contants.ZK_TYPE), "/", x));
                if (StringUtils.isNotBlank(data)) {
                    map.put(x, JSON.parseArray(data, AgentDTO.class));
                } else {
                    map.put(x, Lists.newArrayList());
                }
            });

            // log.info("{},{}", JSON.toJSONString(map.values()), JSON.toJSONString(accounts));

            List<AgentDTO> allotAccounts =
                    map.values().stream().flatMap(List::stream).collect(Collectors.toList());
            List<AgentDTO> noAllotAccount = accounts.stream()
                    .filter(x -> !allotAccounts.contains(x)).collect(Collectors.toList());

            log.info("Not Allot Account : {}", JSON.toJSONString(noAllotAccount));

            if (noAllotAccount.isEmpty()) {
                return;
            }
            if (map.size() != children.size()) {
                log.error("Zookeeper children size not match, map size: {}, children size: {}",
                        map.size(), children.size());
                alarmService.alert(NotifierContext.builder().content(String.format(
                        "zookeeper children size not match, map size: %d, children size: %d",
                        map.size(), children.size())).build());
                return;
            }
            List<AgentDTO> balancedAccounts = 
                    LinkupAccountService.spreadTenantsBalanced(noAllotAccount);


            Map<String, List<AgentDTO>> newMap = new HashMap<>();
            for (Map.Entry<String, List<AgentDTO>> entry : map.entrySet()) {
                // 初始化：每个机器都有自己的 agent 列表（包含历史的）
                newMap.put(entry.getKey(), new ArrayList<>(entry.getValue()));
            }

            // 分配时构造临时结构，记录 “历史已有 + 当前将要分配”的总数
            PriorityQueue<Map.Entry<String, List<AgentDTO>>> serverQueue =
                    new PriorityQueue<>(Comparator.comparingInt(
                            entry -> map.get(entry.getKey()).size() + entry.getValue().size()));

            // 初始化队列
            serverQueue.addAll(newMap.entrySet());

            for (AgentDTO agentDTO : balancedAccounts) {
                Map.Entry<String, List<AgentDTO>> entry = serverQueue.poll(); // 最少总数
                entry.getValue().add(agentDTO);
                serverQueue.offer(entry); // 更新回去
            }

            for (Map.Entry<String, List<AgentDTO>> entry : newMap.entrySet()) {
                agentAllot(entry.getValue(), entry.getKey());
            }
        } catch (Exception exception) {
            log.error("allotAgent Exception ", exception);
            alarmService.alert(NotifierContext
                    .builder().content(String.format(ACCOUNT_ALLOT_ERROR,
                            InetAddressUtil.getIpAddress(), "ALL", exception.getMessage()))
                    .build());
        }
    }

    private void agentAllot(List<AgentDTO> allotList, String client) throws Exception {
        int count = 1;
        while (count <= 2) {
            log.info("Leader 第{} 次分配 client: {},account: {} 开始", count, client,
                    allotList.toString());
            Boolean isSuccess = false;
            try {
                isSuccess = linkupAccountService.allotAccount(allotList, client);
            } catch (Exception e) {
                alarmService
                        .alert(NotifierContext.builder().content(String.format(ACCOUNT_ALLOT_ERROR,
                                client, JSON.toJSONString(allotList), e.getMessage())).build());
            }
            log.info("Leader 分配 client: {},account: {} 结果 :{} ", client, allotList, isSuccess);
            if (isSuccess) {
                setData(client, allotList);
                return;
            } else {
                TimeUnit.SECONDS.sleep(10);
                count++;
            }
        }
    }

    private void setData(String path, List<AgentDTO> agentDTOS) {
        String rootPath = String.format(Contants.ROBOT_PATH, path);

        try {
            String data = agentDTOS.stream().collect(Collectors.groupingBy(AgentDTO::getUserTenant))
                    .entrySet().stream().map(entry -> {
                        String tenant = entry.getKey();
                        List<AgentDTO> agents = entry.getValue();
                        String sipAccounts = agents.stream().map(AgentDTO::getSipAccount)
                                .collect(Collectors.joining(", "));
                        return tenant + "(" + sipAccounts + ") = " + agents.size();
                    }).collect(Collectors.joining(", "));

            if (zookeeperRegistry.checkExists(path)) {
                zookeeperRegistry.setData(rootPath, data.getBytes(StandardCharsets.UTF_8));
            } else {
                zookeeperRegistry.createEphemeralData(rootPath, data);
            }
        } catch (Exception e) {
            log.error("path: {},setData : {} Exception ", rootPath, JSON.toJSONString(agentDTOS),
                    e);
        }
    }



    public List<String> getzkInstanceChildren() {
        return zookeeperRegistry.getChildren("/wacai/counselor/robotConfig");
    }

}
