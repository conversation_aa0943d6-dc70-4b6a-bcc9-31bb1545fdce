package com.wacai.loan.counselor.opt.speech.asr;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import com.wacai.loan.counselor.opt.speech.asr.impl.AliyunRealtimeAsrService;
import com.wacai.loan.counselor.opt.speech.config.AliyunAsrConfig;
import com.wacai.loan.counselor.opt.speech.config.AliyunAsrProperties;
import lombok.extern.slf4j.Slf4j;

/**
 * 阿里云实时语音识别服务管理器
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
public class AliyunRealtimeAsrServiceManager implements InitializingBean, DisposableBean {

    @Resource
    private AliyunAsrProperties properties;

    @Resource
    private Environment environment;

    @Resource
    private ApplicationContext applicationContext;

    @Resource(name = "asrExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    // 服务实例缓存
    private final Map<String, AliyunRealtimeAsrService> serviceCache = new ConcurrentHashMap<>();

    // NLS客户端适配器缓存
    private final Map<String, AliyunNlsClientAdapter> clientCache = new ConcurrentHashMap<>();

    // 业务类型与账号的映射关系缓存
    private final Map<String, String> businessTypeAccountMap = new ConcurrentHashMap<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化默认服务实例
        if (properties.getDefaultConfig().getActive() == 1) {
            createService("default", properties.getDefaultConfig());
        }

        // 初始化激活的账号的服务实例
        for (Map.Entry<String, AliyunAsrConfig> entry : properties.getActiveAccounts().entrySet()) {
            createService(entry.getKey(), entry.getValue());
        }
    }

    @Override
    public void destroy() throws Exception {
        // 关闭所有NLS客户端
        for (AliyunNlsClientAdapter client : clientCache.values()) {
            client.close();
        }
        clientCache.clear();
        serviceCache.clear();
    }

    /**
     * 获取默认服务实例
     *
     * @return 服务实例
     */
    public AliyunRealtimeAsrService getDefaultService() {
        return getService("default");
    }

    /**
     * 获取指定账号的服务实例
     *
     * @param accountName 账号名称
     * @return 服务实例
     */
    public AliyunRealtimeAsrService getService(String accountName) {
        if (StringUtils.isBlank(accountName)) {
            accountName = "default";
        }

        AliyunRealtimeAsrService service = serviceCache.get(accountName);
        if (service == null) {
            log.warn("Service not found for account [{}], use default", accountName);
            service = serviceCache.get("default");
        }

        return service;
    }

    /**
     * 根据业务类型获取服务实例
     *
     * @param businessType 业务类型
     * @return 服务实例
     */
    public AliyunRealtimeAsrService getServiceByBusinessType(String businessType) {
        if (StringUtils.isBlank(businessType)) {
            return getDefaultService();
        }

        // 从缓存中获取
        String accountName = businessTypeAccountMap.get(businessType);
        if (accountName != null) {
            return getService(accountName);
        }

        // 从配置中获取
        String propertyKey = "speech.aliyun.account-selector.business-type." + businessType;
        accountName = environment.getProperty(propertyKey);

        // 如果配置中没有，使用默认账号
        if (StringUtils.isBlank(accountName)) {
            accountName = environment.getProperty("speech.aliyun.account-selector.business-type.default", "default");
        }

        // 缓存结果
        businessTypeAccountMap.put(businessType, accountName);

        log.debug("Selected account [{}] for business type [{}]", accountName, businessType);
        return getService(accountName);
    }

    /**
     * 根据场景类型获取服务实例
     *
     * @param sceneType 场景类型
     * @return 服务实例
     */
    public AliyunRealtimeAsrService getServiceBySceneType(String sceneType) {
        if (StringUtils.isBlank(sceneType)) {
            return getDefaultService();
        }

        // 从配置中获取
        String propertyKey = "speech.aliyun.account-selector.scene-type." + sceneType;
        String accountName = environment.getProperty(propertyKey);

        // 如果配置中没有，使用默认账号
        if (StringUtils.isBlank(accountName)) {
            accountName = environment.getProperty("speech.aliyun.account-selector.scene-type.default", "default");
        }

        log.debug("Selected account [{}] for scene type [{}]", accountName, sceneType);
        return getService(accountName);
    }

    /**
     * 根据自定义参数获取服务实例
     *
     * @param paramName 参数名
     * @param paramValue 参数值
     * @return 服务实例
     */
    public AliyunRealtimeAsrService getServiceByParam(String paramName, String paramValue) {
        if (StringUtils.isBlank(paramName) || StringUtils.isBlank(paramValue)) {
            return getDefaultService();
        }

        // 从配置中获取
        String propertyKey = "speech.aliyun.account-selector." + paramName + "." + paramValue;
        String accountName = environment.getProperty(propertyKey);

        // 如果配置中没有，使用默认账号
        if (StringUtils.isBlank(accountName)) {
            accountName = environment.getProperty("speech.aliyun.account-selector." + paramName + ".default", "default");
        }

        log.debug("Selected account [{}] for param [{}={}]", accountName, paramName, paramValue);
        return getService(accountName);
    }

    /**
     * 创建服务实例
     *
     * @param accountName 账号名称
     * @param config 配置
     */
    private void createService(String accountName, AliyunAsrConfig config) {
        try {
            // 创建NLS客户端适配器
            AliyunNlsClientAdapter client = new AliyunNlsClientAdapter(accountName, config);
            clientCache.put(accountName, client);

            // 创建服务实例
            AliyunRealtimeAsrService service = new AliyunRealtimeAsrService(
                    accountName, config, client, threadPoolTaskExecutor);
            serviceCache.put(accountName, service);

            log.info("Created AliyunRealtimeAsrService for account [{}]", accountName);
        } catch (Exception e) {
            log.error("Create AliyunRealtimeAsrService failed for account [{}]", accountName, e);
        }
    }
}
