/*
 * @Author: shunhua
 * 
 * @Date: 2025-04-30 11:33:41
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-07 11:41:35
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.base.util;

import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;

public class SafeUrlDecoder {


    private static final Pattern URL_ENCODED_PATTERN = Pattern.compile(".*%[0-9a-fA-F]{2}.*");

    public static boolean isUrlEncoded(String input) {
        if (StringUtils.isEmpty(input)) {
            return false;
        }
        return URL_ENCODED_PATTERN.matcher(input).find() || input.contains("+");
    }
}
