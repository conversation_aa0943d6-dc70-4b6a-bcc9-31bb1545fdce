/*
 * @Author: shunhua
 * 
 * @Date: 2024-01-05 18:03:41
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-13 11:16:34
 * 
 * @Description:
 */
package com.wacai.loan.counselor.utils;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Joiner;

/**
 * 
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-20 19:33
 */
public class Utils {
    public static String asUrlParams(Map<String, String> source) {
        return Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(source);
    }

    public static String zkPath(String env, String type) {
        return StringUtils.isEmpty(env) ? type : env + "/" + type;
    }


    public static Map<String, Object> flattenJson(JSONObject jsonObject) {
        Map<String, Object> flatMap = new HashMap<>();
        flatten(jsonObject, "", flatMap);
        return flatMap;
    }

    private static void flatten(Object obj, String prefix, Map<String, Object> flatMap) {
        if (obj instanceof JSONObject) {
            JSONObject jsonObject = (JSONObject) obj;
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                String newKey = prefix.isEmpty() ? key : prefix + "." + key;
                flatten(value, newKey, flatMap);
            }
        } else if (obj instanceof String) {
            try {
                JSONObject nestedJson = JSONObject.parseObject((String) obj);
                flatten(nestedJson, prefix, flatMap);
            } catch (Exception e) {
                // 如果不是 JSON 字符串，直接使用最后的键名
                String finalKey = getFinalKey(prefix);
                // 只有在键不存在时才放入，忽略重复键
                if (!flatMap.containsKey(finalKey)) {
                    flatMap.put(finalKey, obj);
                }
            }
        } else {
            // 对于非嵌套值，使用最后的键名
            String finalKey = getFinalKey(prefix);
            // 只有在键不存在时才放入，忽略重复键
            if (!flatMap.containsKey(finalKey)) {
                flatMap.put(finalKey, obj);
            }
        }
    }

    private static String getFinalKey(String fullKey) {
        String[] parts = fullKey.split("\\.");
        return parts[parts.length - 1];
    }


    /**
     * 过滤字符串中的非字母和数字字符
     *
     * @param input 输入字符串
     * @return 过滤后的字符串
     */
    public static String filterAlphanumeric(String input) {
        // 先处理 Unicode 转义字符
        String decodedString = decodeUnicode(input);
        // 再去除非字母和数字的字符
        return decodedString.replaceAll("[^0-9]", "");
    }

    public static String decodeUnicode(String input) {
        StringBuilder sb = new StringBuilder();
        int i = 0;
        while (i < input.length()) {
            if (i + 5 < input.length() && input.charAt(i) == '\\' && input.charAt(i + 1) == 'u') {
                // 找到 \\uXXXX 模式
                try {
                    String unicode = input.substring(i + 2, i + 6); // 提取 003e
                    char decodedChar = (char) Integer.parseInt(unicode, 16); // 转为字符
                    sb.append(decodedChar);
                    i += 6; // 跳过 \\uXXXX
                } catch (NumberFormatException e) {
                    sb.append(input.charAt(i)); // 解析失败，保留原字符
                    i++;
                }
            } else {
                sb.append(input.charAt(i));
                i++;
            }
        }
        return sb.toString();
    }

    public static String packageUrl(String ip) {
        return "http://" + ip + ":8080";
    }

    public static boolean waitForFileExists(File file, int maxRetries, long intervalMs) {
        for (int i = 0; i < maxRetries; i++) {
            if (file.exists()) {
                return true;
            }
            try {
                TimeUnit.MILLISECONDS.sleep(intervalMs);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        return false;
    }
}
