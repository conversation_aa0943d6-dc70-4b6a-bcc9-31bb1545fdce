/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-23 16:54:44
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-23 16:42:06
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.model;

import java.util.Map;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class ScriptContextDTO {


    private String uuid;

    private String mobile;

    private String input;

    private Map<String, Object> callContext;


    public ScriptContextDTO(String uuid, String text, String mobile) {
        this.uuid = uuid;
        this.input = text;
        this.mobile = mobile;
        this.callContext = CallStateManager.getState(uuid).getCallContext();
    }
}
