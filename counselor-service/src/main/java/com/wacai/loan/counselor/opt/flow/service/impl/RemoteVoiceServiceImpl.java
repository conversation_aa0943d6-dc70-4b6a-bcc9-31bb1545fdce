/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-12 17:23:47
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-25 17:05:14
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.service.impl;

import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.wacai.loan.counselor.opt.base.alarm.AlarmService;
import com.wacai.loan.counselor.opt.base.threadpool.ExecutorServiceHandlerRegistry;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowRemoteVoiceConfig;
import com.wacai.loan.counselor.opt.flow.service.RemoteVoiceService;
import com.wacai.loan.counselor.utils.FFmpegUtils;
import com.wacai.loan.counselor.utils.WebClientUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.util.retry.Retry;

@Service
@Slf4j
public class RemoteVoiceServiceImpl implements RemoteVoiceService {

    @Value("${ai-flow-node-voice-cache.cache-path:/etc/tmp/ai_flow_node_voice_cache}")
    private String cachePath;

    @Value("${linkup.tts.url}")
    private String ttsUrl;
    
    @Resource
    private AlarmService alarmService;

  private final static ExecutorService executorService = ExecutorServiceHandlerRegistry.getExecutorServiceHandler(20,"remote-voice-task");
 

    private static final Map<String, CompletableFuture<File>> downloadingTasks =
            Maps.newConcurrentMap();

   private   static Cache<String, File> cache =
            CacheBuilder.newBuilder().initialCapacity(50).maximumSize(15000) // 覆盖一天 10,000 个文件
                    .expireAfterAccess(24, TimeUnit.HOURS).build();

   private  static Map<String, File> map = Maps.newConcurrentMap();

    @PostConstruct
    public void init() {
        try {
            // 只在路径不存在时才创建它
            Files.createDirectories(Paths.get(cachePath));
        } catch (IOException e) {
            log.error("Files.createDirectories err {}", cachePath, e);
        }
    }


    private WebClient.RequestHeadersSpec<?> buildRequestHeadersSpec(
            WorkFlowRemoteVoiceConfig aiFlowNodeVoiceCacheParam) {
        switch (aiFlowNodeVoiceCacheParam.getMethod()) {
            case GET:
                // 构建带有查询参数的 URI
                String url = UriComponentsBuilder.fromHttpUrl(aiFlowNodeVoiceCacheParam.getUrl())
                        .query(aiFlowNodeVoiceCacheParam.getParam()).build().toUriString();

                // 使用 GET 方法发送请求
                final WebClient.RequestHeadersSpec<?> request =
                        WebClientUtils.getWebClient().get().uri(URI.create(url));

                return request;

            case POST:
                final WebClient.RequestHeadersSpec<?> accept = WebClientUtils.getWebClient().post()
                        .uri(URI.create(aiFlowNodeVoiceCacheParam.getUrl()))
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(JSON.toJSONString(aiFlowNodeVoiceCacheParam.getParsedParam()))
                        .accept(MediaType.APPLICATION_OCTET_STREAM);
                return accept;
            default:
                log.error("not support other method");
                return null;
        }
    }

    /**
     * transferVoiceEncode and cache
     *
     * @param hash
     * @param pathT
     *
     * @return
     */
    @SneakyThrows
    private File transferVoiceEncodeAndCache(Path pathT, String targetName) {
        final File file = FFmpegUtils.transferVoiceEncode(pathT.toString(), targetName);
        if (file == null) {
            log.error("transferVoiceEncode err {}", pathT);
            return null;
        }
        Files.deleteIfExists(pathT);
        return file;
    }


    private File downloadVoiceFile(String callId, String flowCode,
            WorkFlowRemoteVoiceConfig aiFlowNodeVoiceCacheParam, String hash, boolean isDelete) {

        String targetName = FFmpegUtils.getTargetName(hash);
        File targetFile = new File(targetName);
        try {
            if (targetFile.exists()) {
                if (targetFile.length() == 0) {
                    log.warn("callId: {} Empty file detected for hash: {}, path: {}, deleting file",
                            callId, hash, targetFile.getAbsolutePath());
                    targetFile.delete();
                } else {
                    log.info("callId: {} flowCode,{} hash: {} ,downloadVoice Cache {}", callId,
                            flowCode, hash, targetFile.getAbsolutePath());
                    cache.put(hash, targetFile);
                    return targetFile;
                }
            }
        } catch (Exception e) {
            log.error("callId {},hash : {} get cache err {}", callId, hash, e.getMessage());
        }
        try {
            final WebClient.RequestHeadersSpec<?> requestHeadersSpec =
                    buildRequestHeadersSpec(aiFlowNodeVoiceCacheParam);
            if (requestHeadersSpec == null) {
                log.warn("callId: {} flowCode,{} hash:{},Invalid request headers spec", callId,
                        flowCode, hash);
                return null;
            }

            final String fileT = cachePath + "/" + hash + "_t.wav";
            final Path pathT = Paths.get(fileT);

            FFmpegUtils.delMayExistFile(pathT);

            final Flux<?> dataBufferFlux;

            if (aiFlowNodeVoiceCacheParam.getMethod() == RequestMethod.POST) {
                dataBufferFlux = requestHeadersSpec.retrieve().bodyToFlux(DataBuffer.class)
                        .doOnNext(dataBuffer -> FFmpegUtils.writeToFile(pathT, dataBuffer));
            } else {
                dataBufferFlux = requestHeadersSpec.retrieve().bodyToFlux(byte[].class)
                        .doOnNext(dataBuffer -> FFmpegUtils.writeToFile(pathT, dataBuffer));
            }
            final String[] errorMessage = new String[] {""};
            dataBufferFlux.then().doOnError(error -> {
                log.error("callId: {} flowCode:{},hash: {} block file download err {}", callId,
                        flowCode, hash, error);
                errorMessage[0] = error.getMessage();
            }).retryWhen(Retry.backoff(2, Duration.ofSeconds(1)) // 最多重试3次，初始间隔1秒
                    .filter(throwable -> throwable instanceof Exception) // 只重试特定异常
                    .doBeforeRetry(retrySignal -> log.info(
                            "callId: {} Retrying download for hash {}, attempt {}", callId, hash,
                            retrySignal.totalRetries() + 1)))
                    .block(Duration.ofSeconds(60)); // 总超时60秒，包括重试

            if (!pathT.toFile().exists()) {
                log.error(
                        "callId: {} FlowCode: {},hash : {} downloadVoice failed {} not exists after retries",
                        callId, flowCode, hash, fileT);
                alarmService.alert(String.format(
                        "话术TTS失败: \n FlowCode:%s \n File hash :%s \n Message: download err: %s",
                        flowCode, hash, errorMessage[0]));
                return null;
            }

            final File file1 = transferVoiceEncodeAndCache(pathT, targetName);
            if (!file1.exists()) {
                log.error("callId: {} FlowCode: {},hash : {},File : {} transfer fail", callId,
                        flowCode, hash, file1.getAbsolutePath());
                alarmService.alert(String.format("话术TTS失败: \n" + //
                        " FlowCode:%s \n" + //
                        " File hash :%s \n" + //
                        " Message: FFmpeg Fail ", flowCode, hash));
                return null;
            }

            log.info("callId: {} , FlowCode: {},Url : {}, hash : {},File :{} download succ......", callId,
                    flowCode,aiFlowNodeVoiceCacheParam.getUrl(), hash, file1.getAbsolutePath());
            if (!isDelete) {
                cache.put(hash, file1);
            } else {
                map.put(hash, file1);
            }

            return file1;

        } catch (Exception e) {
            log.error("callId: {} FlowCode : {},hash: {} acquireVoice err", callId, flowCode, hash,
                    e);
            alarmService.alert(String.format("话术TTS失败: \n" + //
                    " FlowCode:%s \n" + //
                    " File hash :%s \n" + //
                    " Message:生成话术超时 ", flowCode, hash));
            return null;
        }

    }

    @Override
    public File acquireVoice(String callId, String flowCode,
            WorkFlowRemoteVoiceConfig aiFlowNodeVoiceCacheParam, boolean isDelete) {

        final String hash = aiFlowNodeVoiceCacheParam.hash();

        final File cachedFile = cache.getIfPresent(hash);

        if (Objects.nonNull(cachedFile)) {
            if (!cachedFile.exists()) {
                cache.invalidate(hash);
            } else {
                return cachedFile;
            }
        }

        CompletableFuture<File> future = downloadingTasks.compute(hash, (key, existingFuture) -> {
            if (Objects.nonNull(existingFuture)) {
                log.info("callId: {} Reusing existing future for hash: {}, isDone: {}", callId,
                        hash, existingFuture.isDone());
                if (existingFuture.isDone()) {
                    log.warn("callId: {} Existing future is done, creating new one for hash: {}",
                            callId, hash);
                } else {
                    return existingFuture;
                }
            }
            // log.info("callId: {} Creating new download task for hash: {}", callId, hash);
            CompletableFuture<File> newFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    // log.info("callId: {} Starting downloadVoiceFile for hash: {}", callId, hash);
                    File downloaded = downloadVoiceFile(callId, flowCode, aiFlowNodeVoiceCacheParam,
                            hash, isDelete);
                    if (downloaded != null && downloaded.exists() && downloaded.isFile()) {
                        return downloaded;
                    }
                    throw new RuntimeException("下载失败或文件不存在");
                } finally {
                    // log.info("callId: {} Removing hash from downloadingTasks: {}", callId, hash);
                    downloadingTasks.remove(hash); // 下载完成或失败后清理
                }
            }, executorService);
            return newFuture;
        });
        try {
            return future.get(30, TimeUnit.SECONDS); // 等待下载结果，超时60秒
        } catch (TimeoutException e) {
            log.error("callId: {} acquireVoice timed out for hash: {}, param: {}", callId, hash,
                    aiFlowNodeVoiceCacheParam, e);
            downloadingTasks.remove(hash);
            return null;
        } catch (Exception e) {
            log.error("callId: {} acquireVoice failed for hash: {}, param: {}", callId, hash,
                    aiFlowNodeVoiceCacheParam, e);
            downloadingTasks.remove(hash);
            return null;
        }
    }

    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    public void cleanCache() {
        log.info("开始清理文件缓存，总数：{}", map.size());

        Iterator<Map.Entry<String, File>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, File> entry = iterator.next();
            try {
                File file = entry.getValue();
                if (file != null && file.exists() && file.isFile()) {
                    Files.deleteIfExists(file.toPath());
                    iterator.remove(); // 删除成功后从 map 中移除
                    log.info("删除成功: {}", file.getAbsolutePath());
                } else {
                    iterator.remove(); // 不存在或不是文件，也清除 map 记录
                    log.warn("文件不存在或不是普通文件，已移除映射: {}", file);
                }
            } catch (Exception e) {
                log.error("删除文件异常, hash: {}, file: {}", entry.getKey(),
                        Objects.nonNull(entry.getValue()) ? entry.getValue().getAbsolutePath()
                                : null,
                        e);
            }
        }
        log.info("文件清理任务完成，剩余: {}", map.size());
    }


    @Override
    public File generalTTSFile(String callId, String text) {
        try {
            JSONObject jsonObject =
                    new JSONObject(ImmutableMap.of("text", text));
            JSONObject param = new JSONObject(
                    ImmutableMap.of("jsonData", JSON.toJSONString(jsonObject), "uuid", callId));
            WebClient.RequestHeadersSpec<?> requestHeadersSpec = WebClientUtils.getWebClient()
                    .post().uri(URI.create(ttsUrl)).contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(JSON.toJSONString(param)).accept(MediaType.APPLICATION_OCTET_STREAM);
            String targetName = FFmpegUtils.getTargetName(callId);
            final String fileT = cachePath + "/" + callId + "_t.wav";
            final Path pathT = Paths.get(fileT);

            FFmpegUtils.delMayExistFile(pathT);

            requestHeadersSpec.retrieve().bodyToFlux(DataBuffer.class)
                    .doOnNext(dataBuffer -> FFmpegUtils.writeToFile(pathT, dataBuffer)).then().block(Duration.ofSeconds(5));
            final File file1 = transferVoiceEncodeAndCache(pathT, targetName);
            if (!file1.exists()) {
                return null;
            }
            log.info("callId: {} , text : {},File :{}  download TTSsucc......", callId, text,
                    file1.getAbsolutePath());
            return file1;
        } catch (Exception e) {
            log.error("callId: {} , text : {},File download err: {}", callId, text, e.getMessage());
            return null;
        }
    }
}
