package com.wacai.loan.counselor.opt.base.state;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import lombok.extern.slf4j.Slf4j;

/**
 * 呼叫状态管理器 负责管理所有呼叫的状态，提供线程安全的状态访问和修改方法
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class CallStateManager {

    /**
     * 状态缓存，key为callId，value为状态对象
     */
    private final static Map<String, CallState> stateMap = new ConcurrentHashMap<>();


    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(20);

    public static ScheduledExecutorService getScheduler() {
        return scheduler;
    }

    /**
     * 获取呼叫状态，如果不存在则创建
     *
     * @param callId 呼叫ID
     * @return 呼叫状态
     */
    public static CallState getState(String callId) {
        if (callId == null || callId.isEmpty()) {
            throw new IllegalArgumentException("CallId cannot be null or empty");
        }
        CallState state = stateMap.computeIfAbsent(callId, k -> new CallState());
        return state;
    }

    public static CallState initCallState(String callId) {
        CallState state = stateMap.computeIfAbsent(callId, k -> new CallState());
        return state;
    }

    /**
     * 移除呼叫状态
     *
     * @param callId 呼叫ID
     * @return 被移除的状态，如果不存在则返回null
     */
    public static CallState removeState(String callId) {
        if (callId == null || callId.isEmpty()) {
            return null;
        }

        CallState removed = stateMap.remove(callId);
        if (removed != null) {
            removed.shutdown(); // 释放线程池等资源
            removed.clearWaitHandle();
            removed.getRunning().set(false);
        }
        return removed;
    }

    /**
     * 检查呼叫状态是否存在
     *
     * @param callId 呼叫ID
     * @return 是否存在
     */
    public static boolean hasState(String callId) {
        CallState callState = stateMap.get(callId);
        return Objects.nonNull(callState) && callState.isRunning();
    }



    /**
     * 检查呼叫是否正在运行
     *
     * @param callId 呼叫ID
     * @return 是否正在运行
     */
    public static boolean isRunning(String callId) {
        CallState state = stateMap.get(callId);
        return state != null && state.isRunning();
    }


    /**
     * 检查是否停止接收语音标志
     *
     * @param callId 呼叫ID
     * @return 是否停止接收语音
     */
    public static boolean isStopReceiveVoiceFlag(String callId) {
        CallState state = stateMap.get(callId);
        return state != null && state.isStopReceiveVoiceFlag()&& state.isRunning();
    }

    /**
     * 设置停止接收语音标志
     *
     * @param callId 呼叫ID
     * @param flag 标志值
     */
    public static void setStopReceiveVoiceFlag(String callId, boolean flag) {
        getState(callId).setStopReceiveVoiceFlag(flag);
        log.debug("Set stopReceiveVoiceFlag [{}] for call [{}]", flag, callId);
    }

    /**
     * 检查是否忽略停止接收语音标志
     *
     * @param callId 呼叫ID
     * @return 是否忽略停止接收语音
     */
    public static boolean isIgnoreStopReceiveVoiceFlag(String callId) {
        CallState state = stateMap.get(callId);
        return state != null && state.isIgnoreStopReceiveVoiceFlag();
    }

    /**
     * 设置忽略停止接收语音标志
     *
     * @param callId 呼叫ID
     * @param flag 标志值
     */
    public static void setIgnoreStopReceiveVoiceFlag(String callId, boolean flag) {
        getState(callId).setIgnoreStopReceiveVoiceFlag(flag);
        log.debug("Set ignoreStopReceiveVoiceFlag [{}] for call [{}]", flag, callId);
    }

     


    public static void put(String callId, String key, Object object) {
        getState(callId).put(key, object);
    }


    public static void putAll(String callId, Map<String, Object> map) {
        if (map != null && !map.isEmpty()) {
            getState(callId).putAll(map);
        }
    }


    public static <T> T get(String callId, String key, Class<T> clazz) {
        Object value = getState(callId).get(key);
        if (clazz.isInstance(value)) {
            return clazz.cast(value);
        } else {
            return null;
        }
    }

    public static String getMobile(String callId) {
        return get(callId, "mobile", String.class);
    }
}
