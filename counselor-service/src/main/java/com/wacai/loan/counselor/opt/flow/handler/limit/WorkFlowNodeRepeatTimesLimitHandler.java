/*
 * @Author: shunhua
 * @Date: 2025-05-26 10:20:27
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-24 16:27:58
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.handler.limit;

import java.util.Map;
import java.util.Objects;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowRepeatTimesLimitConfig;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class WorkFlowNodeRepeatTimesLimitHandler {

    
    @SuppressWarnings("unchecked")
    public boolean executeLimit(String callId,WorkFlowRepeatTimesLimitConfig workFlowRepeatTimesLimitConfig, String workNodeCode) {
       
        if(workFlowRepeatTimesLimitConfig.getLimitTimes() == 0){
            return true;
        }

        Map<String,Integer> repeatTimesMap =  CallStateManager.getState(callId).get("repeatTimes", Map.class);
        if(Objects.isNull(repeatTimesMap)){
            repeatTimesMap =Maps.newHashMap();
        }
        Integer current = repeatTimesMap.getOrDefault(workNodeCode, 0);
        if (current < workFlowRepeatTimesLimitConfig.getLimitTimes()) {
            repeatTimesMap.put(workNodeCode, ++current);
            CallStateManager.getState(callId).put("repeatTimes", repeatTimesMap);
            return true;
        }
        return false;
    }
}
