/*
 * @Author: shunhua
 * @Date: 2025-05-23 16:47:00
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-08 14:46:12
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.config;

import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import lombok.Data;

@Data
public class WorkFlowRepeatTimesLimitConfig {

    private int limitTimes;

    public static WorkFlowRepeatTimesLimitConfig builderRepeatTimesLimitConfig(String context) {
        if (StringUtils.isEmpty(context)) {
            return null;
        }
        Map<String, Object> callContext =
                JSON.parseObject(context, new TypeReference<Map<String, Object>>() {});
        Object aiFlowNodeRepeatTimesLimit = callContext.get("_aiFlowNodeRepeatTimesLimit");
        if (Objects.nonNull(aiFlowNodeRepeatTimesLimit)
                && aiFlowNodeRepeatTimesLimit instanceof JSONObject) {
            return ((JSONObject) aiFlowNodeRepeatTimesLimit)
                    .to(WorkFlowRepeatTimesLimitConfig.class);
        }
        return null;
    }

}
