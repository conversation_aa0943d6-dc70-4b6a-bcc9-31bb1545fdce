/*
 * @Author: shunhua
 * @Date: 2025-05-04 14:57:05
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-28 15:37:02
 * @Description: 
 */
package com.wacai.loan.counselor.opt.speech.asr;

import com.alibaba.nls.client.protocol.NlsClient;
import com.wacai.loan.counselor.opt.speech.config.AliyunAsrConfig;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 阿里云NLS客户端适配器
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class AliyunNlsClientAdapter {

    
    private final String accountName;

    private final AliyunAsrConfig config;

    @Getter
    private NlsClient nlsClient;

    private TokenService accessToken;

    /**
     * 构造函数
     *
     * @param accountName 账号名称
     * @param config 配置
     */
    public AliyunNlsClientAdapter(String accountName, AliyunAsrConfig config) {
        this.accountName = accountName;
        this.config = config;
        initClient();
    }

    /**
     * 初始化客户端
     */
    private void initClient() {
        try {
            // 创建AccessToken
            accessToken = new TokenService(config.getAccessKey(), config.getAccessSecret());

            log.info("Get Aliyun access token success for account [{}]: {}", accountName, accessToken.getToken());

            // 创建NlsClient
             nlsClient = new AliyunNlsClient(accessToken);
        } catch (Exception e) {
            log.error("Init Aliyun NLS client failed for account [{}]", accountName, e);
            throw new RuntimeException("Init Aliyun NLS client failed", e);
        }
    }

    /**
     * 关闭客户端
     */
    public void close() {
        if (nlsClient != null) {
            try {
                // NlsClient 没有 close 方法，但它内部可能持有资源
                // 这里将引用置为 null，让 GC 回收资源
                nlsClient = null;
                log.info("Release Aliyun NLS client for account [{}]", accountName);
            } catch (Exception e) {
                log.error("Release Aliyun NLS client failed for account [{}]", accountName, e);
            }
        }
    }
}
