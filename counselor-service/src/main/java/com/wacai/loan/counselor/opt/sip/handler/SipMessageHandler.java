/*
 * @Author: shun<PERSON>
 * @Date: 2025-05-04 14:56:45
 * @LastEditors: shunhua
 * @LastEditTime: 2025-05-04 15:01:26
 * @Description: 
 */
package com.wacai.loan.counselor.opt.sip.handler;

/**
 * SIP 消息处理器接口
 * 
 * 用于解析 SIP 消息
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface SipMessageHandler {
    
    /**
     * 解析 SIP 消息
     * 
     * @param sipMessage SIP 消息
     * @return SIP 消息信息
     */
    SipMessageInfo parseSipMessage(int sipCallId,String sipMessage);
}
