/*
 * @Author: shunhua
 * @Date: 2025-05-03 14:23:50
 * @LastEditors: shunhua
 * @LastEditTime: 2025-05-04 15:12:33
 * @Description: 
 */


package com.wacai.loan.counselor.opt.base.threadpool;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import com.wacai.loan.counselor.opt.base.threadpool.impl.DefaultExecutorServiceHandler;
import lombok.extern.slf4j.Slf4j;

/**
 * 线程池服务处理器注册表.
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2019-03-21 18:02
 */
@Slf4j
public class ExecutorServiceHandlerRegistry {

    private static final Map<String, ExecutorService> REGISTRY = new ConcurrentHashMap<>();

    /**
     * 获取线程池服务.
     *
     * @param type                   线程池类型
     * @param executorServiceHandler 线程池服务处理器
     * @return 线程池服务
     */
    public static ExecutorService getExecutorServiceHandler(final String type,
                                                            final ExecutorServiceHandler executorServiceHandler) {
        return REGISTRY.computeIfAbsent(type, x -> executorServiceHandler.createExecutorService(x));
    }

    public static ExecutorService getExecutorServiceHandler(final String type) {
        return REGISTRY.computeIfAbsent(type, x -> new DefaultExecutorServiceHandler().createExecutorService(x));
    }

    public static ExecutorService getExecutorServiceHandler(int coreNum,final String type) {
        return REGISTRY.computeIfAbsent(type,
                x -> new DefaultExecutorServiceHandler().createExecutorService(coreNum,x));
    }

    /**
     * 从注册表中删除该作业线程池服务.
     *
     * @param jobName 作业名称
     */
    public static void remove(final String jobName) {
        REGISTRY.remove(jobName);
    }

    /**
     * 优雅关闭线程池
     */
    public static void shuntDown() {
        for (Map.Entry<String, ExecutorService> entry : REGISTRY.entrySet()) {
            ExecutorService executorService = entry.getValue();
            executorService.shutdown();
            try {
                if (executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                    log.warn(
                            "Interrupt the worker, which may cause some task inconsistent. Please check the biz logs.");
                    // 等待任务取消的响应
                    if (executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                        log.error(
                                "Thread pool can't be shutdown even with interrupting worker threads, which may cause" +
                                        " some task inconsistent. Please check the biz logs.");
                    }
                }
            } catch (InterruptedException e) {
                executorService.shutdown();
                log.error(
                        "The current server thread is interrupted when it is trying to stop the worker threads. This " +
                                "may leave an inconcistent state. Please check the biz logs.");
                // 保留中断状态
                Thread.currentThread().interrupt();
            }
            log.info("Finally shutdown the thread pool:{}", entry.getKey());
        }
    }
}
