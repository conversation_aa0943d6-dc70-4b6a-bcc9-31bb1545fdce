/*
 * @Author: shunhua
 * @Date: 2025-05-01 10:55:14
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-22 11:08:26
 * @Description: 
 */

package com.wacai.loan.counselor.opt.base.dto;
import com.wacai.loan.counselor.opt.base.constant.EventCallStatusEnums;
import lombok.Data;

@Data
public class DialEventDTO {


    private String action;

    private EventCallStatusEnums.DailRecordStatusEnums callStatus;

    private EventCallStatusEnums.EventStatusEnums event;

    private EventCallStatusEnums.EventStatusEnums eventName;

    private String userData;

    private String uuid;
    
    private String callType;

    private String comment;

    private String dialAgent;

    private String targetNum;

    private String campCode;

    private String showNum;

    public Boolean isFilter() {
        return "AgentReadyHiddenPlain".equalsIgnoreCase(action);
    }

    public void rehandleProps() {
        // this condition we need suit sip event
        if (event == null) {
            event = eventName;
        }
    }
}
