package com.wacai.loan.counselor.opt.ai.model;
import java.util.List;
import lombok.Data;

/**
 * 
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-08-01 18:57
 */


@Data
public class OpenAiRequest {

    private float temperature;

    private List<Message> messages;
 

    public OpenAiRequest( List<Message> messages) {
        this.temperature = 0.2f;
        this.messages = messages;
    }

    @Data
   public  static class Message {

        private String role;

        private String content;

        public Message(String role, String content) {
            this.role = role;
            this.content = content;
        }

        public Message(String content) {
            this.role = "user";
            this.content = content;
        }
    }

}
