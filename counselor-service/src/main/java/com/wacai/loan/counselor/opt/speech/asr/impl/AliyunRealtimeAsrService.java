package com.wacai.loan.counselor.opt.speech.asr.impl;

import java.io.File;
import java.io.FileInputStream;
import java.util.concurrent.TimeUnit;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import com.alibaba.nls.client.protocol.Constant;
import com.alibaba.nls.client.protocol.InputFormatEnum;
import com.alibaba.nls.client.protocol.SpeechReqProtocol;
import com.alibaba.nls.client.protocol.asr.SpeechTranscriber;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.speech.asr.AliyunNlsClientAdapter;
import com.wacai.loan.counselor.opt.speech.asr.AsrService;
import com.wacai.loan.counselor.opt.speech.asr.listener.AsrResultListener;
import com.wacai.loan.counselor.opt.speech.config.AliyunAsrConfig;
import com.wacai.loan.counselor.opt.speech.model.AsrModel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 阿里云实时语音识别服务
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class AliyunRealtimeAsrService implements AsrService {

    @Getter
    private final String accountName;

    @Getter
    private final AliyunAsrConfig config;

    @Getter
    private final AliyunNlsClientAdapter nlsClient;


    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * 构造函数
     *
     * @param accountName 账号名称
     * @param config 配置
     * @param nlsClient NLS客户端适配器
     * @param taskExecutor 线程池
     */
    public AliyunRealtimeAsrService(String accountName, AliyunAsrConfig config,
            AliyunNlsClientAdapter nlsClient, ThreadPoolTaskExecutor threadPoolTaskExecutor) {
        this.accountName = accountName;
        this.config = config;
        this.nlsClient = nlsClient;
        this.threadPoolTaskExecutor = threadPoolTaskExecutor;
    }

    /**
     * 开始语音识别
     *
     * @param callId 呼叫ID
     * @param recording 录音文件
     * @return 语音识别模型
     */
    @Override
    public AsrModel start(String callId, File recording) {
        // Step1 创建实例,建立连接
        final SpeechTranscriber transcriber;
        final AsrResultListener resultListener = new AsrResultListener(callId);

        try {
            transcriber = new SpeechTranscriber(nlsClient.getNlsClient(), resultListener);
        } catch (final Exception e) {
            log.error("Create speech transcriber [{}] error caused.", callId, e);
            throw new RuntimeException(e);
        }
        transcriber.setAppKey(config.getAppKey());
        // 输入音频编码方式
        transcriber.setFormat(InputFormatEnum.PCM);
        // 输入音频采样率
        transcriber.payload.put(Constant.PROP_ASR_SAMPLE_RATE,
                String.valueOf(config.getSampleRate()));

        // 是否返回中间识别结果
        transcriber.setEnableIntermediateResult(false);
        // 是否生成并返回标点符号
        transcriber.setEnablePunctuation(true);
        transcriber.addCustomedParam("speech_noise_threshold", config.getSpeechNoiseThreshold());
        // 是否将返回结果规整化,比如将一百返回为100
        transcriber.setEnableITN(true);
        transcriber.addCustomedParam("disfluency", false);
        transcriber.addCustomedParam("max_sentence_silence", config.getMaxSentenceSilence());

        // Step2 此方法将以上参数设置序列化为json发送给服务端,并等待服务端确认
        try {
            transcriber.start();
        } catch (Exception e) {
            log.error("Start speech transcriber [{}] error caused.", callId);
            throw new RuntimeException(e);
        }
        final String taskId = transcriber.getTaskId();
        log.info("ASR task [{}][{}] started.", taskId, callId);
        resultListener.setTaskId(taskId);

        final AsrModel asrModel = new AsrModel(callId, (model) -> stopAsr(model, transcriber));

        // Step3 语音数据来自声音文件用此方法,控制发送速率;
        // 若语音来自实时录音,不需控制发送速率直接调用recognizer.sent(ins)即可
        threadPoolTaskExecutor.execute(() -> {
            long startSendDelay = config.getStartSendDelay();
            if (startSendDelay > 0) {
                try {
                    TimeUnit.MILLISECONDS.sleep(startSendDelay);
                } catch (final InterruptedException e) {
                    log.warn("Wait ASR task [{}][{}] to start send data error caused.", taskId,
                            callId, e);
                }
            }

            final long startTime = System.currentTimeMillis();
            resultListener.setStartTime(startTime);

            try (final FileInputStream ins = new FileInputStream(recording)) {
                final byte[] data = new byte[config.getSendSize()];
                int emptyDataReadTimes = 0;
                final byte[] silenceData = new byte[320]; // 静音数据（全零）
                while (!asrModel.getStopped().get()) {
                    final int readLength = ins.read(data);
                    if (readLength > 0) {
                        transcriber.send(data, readLength);
                        emptyDataReadTimes = 0;
                        //long sendInterval = getSleepDelta(readLength, config.getSampleRate());
                        try {
                            TimeUnit.MILLISECONDS.sleep(20);
                        } catch (final InterruptedException e) {
                            log.warn("Sleep wait send data error caused.", e);
                        }

                    } else {

                        if (!CallStateManager.isIgnoreStopReceiveVoiceFlag(callId)
                                && !CallStateManager.isStopReceiveVoiceFlag(callId)) {
                            ++emptyDataReadTimes;
                            log.debug("Send file [{}] data fail [{}] times.", recording,
                                    emptyDataReadTimes);
                            long sendFailBreakTimes = config.getSendFailBreakTimes();
                            if (emptyDataReadTimes >= sendFailBreakTimes) {
                                log.info(
                                        "emptyDataReadTimes Send file [{}] data fail [{}] times, break.",
                                        recording, sendFailBreakTimes);
                                break;
                            }
                        }
                        // 发送静音
                        transcriber.send(silenceData, silenceData.length);
                        try {
                            TimeUnit.MILLISECONDS.sleep(20);
                        } catch (final InterruptedException e) {
                            log.warn("Sleep wait send data error caused.", e);
                        }
                    }
                }
                log.info("ASR task [{}][{}] complete, took [{}].", taskId, callId,
                        System.currentTimeMillis() - startTime);
            } catch (final Throwable e) {
                log.error("Open ASR task [{}][{}] file [{}] stream error caused.", taskId, callId,
                        recording, e);
            } finally {
                log.info("ASR task [{}][{}] complete finally, stop transcriber.", taskId, callId);
                stopAsr(asrModel, transcriber);
                if (asrModel.isDeleteFile()) {
                    log.info("Remove recording file [{}].", recording);
                    // FileUtils.deleteQuietly(recording);
                }
            }
        });

        return asrModel;
    }

    /**
     * 计算发送间隔
     *
     * @param dataSize 数据大小
     * @param sampleRate 采样率
     * @return 发送间隔
     */
    public static int getSleepDelta(int dataSize, int sampleRate) {
        return (dataSize * 10 * 8000) / (160 * sampleRate);
    }

    /**
     * 停止语音识别
     *
     * @param model 语音识别模型
     * @param transcriber 语音识别器
     */
    public void stopAsr(AsrModel model, SpeechTranscriber transcriber) {
        final String callId = model.getCallId();

        if (model.getStopped().compareAndSet(false, true)) {
            log.info("Stop ASR transcriber [{}].", callId);
            final SpeechReqProtocol.State state = transcriber.getState();
            if (state != SpeechReqProtocol.State.STATE_CLOSED) {
                try {
                    transcriber.stop();
                } catch (final Exception e) {
                    log.error("Stop ASR transcriber [{}][{}] error caused.", callId, e);
                } finally {
                    log.info("Close ASR transcriber [{}].", callId);
                    transcriber.close();
                }
            } else {
                log.error("ASR transcriber [{}] has been closed, ignore stop.", callId);
            }
        } else {
            log.info("ASR transcriber [{}] stopped, ignore stop.", callId);
        }
    }

    /**
     * 停止语音识别
     *
     * @param uuid 呼叫ID
     */
    @Override
    public void stop(String callId) {}
}
