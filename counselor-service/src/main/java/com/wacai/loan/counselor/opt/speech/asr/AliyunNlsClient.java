/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-28 11:42:07
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-28 11:42:13
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.speech.asr;

import org.apache.commons.lang3.StringUtils;
import com.alibaba.nls.client.protocol.NlsClient;
import com.alibaba.nls.client.transport.Connection;
import com.alibaba.nls.client.transport.ConnectionListener;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AliyunNlsClient extends NlsClient {

    private TokenService tokenService;

    public AliyunNlsClient(TokenService accessToken) {
        super(accessToken.getToken());
        this.tokenService = accessToken;
        log.info("AliYun NLS Client initialized with token: {}", accessToken.getToken());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Connection connect(final ConnectionListener listener) throws Exception {
        final String refreshedToken = tokenService.getRefreshedToken();
        if (StringUtils.isNotBlank(refreshedToken)) {
            log.info("Refresh AliYun access token [{}].", refreshedToken);
            setToken(refreshedToken);
        }
        return super.connect(listener);
    }
}
