package com.wacai.loan.counselor.rebalance.zookeeper;

import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson2.JSON;
import com.wacai.loan.counselor.opt.sip.dataobject.agent.AgentDTO;
import com.wacai.loan.counselor.opt.sip.sipsession.AiAgentRegistryService;
import com.wacai.loan.counselor.rebalance.AgentInstanceManager;
import com.wacai.loan.counselor.utils.InetAddressUtil;
import com.wacai.loan.counselor.utils.Utils;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-20 16:58
 */
@Service
@Slf4j
public class ZkLeaderService {

    @Resource
    private ZookeeperRegistry zookeeperRegistry;


    @Value("${spring.env.active:}")
    private String env;

    @Resource
    AgentInstanceManager agentInstanceManager;

    @Resource
    private AiAgentRegistryService aiAgentRegistryService;

    public void selector() {

        String leader = StringUtils.join(Contants.LEADER, Utils.zkPath(env,Contants.ZK_TYPE));

        if (!zookeeperRegistry.checkExists(leader)) {
            zookeeperRegistry.createPersistent(leader);
        }
        String instance = StringUtils.join(Contants.INSTANCE, Utils.zkPath(env,Contants.ZK_TYPE), "/",
            InetAddressUtil.getIpAddress());

        //获取本地已经注册到Pjsua2的Account ,主要是防止异常情况
        List<AgentDTO> list = aiAgentRegistryService.getLocalAccount();
        log.info("******initializer Pjsua2 Account**** :{}", JSON.toJSONString(list));

        if (!zookeeperRegistry.checkExists(instance)) {
            zookeeperRegistry.createEphemeralData(instance, JSON.toJSONString(list));
        }

        zookeeperRegistry.selector(leader,agentInstanceManager);
    }

    public void close() {
            zookeeperRegistry.close();
    }
}
