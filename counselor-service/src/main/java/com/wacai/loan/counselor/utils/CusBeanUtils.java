package com.wacai.loan.counselor.utils;

import java.beans.FeatureDescriptor;
import java.util.Arrays;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/23 17:36
 */
@UtilityClass
public class CusBeanUtils {
    public static <T> T copyPropertiesIgnoreNull(Object src, T dst) {
        final BeanWrapper beanWrapper = new BeanWrapperImpl(src);
        BeanUtils.copyProperties(src, dst, Arrays.stream(beanWrapper.getPropertyDescriptors())
            .map(FeatureDescriptor::getName)
            .filter(name -> beanWrapper.getPropertyValue(name) == null)
            .toArray(String[]::new));
        return dst;
    }
}
