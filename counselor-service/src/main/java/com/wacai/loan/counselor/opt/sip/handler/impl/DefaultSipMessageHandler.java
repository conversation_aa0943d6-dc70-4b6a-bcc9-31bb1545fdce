/*
 * @Author: shunhua
 * @Date: 2025-04-29 11:14:13
 * @LastEditors: shunhua
 * @LastEditTime: 2025-05-04 15:28:54
 * @Description: 
 */
package com.wacai.loan.counselor.opt.sip.handler.impl;

 

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.wacai.loan.counselor.opt.sip.handler.SipMessageHandler;
import com.wacai.loan.counselor.opt.sip.handler.SipMessageInfo;
import com.wacai.loan.counselor.opt.sip.util.SipMessageUtils;

/**
 * 默认 SIP 消息处理器
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class DefaultSipMessageHandler implements SipMessageHandler {
    
    /**
     * 解析 SIP 消息
     * 
     * @param sipMessage SIP 消息
     * @return SIP 消息信息
     */
    @Override
    public SipMessageInfo parseSipMessage(int sipCallId,String sipMessage) {
        String callUuid = SipMessageUtils.extractCallId(sipMessage);
        String mobile = SipMessageUtils.extractFromDn(sipMessage);
        String userData = SipMessageUtils.extractUserData(sipMessage);
        String inboundChannelUuid = SipMessageUtils.extractInboundChannelUuid(sipMessage);
    
        if (StringUtils.isBlank(mobile)) {
            mobile = SipMessageUtils.getCallNum(sipMessage);
        }
        
        
        return SipMessageInfo.builder()
        .sipCallid(sipCallId)
            .callUuid(callUuid)
            .mobile(mobile)
            .userData(userData)
            .inboundChannelUuid(inboundChannelUuid)
            .build();
    }
}
