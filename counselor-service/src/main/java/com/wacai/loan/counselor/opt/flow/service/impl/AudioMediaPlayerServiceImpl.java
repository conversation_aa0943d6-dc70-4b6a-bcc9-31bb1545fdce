/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-13 19:35:49
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-24 16:29:30
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.service.impl;

import static org.pjsip.pjsua2.pjmedia_type.PJMEDIA_TYPE_AUDIO;
import java.io.File;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import org.apache.commons.lang3.tuple.Pair;
import org.pjsip.pjsua2.AudioMedia;
import org.pjsip.pjsua2.CallInfo;
import org.pjsip.pjsua2.CallMediaInfo;
import org.pjsip.pjsua2.CallMediaInfoVector;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.opt.base.state.CallState;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.flow.service.AudioMediaPlayerService;
import com.wacai.loan.counselor.opt.sip.cache.CallContext;
import com.wacai.loan.counselor.opt.sip.service.EndpointService;
import com.wacai.loan.counselor.opt.sip.sipsession.AiAgentAudioMediaPlayer;
import com.wacai.loan.counselor.opt.sip.sipsession.AiAgentCall;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AudioMediaPlayerServiceImpl implements AudioMediaPlayerService {

    @Resource
    private CallContext callContext;
    
    @Resource
    private EndpointService endpointService;

    @Override
    @SneakyThrows
    public void play(String callId, File file) {
        endpointService.libRegisterThread();
        CallState callState = CallStateManager.getState(callId);
        while (callState.isPlaying()) {
            TimeUnit.MILLISECONDS.sleep(100);
        }
        callState.setPlaying(true);
        callState.setLastAccessTime();
        callState.setStopReceiveVoiceFlag(true);
        AiAgentCall call = callContext.get(callId);

        AiAgentAudioMediaPlayer player =call.createPlayer(file);

        log.info("callId :{} start Player file: {}", callId, file.getName());

        AiAgentAudioMediaPlayer oldPlayer = call.getPlayer();

        if (Objects.nonNull(oldPlayer) && oldPlayer != player) {
            if (!oldPlayer.isEof() && callState.isMediaActive() && call.isActive()
                    && call.hasMedia()) {
                Pair<CallMediaInfo, AudioMedia> infoMediaPair = getAudioMedia(call);
                if (Objects.nonNull(infoMediaPair) && Objects.nonNull(infoMediaPair.getValue())) {
                    AudioMedia audioMedia = infoMediaPair.getValue();
                    log.info(
                            "callId: {},startPlay---> Stop previous player [{}] transmit. asr speechStop",
                            callId, oldPlayer);
                    oldPlayer.stopTransmit(audioMedia);
                }
            }
        }

        if (player != oldPlayer) {
            if (Objects.nonNull(oldPlayer)) {
                oldPlayer.delete();
            }
        }
       call.setPlayer(player);
     
        if (callState.isMediaActive() && call.isActive() && call.hasMedia()) {
            final Pair<CallMediaInfo, AudioMedia> infoMediaPair = getAudioMedia(call);
            if (Objects.nonNull(infoMediaPair) && Objects.nonNull(infoMediaPair.getValue())) {
                log.info("callId: {} strtTransmit ",callId);
                player.startTransmit(infoMediaPair.getValue());
            }
        }
    }


    public Pair<CallMediaInfo, AudioMedia> getAudioMedia(AiAgentCall call) {
        CallInfo callInfo = call.getInfo();
        final CallMediaInfoVector media = callInfo.getMedia();
        log.debug("Get audio media, media size [{}].", media.size());
        // Iterate all the call medias
        for (int i = 0; i < media.size(); i++) {
            final CallMediaInfo callMediaInfo = media.get(i);
            log.debug("Call media info [{}].", callMediaInfo);
            if (callMediaInfo.getType() == PJMEDIA_TYPE_AUDIO) {
                return Pair.of(callMediaInfo, AudioMedia.typecastFromMedia(call.getMedia(i)));
            }
        }
        return null;
    }


    public void resumePlay(String callId) {
        endpointService.libRegisterThread();
        AiAgentCall call = callContext.get(callId);
        final Pair<CallMediaInfo, AudioMedia> infoMediaPair = call.getAudioMedia();
        final AudioMedia audioMedia = infoMediaPair != null ? infoMediaPair.getValue() : null;
        if (audioMedia == null) {
            return;
        }
        call.getPlayer().resume(audioMedia);
    }


    public void stopPlay(String callId) {
        endpointService.libRegisterThread();
        AiAgentCall call = callContext.get(callId);
        AiAgentAudioMediaPlayer player = call.getPlayer();
        if (player != null && !player.isEof() && CallStateManager.getState(callId).isMediaActive()
                && call.isActive() && call.hasMedia()) {
            final Pair<CallMediaInfo, AudioMedia> infoMediaPair = call.getAudioMedia();
            final AudioMedia audioMedia = infoMediaPair != null ? infoMediaPair.getValue() : null;
            try {
                log.info("callId: {} stopTransmit ", callId);
                player.stopTransmit(audioMedia);
                CallStateManager.getState(callId).setPlaying(false);
            } catch (Exception e) {
                log.error("callId: {}   stopTransmit ", callId, e);
            }
        }
    }
}
