/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-06 17:38:26
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-08 16:59:37
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.service.impl;


import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.opt.base.constant.FlowActionEnums;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.flow.cache.WorkFlowLoadCache;
import com.wacai.loan.counselor.opt.flow.core.FlowActionExecutorService;
import com.wacai.loan.counselor.opt.flow.core.FlowNodeAdapterService;
import com.wacai.loan.counselor.opt.flow.core.WorkFlowNode;
import com.wacai.loan.counselor.opt.flow.model.FlowActionDTO;
import com.wacai.loan.counselor.opt.flow.model.NodeAdapterDTO;
import com.wacai.loan.counselor.opt.flow.service.UserInputService;
import com.wacai.loan.counselor.opt.flow.service.WorkFlowNodeService;
import com.wacai.loan.counselor.opt.sip.sipsession.AiAgentCall;
import com.wacai.loan.counselor.utils.SimpleShortPhraseCache;
import com.wacai.loan.counselor.utils.VoiceAssistantDetector;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class WorkFlowNodeServiceImpl implements WorkFlowNodeService {


  private final UserInputService userInputService;


  private final FlowActionExecutorService flowActionExecutorService;


  private final FlowNodeAdapterService flowNodeAdapterService;

  private final WorkFlowLoadCache workFlowLoadCache;


  /**
   * 开始 startFlow 流程
   * 
   * @param call
   * @param workFlowNode
   */
  @Override
  public void startFirstFlow(String callId, WorkFlowNode workFlowNode) {
    workFlowNode.setCurrentNode(callId);
    flowActionExecutorService.executeAction(new FlowActionDTO(callId, FlowActionEnums.PLAY),
        workFlowNode);
    userInputService.add("", callId, CallStateManager.getMobile(callId),
        workFlowNode.getWorkflowNodeConfig());
  }



  /**
   * asr 回调
   * 
   * @param call
   * @param text
   */
  @Override
  public void asrCallback(AiAgentCall call, String text) {
    Optional<WorkFlowNode> workNodeOptional =
        CallStateManager.getState(call.getCallId()).getCurrentNode();
    if (!workNodeOptional.isPresent()) {
      log.warn("callId: {} asrCallback not flowNode", call.getCallId());
      return;
    }
    WorkFlowNode workFlowNode = workNodeOptional.get();

    boolean supportPlayPause = Boolean.TRUE
        .equals(CallStateManager.get(call.getCallId(), "supportPlayPause", Boolean.class));

    if (supportPlayPause) {
      flowActionExecutorService.executeAction(
          new FlowActionDTO(call.getCallId(), FlowActionEnums.RESUME_PLAY), workFlowNode);
      log.info("callId: {} supportPlayPause, so stop play", call.getCallId());
      return;
    }

    if (CallStateManager.getState(call.getCallId()).isStopReceiveVoiceFlag()) {
      // 播放话术的时候，记录用户说的话，入库和逻辑处理
      userInputService.updateUserInputOfVoiceAssistant(call.getCallId(), text);
      Boolean isVoiceAssistantReply = VoiceAssistantDetector.isLikelyAssistantReply(text);
      if (isVoiceAssistantReply && !workFlowNode.getWorkflowNodeConfig().isEnd()) {
        log.info("callId: {} text: [{}] is voice assistant reply", call.getCallId(), text);
        flowActionExecutorService.executeAction(
            new FlowActionDTO(call.getCallId(), FlowActionEnums.STOP_PLAY), workFlowNode);
        actionNextFlow(call, workFlowNode, text);
      } else {
        String combined = SimpleShortPhraseCache.addAndCheck(call.getCallId(), text);
        log.info("callId: {} original text: [{}], combined result: [{}]", call.getCallId(), text,
            combined);
        if (StringUtils.isNotEmpty(combined) && !workFlowNode.getWorkflowNodeConfig().isEnd()) {
          flowActionExecutorService.executeAction(
              new FlowActionDTO(call.getCallId(), FlowActionEnums.STOP_PLAY), workFlowNode);
          actionNextFlow(call, workFlowNode, text);
        }
      }
      return;
    }
    SimpleShortPhraseCache.clear(call.getCallId());
    actionNextFlow(call, workFlowNode, text);
  }



  @Override
  public void actionNextFlow(AiAgentCall call, WorkFlowNode workFlowNode, String text) {

    if (workFlowNode.getWorkflowNodeConfig().isEnd()) {
      log.info("callId: {},flowCode: {} isEnd,so flow end，action hangup", call.getCallId(),
          workFlowNode.getWorkflowNodeConfig().getWorkNodeCode());
      call.hangup();
      return;
    }

    if (!CallStateManager.getState(call.getCallId()).isRunning()) {
      log.info("callId {} is not running,so flow end,action hangup", call.getCallId());
      call.hangup();
      return;
    }

    CallStateManager.getState(call.getCallId()).clearWaitHandle();
    String flowNodeCodeTimeout =
        workFlowNode.getWorkflowNodeConfig().getWorkNodeCode() + "_timeout";
    CallStateManager.getState(call.getCallId()).put(flowNodeCodeTimeout, 0);

    NodeAdapterDTO nodeAdapterDTO = flowNodeAdapterService.adapterFlowNode(call.getCallId(),
        workFlowNode.getWorkflowNodeConfig(), text);

    log.info("callId: {} ,input:{},tip: {} flowCode:【current:{},next:{}】", call.getCallId(), text,
        workFlowNode.getWorkflowNodeConfig().getTip(),
        workFlowNode.getWorkflowNodeConfig().getWorkNodeCode(), nodeAdapterDTO.getNextNodeCode());

    userInputService.update(call.getCallId(), text,
        Objects.nonNull(nodeAdapterDTO) ? nodeAdapterDTO.getAdapterResult() : "");

    if (Objects.isNull(nodeAdapterDTO) || StringUtils.isEmpty(nodeAdapterDTO.getNextNodeCode())) {
      log.info("callId: {} nodeAdapterDTO is null,so flow end", call.getCallId());
      call.hangup();
      return;
    }

    WorkFlowNode nextflowNode = workFlowLoadCache.getWorkFlowNodeOfFill(
        call.getAiFlowBO().getAiFlow().getCode(), nodeAdapterDTO.getNextNodeCode());

    if (Objects.isNull(nextflowNode)) {
      log.info("callId: {} nextflowNode is null,so flow end", call.getCallId());
      call.hangup();
      return;
    }
    nextflowNode.setCurrentNode(call.getCallId());
    if (!flowNodeAdapterService.adapterRepeatTimesLimit(call.getCallId(), nextflowNode)) {
      log.info("callId: {} flowNodeRepeatTimesLimit,so flow end", call.getCallId());
      call.hangup();
      return;
    }

    if (!CallStateManager.hasState(call.getCallId())) {
      log.warn("callId: {} hasState is false,so return", call.getCallId());
      return;
    }

    userInputService.add("", call.getCallId(), CallStateManager.getMobile(call.getCallId()),
        nextflowNode.getWorkflowNodeConfig());

    // 下个节点转接
    FlowActionEnums flowActionEnums = null;
    if (nextflowNode.getWorkflowNodeConfig().getTransferLimitConfig().isTransfer(call.getCallId())) {
      flowActionEnums = FlowActionEnums.TRANSFER;
    } else {
      flowActionEnums = FlowActionEnums.PLAY;
    }

    log.info("callId: {} nextflowNode : {} executeAction: {}", call.getCallId(),
        nextflowNode.getWorkflowNodeConfig().getWorkNodeCode(), flowActionEnums);
    flowActionExecutorService.executeAction(new FlowActionDTO(call.getCallId(), flowActionEnums),
        nextflowNode);
  }

}
