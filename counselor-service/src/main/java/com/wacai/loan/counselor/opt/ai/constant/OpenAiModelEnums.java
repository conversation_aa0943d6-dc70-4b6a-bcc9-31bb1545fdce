/*
 * @Author: shunhua
 * @Date: 2024-12-20 19:27:27
 * @LastEditors: shunhua
 * @LastEditTime: 2025-05-15 19:17:06
 * @Description: 
 */

package com.wacai.loan.counselor.opt.ai.constant;

import java.util.Arrays;
import java.util.Optional;

import lombok.Getter;

@Getter
public enum OpenAiModelEnums {

    OPENAI_MODEL("Doubao-lite-32k-0428"),
 
    DEEPSEEK_MODEL("deepseek-v3-250324"),

    DOUBAO_CHARACTER_MODEL("Doubao-lite-32k_character-241015");
    
    private String modelName;
 
    OpenAiModelEnums(String modelName) {
        this.modelName = modelName;
    }
   

    public static Optional<OpenAiModelEnums> of(String modelName) {
        if (modelName == null) {
            return Optional.empty();
        }
        return Arrays.stream(OpenAiModelEnums.values())
            .filter(openAiModelEnums -> openAiModelEnums.getModelName().equalsIgnoreCase(modelName))
            .findFirst();
    }
}
