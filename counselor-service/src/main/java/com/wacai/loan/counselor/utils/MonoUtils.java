package com.wacai.loan.counselor.utils;

import java.time.Duration;
import java.util.function.Consumer;

import lombok.Setter;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/22 11:16
 */
@UtilityClass
@Slf4j
public class MonoUtils {

    /**
     * common set up in context app
     */
    @Setter
    Consumer<String> alertConsumer;

    public <T> Mono<T> convenientMono(Mono<T> mono, String subject, String msg) {
        return mono.subscribeOn(Schedulers.boundedElastic())
            .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))
                .doAfterRetry(
                    retrySignal -> log.error("Retry {} {} Retries {}", subject, msg, retrySignal.totalRetries(),
                        retrySignal.failure()))
                .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                    final Throwable failure = retrySignal.failure();
                    return failure;
                }))
            .doOnError(throwable -> {
                log.error("{} err {}", subject, msg, throwable);
                if (alertConsumer != null) {
                    alertConsumer.accept(String.format("%s err %s %s", subject, msg, throwable.getMessage()));
                }
            })
            .doOnSuccess(o -> log.info("{} Success {} param {}", subject, msg, o));
    }

    public void run(Runnable runnable, String tip) {
        Mono.fromRunnable(() -> {
            try {
                runnable.run();
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
        })
            .subscribeOn(Schedulers.boundedElastic())
            .doOnSuccess(o -> log.info("{} done", tip))
            .doOnError(e -> log.error("{} err", tip, e))
            .subscribe();
    }

    public void run(Runnable runnable, String tip, String id) {
        run(runnable, tip + " identification: " + id);
    }

    public Mono<?> logMono(Mono<?> mono, String msg) {
        return mono.doOnSuccess(o -> log.info("{} done", msg)).doOnError(e -> log.error("{} err", msg, e));
    }

}
