/*
 * @Author: shunhua
 * 
 * @Date: 2025-04-30 16:00:52
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-25 19:17:54
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.sip.sipsession;

import java.io.File;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import org.apache.commons.lang3.StringUtils;
import org.pjsip.pjsua2.AudioMedia;
import org.pjsip.pjsua2.AudioMediaPlayer;
import com.wacai.loan.counselor.opt.base.AppSpringContext;
import com.wacai.loan.counselor.opt.base.state.CallState;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.base.threadpool.ExecutorServiceHandlerRegistry;
import com.wacai.loan.counselor.opt.flow.core.FlowNavigatorService;
import com.wacai.loan.counselor.opt.flow.core.WorkFlowNode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AiAgentAudioMediaPlayer extends AudioMediaPlayer {


    private String callId;

    private File file;


    private static final ExecutorService audioExecutors =
            ExecutorServiceHandlerRegistry.getExecutorServiceHandler(10, "audio");

    public AiAgentAudioMediaPlayer(String callId, File file) {
        this.callId = callId;
        this.file = file;
    }


    private final AtomicBoolean eof = new AtomicBoolean(false);

    public boolean isEof() {
        return eof.get();
    }

    public void resetEof() {
        eof.set(false);
    }


    @Override
    public void onEof2() {
        if (eof.compareAndSet(false, true)) {
            audioExecutors.execute(() -> doVoiceEndAction());
        } else {
            log.warn("callId: {} onEof2 already triggered, skipping.", callId);
        }
    }

    @Override
    public synchronized void delete() {
        if (Objects.equals(Thread.currentThread().getName(), "Finalizer")) {
            log.info("no del player in Finalizer thread [{}].", this);
            return;
        }
        super.delete();
    }

    @Override
    public void stopTransmit(final AudioMedia sink) throws Exception {
        if (!isEof()) {
            try {
                super.stopTransmit(sink);
            } catch (Exception e) {
                log.error("stopTransmit error!", e);
            }
        } else {
            log.warn("Player [{}] is EOF, ignore stop transmit.", this);
        }
    }

    @SneakyThrows
    public void resume(AudioMedia sink) {
        startTransmit(sink);
    }

    private void doVoiceEndAction() {
        if (StringUtils.isEmpty(callId)) {
            log.error("callId voice play end,but callId is empty,File: {}", file.getAbsolutePath());
            return;
        }
        CallState callState = CallStateManager.getState(callId);
        if (Objects.isNull(callState)) {
            log.error("callId : {} voice play end,but callState is empty,File: {}", callId,
                    file.getAbsolutePath());
            return;
        }
        callState.setPlaying(false);
        callState.setStopReceiveVoiceFlag(false);
        callState.setLastAccessTime();
        Optional<WorkFlowNode> flowNodeOpt = callState.getCurrentNode();
        if (flowNodeOpt.isPresent()) {
            log.info("callId : {}  play onEof2, FlowNodeCode : {}", callId,
                    flowNodeOpt.get().getWorkflowNodeConfig().getWorkNodeCode());
            AppSpringContext.getBean(FlowNavigatorService.class).adapterNodeWait(callId,
                    flowNodeOpt.get());
        } else {
            log.error("callId : {} voice play end,but flowNode is empty,File: {}", callId,
                    file.getAbsolutePath());
        }
    }
}
