/*
 * @Author: shunhua
 * 
 * @Date: 2025-04-30 16:28:52
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-05-04 17:19:19
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.speech.model;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
public class AsrModel {

    private final String callId;

    private final Consumer<AsrModel> stopCallback;

    private AtomicBoolean stopped = new AtomicBoolean(false);
    
    private boolean deleteFile = true;

    private transient long startTime;


    public void stop() {
        stopCallback.accept(this);
    }
}
