/*
 * @Author: shunhua
 * @Date: 2025-07-10 18:49:54
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-10 18:53:00
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.manager;

import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.dao.po.AiFlowToNode;
import com.wacai.loan.counselor.dao.repository.AiFlowToNodeMapper;

@Service
public class AiFlowToNodeManager {

    @Resource
    private AiFlowToNodeMapper aiFlowToNodeMapper;


    public List<AiFlowToNode> listByAiFlowCode(String flowCode){
        AiFlowToNode aiFlowToNode=new AiFlowToNode();
        aiFlowToNode.setAiFlowCode(flowCode);
        return aiFlowToNodeMapper.select(aiFlowToNode);
    }
}
