package com.wacai.loan.counselor.dao.po;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * ai流程到节点连接表
 *
 * @TableName ai_flow_to_node
 */
@Table(name = "ai_flow_to_node")
@Data
@Entity
public class AiFlowToNode implements Serializable {
    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 流程编码
     */
    @Column(name = "ai_flow_code")
    private String aiFlowCode;

    /**
     * 节点编码
     */
    @Column(name = "ai_flow_node_code")
    private String aiFlowNodeCode;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    private Date updatedTime;

    private static final long serialVersionUID = 1L;
}