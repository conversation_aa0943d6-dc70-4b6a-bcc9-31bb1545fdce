/*
 * @Author: shunhua
 * 
 * @Date: 2025-08-25 17:48:20
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-25 17:56:57
 * 
 * @Description:
 */
package com.wacai.loan.counselor.utils;

import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;

public class FlowTipUtils {
 

    public static boolean isFormat(String tip) {
        return StringUtils.isNotEmpty(tip)&& tip.contains("${");
    }

    public static String format(String template, Map<String, Object> values) {
        StringSubstitutor sub = new StringSubstitutor(values);
        String result = sub.replace(template);
        return result;
    }
}
