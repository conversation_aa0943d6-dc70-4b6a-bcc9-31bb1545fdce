/*
 * @Author: shunhua
 * @Date: 2025-04-30 17:50:07
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-15 14:42:51
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.listener.asr;

import java.util.Objects;
import java.util.concurrent.ExecutorService;
import javax.annotation.Resource;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import com.wacai.loan.counselor.opt.base.event.AsrCompletedEvent;
import com.wacai.loan.counselor.opt.base.threadpool.ExecutorServiceHandlerRegistry;
import com.wacai.loan.counselor.opt.flow.service.WorkFlowNodeService;
import com.wacai.loan.counselor.opt.sip.cache.CallContext;
import com.wacai.loan.counselor.opt.sip.sipsession.AiAgentCall;
import lombok.extern.slf4j.Slf4j;

/**
 * ASR 事件监听器
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
public class AsrEventListener {
    

    @Resource
    private CallContext callContext;

    @Resource
    private WorkFlowNodeService workFlowNodeService;

    
    private ExecutorService aService=ExecutorServiceHandlerRegistry.getExecutorServiceHandler(20,"asr-callback");

    /**
     * 处理 ASR 识别完成事件
     * 
     * @param event ASR 识别完成事件
     */
    @EventListener
    public void handleAsrCompletedEvent(AsrCompletedEvent event) {
      //  log.info("callId: {} ASR callback text {}", event.getCallId(), event.getText());
       AiAgentCall call= callContext.get(event.getCallId());
       if(Objects.isNull(call)){
        log.warn("call is null so ignore asr event {}",event.toString());
        return ;
       }
      aService.execute(()->workFlowNodeService.asrCallback(call, event.getText())); 
    }
}
