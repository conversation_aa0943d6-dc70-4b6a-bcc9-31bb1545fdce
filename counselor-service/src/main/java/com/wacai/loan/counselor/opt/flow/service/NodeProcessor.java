/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-15 17:42:53
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-25 17:43:01
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.service;

import com.wacai.loan.counselor.opt.flow.model.NodeAdapterDTO;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;

public interface NodeProcessor {


    /**
     * 适配流程节点
     * 
     * @param callId
     * @param workflowNodeConfig
     * @param text
     */
    NodeAdapterDTO adapterTranferFlowNode(String callId, WorkflowNodeConfig workflowNodeConfig,
            String text);

    /**
     * 适配 Script
     * 
     * @param callId
     * @param workflowNodeConfig
     * @param text
     * @return
     */
    NodeAdapterDTO adapterScriptFlowNode(String callId, WorkflowNodeConfig workflowNodeConfig,
            String text);


    /**
     *  节点次数限制
     * @param callId
     * @param workflowNodeConfig
     * @return
     */
    Boolean flowNodeRepeatTimesLimit(String callId,WorkflowNodeConfig workflowNodeConfig);

}
