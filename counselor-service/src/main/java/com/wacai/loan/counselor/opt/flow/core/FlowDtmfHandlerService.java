/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-25 17:53:12
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-25 17:58:46
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.core;

import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.opt.base.constant.FlowActionEnums;
import com.wacai.loan.counselor.opt.flow.model.FlowActionDTO;
import com.wacai.loan.counselor.opt.flow.service.WorkFlowNodeService;
import com.wacai.loan.counselor.opt.sip.cache.CallContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class FlowDtmfHandlerService {

    private final FlowActionExecutorService flowActionExecutorService;

    private final WorkFlowNodeService workFlowNodeService;

    private final CallContext callContext;

    public void dtmfHandler(String callId, WorkFlowNode workFlowNode, String digit) {
        flowActionExecutorService
                .executeAction(new FlowActionDTO(callId, FlowActionEnums.STOP_PLAY), workFlowNode);
        workFlowNodeService.actionNextFlow(callContext.get(callId), workFlowNode, digit);
    }
}
