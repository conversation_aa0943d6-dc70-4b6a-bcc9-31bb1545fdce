package com.wacai.loan.counselor.utils;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/**
 * InetAddressUtil
 *
 * <AUTHOR>
 * @since 2019/11/5
 */
public class InetAddressUtil {
    public static final String LOCK = "lock";

    /** 十六进制IP **/
    private static String hexIp = null;
    private static String ipAddress = null;

    private static List<String> localIpList = null;

    public static final String LINK_AGENT_VIP = "*************";
    public static final String LINK_AGENT_EXTIP = "***************";

    static {
        synchronized (LOCK) {
            try {
                localIpList = getLoaclAddressList();
                ipAddress = getLoaclAddress();
                if (ipAddress != null && !"unknown".equals(ipAddress)) {
                    hexIp = getHexIP(ipAddress);
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
    }


    private static List<String> getLoaclAddressList(){
        List<String> lips = new ArrayList<>();
        try {
            Enumeration<NetworkInterface> nics = NetworkInterface.getNetworkInterfaces();
            if (nics == null) return lips;

            while (nics.hasMoreElements()) {
                NetworkInterface nic = nics.nextElement();
                if(!nic.isUp()){
                    continue;
                }
                Enumeration<InetAddress> addresses = nic.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    if (address.isSiteLocalAddress()) {
                        byte[] addressBytes = address.getAddress();
                        if (addressBytes.length == 4) {
                            int intIp = ByteBuffer.wrap(addressBytes).getInt();
                            lips.add(parseInt2IPv4(intIp));
                        } else if (addressBytes.length == 16) {
                        }
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return lips;
    }


  private  static String getLoaclAddress() {
    try {
        Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
        while (interfaces.hasMoreElements()) {
            NetworkInterface iface = interfaces.nextElement();
            if (!iface.isUp() || iface.isLoopback() || iface.isVirtual()) continue;

            Enumeration<InetAddress> addresses = iface.getInetAddresses();
            while (addresses.hasMoreElements()) {
                InetAddress addr = addresses.nextElement();
                if (!addr.isLoopbackAddress() && addr instanceof Inet4Address && addr.isSiteLocalAddress()) {
                    return addr.getHostAddress(); // 返回第一个符合条件的内网 IPv4
                }
            }
        }
    } catch (SocketException e) {
        e.printStackTrace();
    }
    return "unknown";
}
//        if(lips.isEmpty()){
//            return "unknown";
//        }
//        return lips.stream()
//                .filter(ip -> !LINK_AGENT_VIP.equals(ip))
//                .findFirst()
//                .orElse("unknown");

    public static String parseInt2IPv4(int ipv4){
        if(ipv4 == 0)
            return "unknown";

        StringBuilder result = new StringBuilder("");
        result.append(ipv4 >> 24 & 0xff).append(".")
                .append(ipv4 >> 16 & 0xff).append(".")
                .append(ipv4 >> 8 & 0xff).append(".")
                .append(ipv4 & 0xff);

        return result.toString();
    }

    private static String getHexIP(String ip) {
        String[] ips = ip.split("\\.");
        StringBuilder sb = new StringBuilder();
        for (String column : ips) {
            String hex = Integer.toHexString(Integer.parseInt(column));
            if (hex.length() == 1) {
                sb.append('0').append(hex);
            } else {
                sb.append(hex);
            }

        }
        return sb.toString();
    }

    public static String getHexIp() {
        return hexIp;
    }

    public static String getIpAddress() {
        return ipAddress;
    }

    public static boolean isHasVip(String vip){
        return StringUtils.isNotBlank(vip) && localIpList.contains(vip);
    }
}
