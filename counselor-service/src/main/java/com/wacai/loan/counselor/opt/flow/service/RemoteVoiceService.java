/*
 * @Author: shunhua
 * @Date: 2025-05-12 17:22:47
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-25 11:01:50
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.service;

import java.io.File;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowRemoteVoiceConfig;

public interface RemoteVoiceService {


 File acquireVoice(String callId,String flowCode,WorkFlowRemoteVoiceConfig aiFlowNodeVoiceCacheParam,boolean isDelete);

 File generalTTSFile(String callId, String text);
}
