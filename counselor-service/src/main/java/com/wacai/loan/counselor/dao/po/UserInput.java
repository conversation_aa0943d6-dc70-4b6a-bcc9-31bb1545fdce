package com.wacai.loan.counselor.dao.po;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * 用户输入内容
 *
 * @TableName user_input
 */
@Table(name = "user_input")
@Data
@Entity
public class UserInput implements Serializable {
    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户输入
     */
    private String input;

    /**
     * uuid
     */
    @Column(name = "call_uuid")
    private String callUuid;

    /**
     * ai提示
     */
    private String tip;

    /**
     * 用户电话
     */
    private String phone;

    /**
     * 额外信息
     */
    @Column(name = "ext_info")
    private String extInfo;

    /**
     * 备注
     */
    private String comment;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    private Date updatedTime;

    /**
     * 流程 code
     */
    @Column(name = "flow_code")
    private String flowCode;

    @Column(name = "semantics")
    private String semantics;

    @Column(name = "node_context")
    private String nodeContext;

    @Column(name = "ai_semantics")
    private String aiSemantics;

    private static final long serialVersionUID = 1L;
}