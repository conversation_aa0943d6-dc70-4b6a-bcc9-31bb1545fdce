/*
 * @Author: shunhua
 * 
 * @Date: 2025-08-01 11:12:34
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-01 16:43:15
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.cache;

import javax.annotation.Resource;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.opt.flow.manager.AiFlowManager;
import com.wacai.loan.counselor.opt.flow.model.AiFlowBO;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class WorkFlowBOCache {


    @Resource
    private AiFlowManager aiFlowManager;

    @Cacheable(value = "workflowByCode", key = "#flowCode")
    public AiFlowBO getAiFlowByCode(String flowCode) {
        return new AiFlowBO(aiFlowManager.getAiFlowByCode(flowCode));
    }

    @Cacheable(value = "workflowByCampCode", key = "#campCode")
    public AiFlowBO getAiFlowByCampCode(String campCode) {
        return new AiFlowBO(aiFlowManager.findAiFlowByCampCode(campCode));
    }
}
