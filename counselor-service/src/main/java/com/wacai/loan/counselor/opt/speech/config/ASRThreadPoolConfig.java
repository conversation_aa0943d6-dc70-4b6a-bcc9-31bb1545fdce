/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-18 10:47:02
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-18 10:56:36
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.speech.config;

import java.util.concurrent.ThreadPoolExecutor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import lombok.extern.slf4j.Slf4j;

@Configuration
@Slf4j
public class ASRThreadPoolConfig {

    @Bean("asrExecutor")
    public ThreadPoolTaskExecutor asrExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 线程池参数配置
        executor.setCorePoolSize(20); // 核心线程数
        executor.setThreadNamePrefix("ai-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());

        // 初始化线程池结构
        executor.initialize();

        // 手动预启动所有核心线程
        prestartCoreThreads(executor);

        return executor;
    }

    private void prestartCoreThreads(ThreadPoolTaskExecutor executor) {
        try {
            ThreadPoolExecutor nativeExecutor = executor.getThreadPoolExecutor();
            int started = nativeExecutor.prestartAllCoreThreads();
            log.info("Aliyun ASR 线程池预热成功，启动了 {} 个核心线程", started);
        } catch (Exception e) {
            log.error("预热线程池失败", e);
        }
    }
}
