/*
 * @Author: shunhua
 * @Date: 2025-04-29 11:12:28
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-25 19:24:34
 * @Description: 
 */
package com.wacai.loan.counselor.opt.sip.cache;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.stereotype.Component;
import com.wacai.loan.counselor.opt.sip.sipsession.AiAgentCall;
import lombok.Getter;


/**
 * 呼叫上下文
 *
 * 用于存储呼叫对象
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class CallContext {

    /**
     * 呼叫映射
     */
    @Getter
    private final Map<String, AiAgentCall> callMap = new ConcurrentHashMap<>();

    /**
     * 添加呼叫
     *
     * @param callUuid 呼叫UUID
     * @param call 呼叫对象
     */
    public void put(String callUuid, AiAgentCall call) {
        callMap.put(callUuid, call);
    }

    /**
     * 获取呼叫
     *
     * @param callUuid 呼叫UUID
     * @return 呼叫对象
     */
    public AiAgentCall get(String callUuid) {
        return callMap.get(callUuid);
    }

    /**
     * 移除呼叫
     *
     * @param callUuid 呼叫UUID
     * @return 呼叫对象
     */
    public AiAgentCall remove(String callUuid) {
        return callMap.remove(callUuid);
    }

    /**
     * 是否包含呼叫
     *
     * @param callUuid 呼叫UUID
     * @return 是否包含
     */
    public boolean contains(String callUuid) {
        return callMap.containsKey(callUuid);
    }

    /**
     * 获取呼叫数量
     *
     * @return 呼叫数量
     */
    public int size() {
        return callMap.size();
    }

    /**
     * 清空呼叫
     */
    public void clear() {
        callMap.clear();
    }
}
