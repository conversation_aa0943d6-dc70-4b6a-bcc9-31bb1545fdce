package com.wacai.loan.counselor.opt.base.state;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import com.wacai.loan.counselor.opt.flow.core.WorkFlowNode;
import lombok.Getter;
import lombok.Setter;


/**
 * 呼叫状态类 使用原子变量确保线程安全
 *
 * <AUTHOR>
 * @version 1.0
 */
public class CallState {

    /**
     * 是否正在运行
     */
    @Getter
    private final AtomicBoolean running = new AtomicBoolean(true);

    /**
     * 是否停止接收语音标志
     */
    private final AtomicBoolean stopReceiveVoiceFlag = new AtomicBoolean(false);

    /**
     * 是否忽略停止接收语音标志
     */
    private final AtomicBoolean ignoreStopReceiveVoiceFlag = new AtomicBoolean(false);

    /**
     * 最后访问时间（毫秒）
     */
    private final AtomicLong lastAccessTime = new AtomicLong(0);


    /**
     * 媒体是否运行
     */
    private final AtomicBoolean mediaActive = new AtomicBoolean(false);


    private volatile WorkFlowNode currentNode;


    private final AtomicBoolean playing = new AtomicBoolean(false);


    @Getter
    private final Map<String, Object> callContext = new ConcurrentHashMap<>();


    @Getter
    private ExecutorService speechExecutor = Executors.newSingleThreadExecutor();

    @Getter
    @Setter
    private ScheduledFuture<?> waitHandle;

    @Getter
    private final List<String> workFlowList = new CopyOnWriteArrayList<>();

    public CallState() {

    }


    public void setCurrentNode(WorkFlowNode node) {
        this.currentNode = node;
    }

    public Optional<WorkFlowNode> getCurrentNode() {
        return Optional.ofNullable(currentNode);
    }

    /**
     * 检查是否正在运行
     *
     * @return 是否正在运行
     */
    public boolean isRunning() {
        return running.get();
    }

    public void put(String key, Object value) {
        callContext.put(key, value);
    }

    public Object get(String key) {
        return callContext.get(key);
    }

    public <T> T getOrDefault(String key, Class<T> clazz, T defaultValue) {
        Object t = callContext.get(key);
        return clazz.cast(t == null ? defaultValue : t);
    }

    public <T> T get(String key, Class<T> clazz) {
        Object t = callContext.get(key);
        return clazz.cast(t);
    }

    /**
     * 设置运行状态
     *
     * @param value 运行状态
     */
    public void setRunning(boolean value) {
        running.set(value);
    }

    public void setPlaying(boolean value) {
        playing.set(value);
    }

    public boolean isPlaying() {
        return playing.get() & isRunning();
    }

    /**
     * 检查是否停止接收语音标志
     *
     * @return 是否停止接收语音
     */
    public boolean isStopReceiveVoiceFlag() {
        return stopReceiveVoiceFlag.get();
    }

    /**
     * 设置停止接收语音标志 false 开始接听语音标志
     * 
     *
     * @param value 标志值
     */
    public void setStopReceiveVoiceFlag(boolean value) {
        stopReceiveVoiceFlag.set(value);

    }

    /**
     * 检查是否忽略停止接收语音标志
     *
     * @return 是否忽略停止接收语音
     */
    public boolean isIgnoreStopReceiveVoiceFlag() {
        return ignoreStopReceiveVoiceFlag.get();
    }

    /**
     * 设置忽略停止接收语音标志
     *
     * @param value 标志值
     */
    public void setIgnoreStopReceiveVoiceFlag(boolean value) {
        ignoreStopReceiveVoiceFlag.set(value);

    }

    /**
     * 原子性地检查并设置运行状态
     *
     * @param expect 期望的当前值
     * @param update 要设置的新值
     * @return 如果成功更新则返回true
     */
    public boolean compareAndSetRunning(boolean expect, boolean update) {
        boolean result = running.compareAndSet(expect, update);

        return result;
    }

    /**
     * 原子性地检查并设置媒体状态
     *
     * @param expect 期望的当前值
     * @param update 要设置的新值
     * @return 如果成功更新则返回true
     */
    public boolean compareAndSetMediaActive(boolean expect, boolean update) {
        boolean result = mediaActive.compareAndSet(expect, update);

        return result;
    }

    public boolean isMediaActive() {
        return mediaActive.get();
    }

    /**
     * 获取最后访问时间
     *
     * @return 最后访问时间（毫秒）
     */
    public long getLastAccessTime() {
        return lastAccessTime.get();
    }


    public void setLastAccessTime() {
        lastAccessTime.set(System.currentTimeMillis());
    }

    public void putAll(Map<String, Object> map) {
        callContext.putAll(map);
    }

    public void addWorkFlowList(String workFlowCode) {
        workFlowList.add(workFlowCode);
    }

    public void clearWaitHandle() {
        if (waitHandle != null) {
            waitHandle.cancel(true);
            waitHandle = null;
        }
    }

    public void shutdown() {
        if (speechExecutor != null && !speechExecutor.isShutdown()) {
            speechExecutor.shutdown();
        }
        speechExecutor = null;
    }
}
