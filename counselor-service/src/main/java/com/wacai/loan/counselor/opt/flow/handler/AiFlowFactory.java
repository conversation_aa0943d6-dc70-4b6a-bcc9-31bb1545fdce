/*
 * @Author: shunhua
 * 
 * @Date: 2025-04-30 13:49:18
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-24 15:17:35
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.handler;

import java.util.Objects;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson2.JSONObject;
import com.wacai.loan.counselor.opt.base.cache.CallUserDataCache;
import com.wacai.loan.counselor.opt.base.constant.FlowConstants;
import com.wacai.loan.counselor.opt.flow.cache.WorkFlowBOCache;
import com.wacai.loan.counselor.opt.flow.model.AiFlowBO;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AiFlowFactory {


    @Resource
    private WorkFlowBOCache workFlowBOCache;



    public AiFlowBO buildAiFlowBO(String callId, String userData) {

        if (StringUtils.isEmpty(userData)) {
            return null;
        }
        JSONObject uJsonObject = CallUserDataCache.handle(userData);
        final String flowCode = uJsonObject.getString(FlowConstants.FLOW_CODE);
        if (StringUtils.isNotEmpty(flowCode)) {
            CallUserDataCache.cache(callId, uJsonObject);
            return workFlowBOCache.getAiFlowByCode(flowCode);
        }
        return null;
    }

    public AiFlowBO getDialEventDTO(String callId, String userData, String campCode) {

        JSONObject paramObject = CallUserDataCache.getCache(callId);
        if (Objects.isNull(paramObject)) {
            paramObject = CallUserDataCache.handle(userData);
            CallUserDataCache.cache(callId, paramObject);
        }
        String flowCode = paramObject.getString(FlowConstants.FLOW_CODE);
        if (StringUtils.isNotEmpty(flowCode)) {
            return workFlowBOCache.getAiFlowByCode(flowCode);
        } else {
            AiFlowBO aiFlowBO = workFlowBOCache.getAiFlowByCampCode(campCode);
            paramObject.put(FlowConstants.FLOW_CODE, aiFlowBO.getAiFlow().getCode());
            CallUserDataCache.cache(callId, paramObject);
            return aiFlowBO;
        }
    }

}
