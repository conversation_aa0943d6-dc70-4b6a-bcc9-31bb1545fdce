/*
 * @Author: shunhua
 * 
 * @Date: 2025-04-30 14:20:17
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-05-04 15:30:13
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.sip.service.impl;

import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.pjsip.pjsua2.Endpoint;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.opt.sip.service.EndpointService;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class EndpointServiceImpl implements EndpointService {

    @Resource
    Endpoint endpoint;

    @Override
    public void libRegisterThread(String name) {
        if (endpoint.libIsThreadRegistered()) {
            return;
        }

        if (StringUtils.isBlank(name)) {
            name = "EndpointService default";
        }

        try {
            endpoint.libRegisterThread(name);
        } catch (Exception e) {
            log.error("libRegisterThread err ", e);
        }
    }

    @Override
    public void libRegisterThread() {
        libRegisterThread(null);
    }
}
