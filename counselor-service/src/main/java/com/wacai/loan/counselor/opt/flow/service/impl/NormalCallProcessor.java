/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-12 16:30:59
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-09-05 10:19:26
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.service.impl;

import static com.wacai.loan.counselor.opt.base.alarm.AlarmTemplateCst.TTS_FILE_ALARM;
import java.io.File;
import java.util.Objects;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.pjsip.pjsua2.CallOpParam;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson2.JSONObject;
import com.wacai.loan.counselor.opt.base.alarm.AlarmService;
import com.wacai.loan.counselor.opt.base.alarm.NotifierContext;
import com.wacai.loan.counselor.opt.base.cache.CallUserDataCache;
import com.wacai.loan.counselor.opt.base.constant.FlowConstants;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowRemoteVoiceConfig;
import com.wacai.loan.counselor.opt.flow.handler.transfer.TransferService;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import com.wacai.loan.counselor.opt.flow.service.AudioMediaPlayerService;
import com.wacai.loan.counselor.opt.flow.service.CallProcessor;
import com.wacai.loan.counselor.opt.flow.service.RemoteVoiceService;
import com.wacai.loan.counselor.opt.sip.cache.CallContext;
import com.wacai.loan.counselor.opt.sip.service.EndpointService;
import com.wacai.loan.counselor.opt.sip.util.SipMessageUtils;
import com.wacai.loan.counselor.utils.Utils;
import com.wacai.loan.counselor.utils.VoiceUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class NormalCallProcessor implements CallProcessor {

  @Resource
  private RemoteVoiceService remoteVoiceService;

  @Resource
  private AudioMediaPlayerService audioMediaPlayerService;

  @Resource
  private TransferService transferService;

  @Resource
  private EndpointService endpointService;

  @Resource
  private CallContext callContext;

  @Resource
  private AlarmService alarmService;

  @Override
  public boolean transfer(String callId, WorkflowNodeConfig workflowNodeConfig) {

    String transferAgent = CallUserDataCache.getTranferAgent(callId);

    String transferQueueCode = CallUserDataCache.getTranferQueueCode(callId);

    if (StringUtils.isEmpty(transferQueueCode)) {
      transferQueueCode = workflowNodeConfig.getTransferLimitConfig().getTransferQueueCode();
    }
    String referToSipAgent = transferService.acqTransfer(callId, transferAgent,
        workflowNodeConfig.getTenantCode(), transferQueueCode);
    if (StringUtils.isEmpty(referToSipAgent)) {
      log.error(
          "Transfer failed for callId: {}, transferAgent or queueCode: [{},{}], referToSipAgent is empty",
          callId, transferAgent, transferQueueCode);
      return false;
    }
    String mobile = CallStateManager.getMobile(callId);
    CallOpParam callOpParam = SipMessageUtils.buildHeader(mobile);
    try {
      endpointService.libRegisterThread("xfer");
      callContext.get(callId).xfer(referToSipAgent, callOpParam);
      CallOpParam callOpParam1 = new CallOpParam(true);
      callOpParam1.setReason("refer");
      return true;
    } catch (Exception e) {
      log.error("Transfer failed for callId: {}, referToSipAgent: {}, error: {}", callId,
          referToSipAgent, e.getMessage(), e);
    }
    return false; // Placeholder for transfer logic
  }

  @Override
  public void playVoice(String callId, String text, String fileName,
      WorkflowNodeConfig nodeConfig) {
    if (!CallStateManager.getState(callId).isRunning()) {
      log.warn("Call is not running, cannot play voice for callId: {}", callId);
      return;
    }
    File audioFile = null;
    // 优先播放text
    if (StringUtils.isNotBlank(fileName)) {
      audioFile = new File(fileName);
    } else if (StringUtils.isNotBlank(nodeConfig.getVoicePath())) {
      audioFile = playLocal(callId, text, nodeConfig.getVoicePath());
    } else if (Objects.nonNull(nodeConfig.getVoiceJsonConfig())) {
      audioFile = playRemote(callId, nodeConfig.getWorkNodeCode(), nodeConfig.getVoiceJsonConfig());
    } else {
      log.warn("callId: {} not voicePath and voiceJsonConfig", callId);
    }


    if (audioFile == null || !audioFile.exists()) {
      if (audioFile != null) {
        if (!Utils.waitForFileExists(audioFile, 2, 100)) {
          log.warn("callId: {} playVoice file not exist after retries, fileName: {}", callId,
              audioFile.getAbsolutePath());
          audioFile = remoteVoiceService.generalTTSFile(callId + "_tts", text);
          alarmService.alert(NotifierContext.builder().content(String.format(TTS_FILE_ALARM, callId,
              text, audioFile == null ? null : audioFile.getName())).build());
          log.info("callId: {} playVoice file is null, generate tts directly, fileName: {}", callId,
              Objects.isNull(audioFile) ? "null" : audioFile.getAbsolutePath());
        }
      } else {
        log.warn("callId: {} playVoice file is null, generate tts directly", callId);
        audioFile = remoteVoiceService.generalTTSFile(callId + "_tts", text);
        alarmService.alert(NotifierContext.builder().content(String.format(TTS_FILE_ALARM, callId,
            text, audioFile == null ? null : audioFile.getName())).build());
        log.info("callId: {} playVoice file is null, generate tts directly, fileName: {}", callId,
            Objects.isNull(audioFile) ? "null" : audioFile.getAbsolutePath());
      }
    }

    if (Objects.nonNull(audioFile) && CallStateManager.getState(callId).isRunning()) {
      audioMediaPlayerService.play(callId, audioFile);
    }
  }

  @Override
  public void resumePlay(String callId) {
    audioMediaPlayerService.resumePlay(callId);
  }

  @Override
  public void stopPlay(String callId) {
    audioMediaPlayerService.stopPlay(callId);
  }


  private File playRemote(String callId, String flowCode,
      WorkFlowRemoteVoiceConfig voiceJsonConfig) {
    JSONObject userData = CallUserDataCache.getCache(callId);
    Object paramObj = CallUserDataCache.getRemoteVoiceParam(userData);
    String cusParam = WorkFlowRemoteVoiceConfig.acqJsonData(paramObj);
    return remoteVoiceService.acquireVoice(callId, flowCode,
        voiceJsonConfig.buildMergedParamSmart(cusParam), true);
  }



  private File playLocal(String callId, String text, String localVoicePath) {
    final String voicePath = VoiceUtils.getVoicePath(callId, localVoicePath);
    boolean notBlank = StringUtils.isNotBlank(voicePath);
    String fileName;
    fileName = FlowConstants.getWavPath() + voicePath;
    if (notBlank) {
      if (!fileName.endsWith(FlowConstants.AUDIO_FORMAT)) {
        fileName += FlowConstants.AUDIO_FORMAT;
      }
      return new File(fileName);
    }
    return null;
  }


  @Override
  public void cyclePlayVoice(String callId, String voicePath) {
    if (!CallStateManager.getState(callId).isRunning()) {
      return;
    }
    CallStateManager.getState(callId).setStopReceiveVoiceFlag(true);
    File audioFile = playLocal(callId, "", voicePath);
    audioMediaPlayerService.play(callId, audioFile);
  }

  @Override
  public void playRealtimeVoice(String callId, Byte[] voiceBytes) {
    // TODO Auto-generated method stub
    throw new UnsupportedOperationException("Unimplemented method 'playRealtimeVoice'");
  }


}
