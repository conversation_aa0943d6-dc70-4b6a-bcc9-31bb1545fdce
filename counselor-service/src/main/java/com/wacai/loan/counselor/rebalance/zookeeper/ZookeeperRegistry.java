package com.wacai.loan.counselor.rebalance.zookeeper;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.apache.zookeeper.CreateMode;
import org.apache.zookeeper.KeeperException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.rebalance.AgentInstanceManager;
import com.wacai.loan.counselor.utils.InetAddressUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-20 16:27
 */
@Service
@Slf4j
public class ZookeeperRegistry {

    @Value("${dubbo.zk.servers}")
    private String serverLists;

    private CuratorFramework curatorFramework;

    private LeaderSelectorListener listener;

    @PostConstruct
    public void init() {
        log.info("Counselor-voice: zookeeper registry center init, server lists is: {}.",
                serverLists);
        CuratorFrameworkFactory.Builder builder = CuratorFrameworkFactory.builder()
                .connectString(serverLists).retryPolicy(new ExponentialBackoffRetry(10000, 3));
        curatorFramework = builder.build();
        curatorFramework.start();
        try {
            curatorFramework.blockUntilConnected(10000 * 3, TimeUnit.MILLISECONDS);
            log.info("Counselor-voice init:zookeeper registry center success");
        } catch (final Exception ex) {
            log.error(" Counselor-voice zookeeper init exception : ", ex);
        }
    }

    @SneakyThrows
    public void selector(String path, AgentInstanceManager agentInstanceManager) {
        String name = "client-" + InetAddressUtil.getIpAddress();
        this.listener =
                new LeaderSelectorListener(curatorFramework, path, name, agentInstanceManager);
        listener.start();
    }

    public boolean checkExists(String path) {
        try {
            if (curatorFramework.checkExists().forPath(path) != null) {
                return true;
            }
        } catch (Exception e) {
        }
        return false;
    }

    public void createEphemeral(String path) {
        try {
            curatorFramework.create().creatingParentsIfNeeded().withMode(CreateMode.EPHEMERAL)
                    .forPath(path);
        } catch (KeeperException.NodeExistsException e) {
            throw new IllegalStateException(e.getMessage(), e);
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    public void createEphemeralData(String path, String data) {
        try {
            curatorFramework.create().creatingParentsIfNeeded().withMode(CreateMode.EPHEMERAL)
                    .forPath(path, data.getBytes(StandardCharsets.UTF_8));
        } catch (KeeperException.NodeExistsException e) {
            throw new IllegalStateException(e.getMessage(), e);
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    public void createPersistent(String path) {
        try {
            curatorFramework.create().creatingParentsIfNeeded().withMode(CreateMode.PERSISTENT)
                    .forPath(path);
        } catch (KeeperException.NodeExistsException e) {
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    public List<String> getChildren(String path) {
        try {
            return curatorFramework.getChildren().forPath(path);
        } catch (KeeperException.NoNodeException e) {
            return new ArrayList<>();
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    public String getData(String path) {
        try {
            byte[] data = curatorFramework.getData().forPath(path);
            if (Objects.isNull(data) || data.length == 0) {
                return StringUtils.EMPTY;
            }
            return new String(data, Charset.forName("utf-8"));
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    public void setData(String path, byte[] data) {
        try {
            curatorFramework.setData().inBackground().forPath(path, data);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    public void close() {
        if (Objects.nonNull(listener)) {
            listener.close();
        }
        curatorFramework.close();
    }
}
