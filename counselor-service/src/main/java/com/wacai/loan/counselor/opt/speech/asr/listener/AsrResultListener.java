/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-04 14:57:05
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-24 14:09:14
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.speech.asr.listener;

import org.apache.commons.lang3.StringUtils;
import com.alibaba.nls.client.protocol.asr.SpeechTranscriberListener;
import com.alibaba.nls.client.protocol.asr.SpeechTranscriberResponse;
import com.wacai.loan.counselor.opt.base.AppSpringContext;
import com.wacai.loan.counselor.opt.base.event.AsrCompletedEvent;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 语音识别结果监听器
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class AsrResultListener extends SpeechTranscriberListener {


    private final String callId;

    @Getter
    @Setter
    private String taskId;

    @Getter
    @Setter
    private long startTime;

    /**
     * 构造函数
     *
     * @param appContext 应用上下文
     * @param callId 呼叫ID
     */
    public AsrResultListener(String callId) {
        this.callId = callId;
    }

    @Override
    public void onTranscriptionResultChange(SpeechTranscriberResponse response) {
        // 处理实时识别结果变化
        final String text = response.getTransSentenceText();
        final long duration = response.getTransSentenceTime();

        log.info(
        "ASR [{}] temp result, name [{}], status [{}], index [{}], result [{}], duration [{}]",
        taskId, response.getName(),
        // 状态码 20000000 表示正常识别
        response.getStatus(),
        // 句子编号，从1开始递增
        response.getTransSentenceIndex(), text,
        // 当前已处理的音频时长，单位是毫秒
        duration);
    }

    @Override
    public void onTranscriberStart(SpeechTranscriberResponse response) {
        log.info("onTranscriberStart start {}", response);
    }

    @Override
    public void onSentenceBegin(SpeechTranscriberResponse response) {
        log.debug("ASR sentence [{}][{}] begin.", taskId, callId);
    }

    @Override
    public void onSentenceEnd(SpeechTranscriberResponse response) {
        final String text = response.getTransSentenceText();
        final long duration = response.getTransSentenceTime() - response.getSentenceBeginTime();
        startTime = System.currentTimeMillis();
        log.info(
                "ASR [{}] result, confidence [{}], status [{}], index [{}], result [{}], time [{}], begin_time: {},time:{}",
                taskId, response.getConfidence(),
                // 状态码 20000000 表示正常识别
                response.getStatus(),
                // 句子编号，从1开始递增
                response.getTransSentenceIndex(), text,
                // 当前已处理的音频时长，单位是毫秒
                duration, response.getSentenceBeginTime(), response.getTransSentenceTime());

        log.info("callId: {} ASR callback text {}", callId, text);
        if (StringUtils.isNotBlank(text)) {
            AppSpringContext.getContext().publishEvent(new AsrCompletedEvent(callId, text));
        }
    }

    @Override
    public void onTranscriptionComplete(SpeechTranscriberResponse response) {
        log.info("ASR task [{}][{}] complete.", taskId, callId);
    }

    @Override
    public void onFail(SpeechTranscriberResponse response) {
        log.error("ASR task [{}][{}] failed: {}", taskId, callId, response.getStatus());
    }
}
