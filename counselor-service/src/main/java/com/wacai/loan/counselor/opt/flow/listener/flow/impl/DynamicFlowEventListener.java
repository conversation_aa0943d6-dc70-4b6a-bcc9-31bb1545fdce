/*
 * @Author: shunhua
 * 
 * @Date: 2025-09-04 14:17:41
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-09-05 10:03:57
 * 
 * @Description: 动态语音流 dynamic flow event listener
 */
package com.wacai.loan.counselor.opt.flow.listener.flow.impl;

import java.util.Objects;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.opt.base.constant.AiFlowTypeEnums;
import com.wacai.loan.counselor.opt.base.dto.DialEventDTO;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.base.util.CounselorFileUtils;
import com.wacai.loan.counselor.opt.flow.listener.flow.AbstractFlowEventListener;
import com.wacai.loan.counselor.opt.flow.model.AiFlowBO;
import com.wacai.loan.counselor.opt.flow.service.DynamicFlowService;
import com.wacai.loan.counselor.opt.sip.sipsession.AiAgentCall;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class DynamicFlowEventListener extends AbstractFlowEventListener {

    @Resource
    private DynamicFlowService dynamicFlowService;

    @Override
    protected AiFlowTypeEnums getSupportedFlowType() {
        return AiFlowTypeEnums.DYNAMIC;
    }

    @Override
    protected void handleRingEvent(DialEventDTO eventDTO) {}

    @Override
    protected void handleAnswerEvent(DialEventDTO eventDTO) {
        AiAgentCall call = callContext.get(eventDTO.getUuid());
        if (Objects.isNull(call)) {
            log.warn("aiFlow is null, startAsr by default {}", eventDTO.getUuid());
            return;
        }
        AiFlowBO aiFlowBO = call.getAiFlowBO();
        if (Objects.isNull(aiFlowBO)) {
            log.warn("aiFlowBO is null, startAsr by default {}", eventDTO.getUuid());
            return;
        }
        if (Objects.nonNull(aiFlowBO.getDynamicCallbackConfig())
                && StringUtils.isNotEmpty(aiFlowBO.getDynamicCallbackConfig().getWsUrl())) {
            String mobile = CallStateManager.getMobile(call.getCallId());
            log.info("start dynamic webSocket client callId= {},mobile= {}", call.getCallId(),
                    mobile);
            String recordDir = Objects.nonNull(aiFlowBO)
                    && StringUtils.isNotBlank(aiFlowBO.getAiFlow().getRecordPath())
                            ? aiFlowBO.getAiFlow().getRecordPath()
                            : CounselorFileUtils.getTempDir();
            dynamicFlowService.connectWebSocket(call.getCallId(), mobile, recordDir,
                    aiFlowBO.getDynamicCallbackConfig().getWsUrl());
        }

    }

    @Override
    protected void handleHangupEvent(DialEventDTO eventDTO) {

    }



}
