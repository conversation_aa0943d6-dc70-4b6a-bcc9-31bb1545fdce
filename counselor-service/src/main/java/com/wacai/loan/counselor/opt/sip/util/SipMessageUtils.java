package com.wacai.loan.counselor.opt.sip.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.pjsip.pjsua2.CallOpParam;
import org.pjsip.pjsua2.SipHeader;
import org.pjsip.pjsua2.SipTxOption;

/**
 * SIP 消息工具类
 * 
 * 用于解析 SIP 消息
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class SipMessageUtils {

    private static final Pattern CALL_ID_PATTERN = Pattern.compile("Call-ID:\\s*([^\\r\\n]+)");

    private static final Pattern FROM_DN_PATTERN =
            Pattern.compile("From: \"Extension \\d+\" <sip:(\\d+)@.*?>;tag=.*");

    private static final Pattern USER_DATA_PATTERN = Pattern.compile("X-User-Data:\\s*(\\S+)\\s*");


    private static final Pattern X_INBOUND_CHANNEL_UUID =
            Pattern.compile("X-linkup-inbound-channel-uuid:\\s+([\\w\\-]+)");

    private static final Pattern X_CALL_NUMBER = Pattern.compile("X-Call-Number:\\s+([\\w\\-]+)");

    /**
     * 提取呼叫ID
     * 
     * @param sipMessage SIP消息
     * @return 呼叫ID
     */
    public static String extractCallId(String sipMessage) {
        Matcher matcher = CALL_ID_PATTERN.matcher(sipMessage);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }


    /**
     * 提取来源号码
     * 
     * @param sipMessage SIP消息
     * @return 来源号码
     */
    public static String extractFromDn(String sipMessage) {
        Matcher matcher = FROM_DN_PATTERN.matcher(sipMessage);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }

    /**
     * 提取用户数据
     * 
     * @param sipMessage SIP消息
     * @return 用户数据
     */
    public static String extractUserData(String sipMessage) {
        Matcher matcher = USER_DATA_PATTERN.matcher(sipMessage);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }

    /**
     * 提取入站通道UUID
     * 
     * @param sipMessage SIP消息
     * @return 入站通道UUID
     */
    public static String extractInboundChannelUuid(String sipMessage) {
        Matcher matcher = X_INBOUND_CHANNEL_UUID.matcher(sipMessage);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }

    /**
     * 获取呼叫号码
     * 
     * @param sipMessage SIP消息
     * @return 呼叫号码
     */
    public static String getCallNum(String sipMessage) {
        Matcher matcher = X_CALL_NUMBER.matcher(sipMessage);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }



    public static CallOpParam buildHeader(String mobile) {
        CallOpParam callOpParam = new CallOpParam();
        SipTxOption sipTxOption = new SipTxOption();

        sipTxOption.getHeaders().add(buildHeader("X-Call-Number", mobile));
        // sipTxOption.getHeaders().add(buildHeader("X-User-Data",
        // CallUserDataCache.getCacheDataOfEecode(robotCall.getUuid())));
        callOpParam.setTxOption(sipTxOption);
        return callOpParam;
    }

    private static SipHeader buildHeader(String name, String value) {
        SipHeader h = new SipHeader();
        h.setHName(name);
        h.setHValue(value);
        return h;
    }
}
