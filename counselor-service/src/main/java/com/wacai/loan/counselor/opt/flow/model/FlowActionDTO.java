/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-25 16:07:17
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-25 18:51:53
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.model;

import com.wacai.loan.counselor.opt.base.constant.FlowActionEnums;
import lombok.Data;

@Data
public class FlowActionDTO {

    private String callId;

    private String fileName;

    private FlowActionEnums flowActionEnums;

    public FlowActionDTO(String callId, String fileName, FlowActionEnums flowActionEnums) {
        this.callId = callId;
        this.fileName = fileName;
        this.flowActionEnums = flowActionEnums;
    }

    public FlowActionDTO(String callId, FlowActionEnums flowActionEnums) {
        this.callId = callId;
        this.flowActionEnums = flowActionEnums;
    }
}
