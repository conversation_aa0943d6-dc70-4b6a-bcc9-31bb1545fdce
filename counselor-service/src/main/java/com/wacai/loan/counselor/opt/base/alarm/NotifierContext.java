package com.wacai.loan.counselor.opt.base.alarm;

import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

/**
 * 告警对象
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2019-10-23 15:03
 */
@ToString
@Data
@Builder
public class NotifierContext {

    /**
     * 通知标题；
     */
    private String title;
    /**
     * 可用枚举值为：fatal error warn info
     * info -> 企业微信&飞书
     * warn -> 企业微信&飞书、邮件
     * error -> 企业微信&飞书、邮件、短信
     * fatal -> 企业微信&飞书、邮件、短信、电话
     */
    private String level;
    /**
     * 通知内容；
     */
    private String content;
    /**
     * 接收人花名数组；
     */
    private List<String> receivers;
    /**
     * 是个 key-value 字典格式，此次事件的唯一标识，相同 label 的通知会被认为是同一类事件，才会触发收敛策略。 非常重要，不可为空；
     */
    private Map<String, Object> label;

}
