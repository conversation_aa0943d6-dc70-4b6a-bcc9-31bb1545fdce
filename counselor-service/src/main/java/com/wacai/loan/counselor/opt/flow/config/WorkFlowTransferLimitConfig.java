/*
 * @Author: shunhua
 * 
 * @Date: 2025-08-08 14:46:35
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-08 16:58:40
 * 
 * @Description: 转接限制配置
 */
package com.wacai.loan.counselor.opt.flow.config;

import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wacai.loan.counselor.opt.base.state.CallState;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class WorkFlowTransferLimitConfig {

    private String transferQueueCode;


    public WorkFlowTransferLimitConfig(String transferQueueCode) {
        this.transferQueueCode = transferQueueCode;
    }

    /**
     * 包含的节点
     */
    private List<String> containWorkFlowCodeList;

    /**
     * 排除的节点
     */
    private List<String> excludeWorkFlowCodeList;



    public static WorkFlowTransferLimitConfig builderTransferLimitConfig(String context,
            String transferQueueCode) {

        if (StringUtils.isEmpty(context)) {
            return new WorkFlowTransferLimitConfig(transferQueueCode);
        } else {
            JSONObject contextJson = JSON.parseObject(context);
            if (Objects.isNull(contextJson) || !contextJson.containsKey("transferLimitConfig")) {
                return new WorkFlowTransferLimitConfig(transferQueueCode);
            }

            JSONObject transferLimitConfigJson = contextJson.getJSONObject("transferLimitConfig");

            // 反序列化为 WorkFlowTransferLimitConfig
            WorkFlowTransferLimitConfig config =
                    transferLimitConfigJson.to(WorkFlowTransferLimitConfig.class);

            // 设置队列编码
            config.setTransferQueueCode(transferQueueCode);
            return config;
        }
    }


    public boolean isTransfer(String callId) {

        if (StringUtils.isEmpty(transferQueueCode)) {
            return false;
        } else {
            if (CollectionUtils.isEmpty(containWorkFlowCodeList)
                    && CollectionUtils.isEmpty(excludeWorkFlowCodeList)) {
                return true;
            }
            CallState callState = CallStateManager.getState(callId);
            if (Objects.isNull(callState)) {
                return false;
            }
            List<String> workFlowList = callState.getWorkFlowList();

            boolean containMatch = CollectionUtils.isEmpty(containWorkFlowCodeList)
                    || workFlowList.stream().anyMatch(containWorkFlowCodeList::contains);

            boolean excludeMatch = CollectionUtils.isEmpty(excludeWorkFlowCodeList)
                    || workFlowList.stream().noneMatch(excludeWorkFlowCodeList::contains);

            return containMatch && excludeMatch;
        }
    }

}
