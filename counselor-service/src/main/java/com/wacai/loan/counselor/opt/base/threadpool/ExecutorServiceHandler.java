/*
 * @Author: shunhua
 * @Date: 2025-05-04 14:53:39
 * @LastEditors: shunhua
 * @LastEditTime: 2025-05-04 15:12:27
 * @Description: 
 */

package com.wacai.loan.counselor.opt.base.threadpool;

import java.util.concurrent.ExecutorService;

/**
 * 线程池服务处理器
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2019-03-21 17:55
 */

public interface ExecutorServiceHandler {

    /**
     * 创建线程池服务对象
     *
     * @param type
     * @return 线程池服务对象
     */
    ExecutorService createExecutorService(final String type);

    ExecutorService createExecutorService(int coreNum,final String type);
}
