package com.wacai.loan.counselor.ai;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import com.google.common.collect.Lists;
import com.wacai.loan.counselor.opt.base.cache.CallUserDataCache;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 15:06
 */
@UtilityClass
@Slf4j
public class AiTipFlowHelper {

    public static CallUserDataCache callUserDataCache;

    static {
        callUserDataCache = new CallUserDataCache();
    }

    public static final String[] PHONE_AUTHORIZATION =
            {"嗯", "有", "行", "好", "可以", "奥", "嗷啦", "哦尅", "嗯你说", "当然", "你说", "嗯嗯", "嗯呗", "嗯的", "能啊",
                    "接受", "好的", "行吧", "同意", "授权吧", "授权", "搞呗", "ok", "行行行", "行啊"};

    public static final String[] PHONE_N = {"不对", "不是这个", "不是本机", "不是同一个", "不同", "不一样", "错", "不是这个",
            "不是这个手机", "不是", "不对", "不是我的手机", "不是这个号", "不对", "不对", "另外一个", "其他", "另外", "另一个", "有别",
            "别", "另外", "新号码", "新换", "改过", "不是我", "这个不是", "不可以", "不用这个", "给你个电话", "我报一个", "你记另外一个",
            "不是这个号码手机"};
    public static final String[] PHONE_Y = {"对对", "就是这个", "是", "我的手机就是", "就这个手机号", "手机号就是", "这个就是",
            "没错", "就是这个号码", "是手机号", "是我", "就这个", "是我", "这手机号是我", "手机号码是我", "嗯", "手机号是我", "只要是我",
            "号码就是", "电话号码就是", "这个手机是", "是这个", "这个手机号码对", "是这个手机号", "对", "就这个号码", "是好拜拜", "没错", "正确",
            "对就是", "就是", "就这个手机", "对好", "对就手机", "是我自己", "对", "对就加这个", "对那个手机号", "就那个手机号", "对是这",
            "就我这个", "就是", "手机号是我", "对手机号", "嗯对", "对头", "对就这手机号", "手机号就是我", "对这个手机号", "就这个电话号码",
            "是可以收到", "手机号码是我的", "我是用这个手机号", "就这手机号", "就本机号码", "就手机号", "就是手机号", "对对对", "就这个电话",
            "我手机号就是", "这个手机号就是", "我手机号码就是", "就是用这个号", "这手机号就是", "这个手机号就行", "是本手机", "本机号码", "是这个号码",
            "好", "就我手机号码", "对是", "呃对", "对是我的", "对这个号", "就是这个", "就这个号"};

    public static final String[] MAIN_ANSWER_QUERY = {"骗子", "假", "派出所", "公安", "打错", "投诉", "查一下",
            "查一查", "不知道", "不了解", "不懂", "不必", "不必了", "算了", "不需要", "用不到", "没", "不考虑", "不清楚", "不了",
            "没兴趣", "不感兴趣", "没时间", "没空", "不用", "不好意思", "不愿意", "不想说", "挂", "先这样", "没有需要", "不用说",
            "不用介绍", "听不清", "听不清楚", "不清楚", "你说啥", "什么", "不需要谢谢", "没", "啥玩意", "不要再打电话", "听不懂人话",
            "烦不烦", "暂时没有", "不要不要", "不要再联系", "啥都没有", "拜拜", "打错", "暂时不", "用不着", "我说我没", "算了", "不可以",
            "没不", "忙", "在忙"};

    public static final String[] MAIN_ANSWER_OTHER = {"说", "做", "做啥", "什么", "嘛", "没", "不", "没听懂",
            "没明白", "听清", "啥事", "说什么", "哪里", "那里", "什么事", "怎么说", "做什么", "做啥", "干啥", "想说啥", "找谁",
            "想说什么", "你们", "你", "要", "找", "我", "做", "干", "什么", "啥", "需", "要", "找", "我", "做", "什么啥",
            "你是谁", "哪一位", "什么嘛", "你们)", "哪位", "什么意思", "人", "事", "东西", "玩意", "电话", "什么情况", "干要做",
            "说讲", "一遍", "呀啊哦噢哟嘞呢", "你是哪里", "你哪里", "你说什么", "再说一遍", "你再说一遍", "你们是哪里的", "什么东西", "再说一次",
            "哪儿的", "什么啊", "哪儿啊", "哪位", "说什么", "你您", "是谁", "哪", "内位", "人找", "懒得", "算了", "我不用", "先不用",
            "骗子吧", "假的", "派出所", "公安", "打错", "投诉", "查一下", "查一查", "不知道", "不了解", "不懂", "不必", "不必了",
            "算了", "不需要", "用不到", "没", "不考虑", "不清楚", "不了", "没兴趣", "不感兴趣", "没时间", "没空", "不用了", "不好意思",
            "不愿意", "不想说", "挂了", "先这样", "没有需要", "不用说了", "不用介绍", "听不清", "听不清楚", "不清楚", "你说啥", "什么",
            "不需要谢谢", "没", "啥玩意", "不要再打电话", "听不懂人话", "烦不烦", "暂时没有", "不要不要", "不要再联系了", "啥都没有", "拜拜",
            "打错了", "暂时不", "用不着", "我说我没", "算了吧", "不可以", "没不"};

    public static final String[] MAIN_ANSWER_AI = {"智能助理", "电话助理", "智能助手", "语音助手", "正在通话中", "请按一重录",
            "暂时无法接听", "无法接听您的电话", "请留言", "您的来电或留言", "滴声后给他留言", "您拨打的用户正在通话中", "呼叫保持", "现在是呼叫",
            "请在嘀声后留言", "请稍等现在是呼叫保持", "您拨打的用户暂时无法接听", "您拨打的用户正忙", "您可以在嘀声后给他留言", "也可以按一号键直接留言",
            "将收取正常的通话费用", "若无需留言请挂机", "不方便", "智能助理", "机主", "帮您转达", "助理", "小艾助理", "小爱助理", "机主的助理",
            "机主的智能助理", "机主的小爱助理", "电话秘书", "机主的电话秘书", "不方便请留言", "我是语音助理", "我是机主的秘书", "您已超时",
            "超时重新录制", "请按一", "您已超时重新录制", "你可以尝试", "想要我为你做什么", "助理小志", "助理小王", "助理小志的", "机主的电话助理",
            "电话助理", "通讯管家", "机主正忙", "无法接听", "在开车", "在开会", "现在不方便", "现在有事", "我信号不好", "在忙", "等会打给你",
            "现在没空", "你等会打过来", "听不清", "信号差", "没空呢", "开车呢", "在开车啊", "开车", "我没空", "忙着", "忙", "我在忙",
            "上班", "打工", "我在上班", "开会", "在开会", "没时间", "我没时间", "会议", "会议中", "在会议", "听不清楚", "忙", "有事",
            "稍后联系"};

    public static final String[] MAIN_ANSWER_REGISTERED = {"登记过", "房东", "房东登记过", "房东搞", "房东弄过",
            "房东已经", "房东登记", "房东登记", "已登记", "已经登记过", "搞过", "搞", "登记好", "已登记", "弄过", "前两天刚搞", "刚搞过",
            "房东帮忙搞", "房东来搞", "房东来弄", "去搞过", "去搞过", "去过", "已经搞"};

    public static final String[] MAIN_ANSWER_N =
            {"没", "不", "不是", "没有", "打错", "不用", "不需要", "不必", "不必", "用不到", "用不着", "暂时没", "木有", "现在还没",
                    "近期没", "没这个规划", "暂时好像没有", "嗯没有", "这个没有", "没有需要", "没有谢谢", "不好意思", "暂时不用", "不对",
                    "不住着", "不住这", "我不住这", "我不住这里", "没有住啊", "不住这里", "没住", "离开", "否"};
    public static final String[] MAIN_ANSWER_Y = {"是", "对", "嗯", "有", "行", "好", "说", "讲", "可以", "奥",
            "嗷啦", "哦尅", "是也", "没错", "不错", "是对", "是", "请讲", "你说", "嗯你说", "怎么", "什么事", "当然", "讲一下",
            "你说", "有", "讲吧", "嗯嗯", "嗯呗", "您说", "港讲", "明白", "晓得", "干嘛", "能啊", "你讲", "你说说看", "什么",
            "请说", "啥", "是住着", "对的住着", "就住着", "就住这", "是住这里", "住这里的", "家里", "在横峰", "在横峰街道", "住横峰",
            "住横峰街道", "我住这的", "对横峰街道"};

    public static final String[] SPECIAL_WORD = {"是", "不是", "没有", "说", "有", "什么", "是不是", "不能"};

    public static final String[] YES = {"是", "对", "嗯", "有", "行", "好", "可以", "奥", "嗷啦", "哦尅", "是也",
            "没错", "是对", "是的", "嗯你说", "当然", "有的", "嗯嗯", "嗯呗", "嗯的", "是的啊", "能啊是住着", "对的住着", "就住着",
            "就住这", "是住这里", "住这里的", "我住这的", "好的", "确实是", "是也", "对", "对对", "对对对", "有的", "对呀", "恩没错",
            "嗯是", "啊对", "嗯嗯对", "对头", "正确", "对的对的", "对就是", "就是", "对是", "呃对"};
    public static final String[] AU_YES = {"对", "就是这个", "我的手机就是", "就这个手机号", "手机号就是", "这个就是", "恩没错",
            "就是这个号码", "是手机号", "啊是我的", "嗯是", "就这个", "哦是我", "这手机号是我的", "手机号码是我", "嗯嗯嗯", "手机号是我的",
            "只要是我的", "号码就是", "电话号码就是", "这个手机是", "哦是这个", "这个手机号码对", "嗯是这个手机号", "啊对", "就这个号码", "是好拜拜",
            "没错", "正确", "对的对的", "对就是", "就是", "就这个手机", "嗯对好", "对就手机", "是我自己的", "对嗯", "对就加这个",
            "对那个手机号", "对的", "啊对", "就那个手机号", "对是这", "对呀", "就我这个", "就是就是", "手机号是我", "对手机号", "嗯嗯对",
            "对头", "对就这手机号", "手机号就是我", "对这个手机号", "就这个电话号码", "是可以收到", "手机号码是我的", "我是用这个手机号", "就这手机号",
            "就本机号码", "就手机号", "就是手机号", "对对对", "就这个电话", "我手机号就是", "这个手机号就是", "我手机号码就是", "就是用这个号",
            "这手机号就是", "这个手机号就行", "是本手机", "本机号码", "是这个号码", "好好好", "就我手机号码", "对是", "呃对", "对是我的",
            "对这个号", "就是这个", "就这个号", "嗯", "有", "行", "好", "可以", "奥", "嗷啦", "哦尅", "嗯你说", "当然", "你说",
            "嗯嗯", "嗯呗", "嗯的", "能啊", "接受", "好的", "行吧", "同意", "授权吧", "授权", "搞呗", "ok", "行行行", "行啊"};

    public static final String[] NO = {"没", "不", "不是", "没有", "打错了", "不用", "不需要", "不必", "不必了", "用不到",
            "用不着", "暂时没", "木有", "现在还没", "近期没", "没这个规划", "暂时好像没有", "没有", "这个没有", "没有需要", "没有谢谢",
            "不好意思", "暂时不用", "不对", "不住着", "不住这", "我不住这", "我不住这里", "没有住啊", "不住这里", "没住", "没住啊", "不对",
            "不是", "不对的", "不对的啊", "没必要", "不搞这个", "不用不用", "不愿意", "不感兴趣", "我不听", "不听了", "不用安排", "不需要",
            "不用", "不考虑", "没考虑", "不需要", "没需求", "不想说", "不想了解", "没有需要", "不用介绍", "不想要", "挂了", "先这样",
            "不要", "不想", "没这个打算", "改天聊", "用不上", "别打", "不了", "不搞", "不做", "不弄", "暂时不用", "不要不要",
            "这个我不要", "不用谢谢", "谢谢不用", "哦不用", "不需要了", "没需要了", "不用了啊", "现在没有", "暂时没有", "没有没有",
            "没有没有没有", "没得没得", "否"};
    public static final String[] AI = {"智能助理", "电话助理", "智能助手", "语音助手", "正在通话中", "请按一重录", "暂时无法接听",
            "无法接听您的电话", "请留言", "您的来电或留言", "滴声后给他留言", "您拨打的用户正在通话中", "呼叫保持", "现在是呼叫", "请在嘀声后留言",
            "请稍等现在是呼叫保持", "您拨打的用户暂时无法接听", "您拨打的用户正忙", "您可以在嘀声后给他留言", "也可以按一号键直接留言", "将收取正常的通话费用",
            "若无需留言请挂机", "不方便", "智能助理", "机主", "帮您转达", "助理", "小艾助理", "小爱助理", "机主的助理", "机主的智能助理",
            "机主的小爱助理", "电话秘书", "机主的电话秘书", "不方便请留言", "我是语音助理", "我是机主的秘书", "您已超时", "超时重新录制", "请按一",
            "您已超时重新录制", "你可以尝试", "想要我为你做什么", "助理小志", "助理小王", "助理小志的", "机主的电话助理", "电话助理", "通讯管家",
            "机主正忙", "无法接听", "在开车", "在开会", "现在不方便", "现在有事", "我信号不好", "在忙", "等会打给你", "现在没空", "你等会打过来",
            "听不清", "信号差", "没空呢", "开车呢", "在开车啊", "开车", "我没空", "忙着呢", "忙啊", "我在忙", "上班呢", "上班啊", "打工",
            "我在上班", "开会", "在开会啊", "在开会", "开会呢", "没时间", "没时间呢", "我没时间", "会议", "会议中", "在会议", "听不清楚啊",
            "忙", "有事有事", "稍后联系正在通话中", "请按一重录", "暂时无法接听", "无法接听您的电话", "请留言", "请挂机", "您的来电或留言",
            "滴声后给他留言", "您拨打的用户正在通话中", "号码不存在", "呼叫保持", "请稍等现在是呼叫", "请在嘀声后留言", "请稍等现在是呼叫保持",
            "您拨打的用户暂时无法接听", "您拨打的用户正忙", "您可以在嘀声后给他留言", "也可以按一号键直接留言留言", "将收取正常的通话费用", "若无需留言请挂机",
            "不方便", "智能助理", "机主", "帮您转达", "助理", "小艾助理", "小爱助理", "智能助手", "机主的助理", "机主的智能助理",
            "机主的小爱助理", "电话秘书", "机主的电话秘书", "不方便请留言", "留言", "把这个要求转达给他的", "我是语音助理", "我是机主的秘书",
            "主人不让我告诉别人他的微信号。", "基础不让我告诉别人他的微信号。", "我可以帮助您转达", "方便问一下你是哪个机构吗", "我会通知机主的", "老板不太用微信",
            "小智慧告诉他", "你可以留言给他"};

    public static final String[] OTHER = {"不懂", "听不懂", "听不清", "不明白", "什么", "甚意思呀", "什么意思", "啥",
            "啥玩意", "什么玩意", "再说", "再说一遍", "再说下", "怎么", "怎么了", "你干啥的"};
    public static final List<Pattern> patterns =
            Lists.newArrayList(Pattern.compile("[\\p{script=Han}\\w]+是[\\p{script=Han}\\w]+"),
                    Pattern.compile("[\\p{script=Han}\\w]+有[\\p{script=Han}\\w]+"),
                    Pattern.compile("[\\p{script=Han}\\w]+不能[\\p{script=Han}\\w]+"));
    public static final String[] SPECIAL_NOT_WORD = {"不好意思", "不方便", "没空", "没时间", "没有听清楚"};

    public static final String FUHAO = "[,.，。把吧哦也啊阿呢了的]";
    public static final String WENLING = "wenling_";
    public static final String WENLING_2ED = WENLING + "2ed_";

    public static final List<Pattern> otherPatterns =
            Arrays.asList(".*有什么事你说.*", ".*不清楚.*", ".*不明白.*", ".*没听懂.*", ".*没听清楚.*",
                    ".*(?<!不)是不是.*", ".*(?<!没)有没有.*", ".*不知道.*", ".*不好意思.*", ".*没时间.*", ".*你是哪个.*",
                    ".*有什么事.*", ".*这是什么.*", ".*喂，你好。.*", ".*那你是谁呀.*", ".*你是谁.*", ".*你们是谁.*",
                    ".*你是说.*", ".*是我听错了.*", ".*是这样吗.*", ".*有事吗.*", ".*是.*还是.*", ".*是.*玩意.*",
                    ".*你们是*", ".*您们是*").stream().map(Pattern::compile).collect(Collectors.toList());

    public static final List<Pattern> yesPatterns =
            Arrays.asList(".*现在打的.*", ".*打的.*", ".*我的电话.*", ".*电话.*", ".*就打.*", ".*这个.*", ".*你看.*",
                    ".*你大的.*", ".*你打.*", ".*你打的.*", ".*这个.*", ".*就这个.*", ".*号码.*", ".*打过来.*",
                    ".*[0-9]{3,}.*").stream().map(Pattern::compile).collect(Collectors.toList());

}
