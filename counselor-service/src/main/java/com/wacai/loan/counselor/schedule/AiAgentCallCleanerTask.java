/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-25 19:02:00
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-25 19:24:23
 * 
 * @Description:
 */
package com.wacai.loan.counselor.schedule;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import com.wacai.loan.counselor.opt.base.state.CallState;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.sip.cache.CallContext;
import com.wacai.loan.counselor.opt.sip.sipsession.AiAgentCall;

@Service
@Slf4j
public class AiAgentCallCleanerTask {

    private static final long IDLE_TIMEOUT_MILLIS = TimeUnit.MINUTES.toMillis(5);

    @Resource
    private CallContext callContext;

    @Scheduled(cron = "0 0/5 8-20 * * ?") // 每 5 分钟执行一次
    public void cleanIdleCalls() {
        long now = System.currentTimeMillis();
        int cleanCount = 0;
        for (Map.Entry<String, AiAgentCall> entry : callContext.getCallMap().entrySet()) {
            AiAgentCall call = entry.getValue();
            CallState callState = CallStateManager.getState(entry.getKey()); // 确保状态被更新

            if (Objects.isNull(call) || Objects.isNull(callState)
                    || callState.getLastAccessTime() == 0) {
                log.warn("[CallCleaner] Call is null or idle time not set, skipping: {}",
                        entry.getKey());
                continue; // 如果 call 为空或没有设置 idle 时间，跳过
            }
            long idle = now - callState.getLastAccessTime();
            if (idle > IDLE_TIMEOUT_MILLIS) {
                String callId = entry.getKey();
                try {
                    call.releaseCall(); // 释放资源
                    cleanCount++;
                    log.info("[CallCleaner] Released idle call: {}, idle={} ms", callId, idle);
                } catch (Exception e) {
                    log.warn("[CallCleaner] Failed to release call: {}", callId, e);
                }
            }
        }
        if (cleanCount > 0) {
            log.info("[CallCleaner] Cleaned {} idle RobotCall(s).", cleanCount);
        }
    }

}
