/*
 * @Author: shunhua
 * @Date: 2025-05-01 10:56:17
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-22 11:24:09
 * @Description: 
 */
package com.wacai.loan.counselor.opt.base.constant;

import java.util.Arrays;

/**
 * 
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-12-18 13:49
 */
public enum EventCallStatusEnums {

    DIAL_ANSWE_ORIGINATE,
    DIAL_ANSWE_RING,
    DIAL_RING_RING,
    DIAL_ANSWE_ANSWER,
    DIAL_REJECT_HANGUP,
    DIAL_SUCC_HANGUP,
    DIAL_UNKNOW;

    public enum DailRecordStatusEnums {

        DIAL_ANSWE,
        DIAL_SUCC,
        DIAL_REJECT,
        DIAL_FAIL,
        DIAL_RING,
        DIAL_ORGN,
        SEND_SUCC,
        LOSS;
    }

    public enum EventStatusEnums {

        ORIGINATE, RING, ANSWER, HAN<PERSON><PERSON>, FAILURE, LOSS, DIAL_FAIL;
    }

    public  static  EventCallStatusEnums of(DailRecordStatusEnums statusEnums, EventStatusEnums eventStatusEnums) {
        String name = statusEnums.name() + "_" + eventStatusEnums.name();
        return Arrays.stream(EventCallStatusEnums.values()).filter(x -> x.name().equalsIgnoreCase(name)).findFirst()
            .orElse(DIAL_UNKNOW);
    }
}


