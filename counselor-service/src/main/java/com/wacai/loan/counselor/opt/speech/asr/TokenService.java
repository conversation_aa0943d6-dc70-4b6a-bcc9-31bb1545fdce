/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-28 15:23:15
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-28 15:23:17
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.speech.asr;

import java.util.concurrent.TimeUnit;
import com.alibaba.nls.client.AccessToken;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TokenService {

    private String accessKey;

    private String accessSecret;

    private volatile AccessToken accessToken;

    public TokenService(String accessKey, String accessSecret) {
        this.accessKey = accessKey;
        this.accessSecret = accessSecret;
    }

    public String getToken() {
        if (isTokenExpired()) {
            accessToken = getAccessToken();
        }
        return accessToken.getToken();
    }

    public boolean isTokenExpired() {
        if (accessToken == null) {
            return true;
        }
        final long expireTime = accessToken.getExpireTime() * 1000;
        // expire after 1h
        if (expireTime - System.currentTimeMillis() < TimeUnit.HOURS.toMillis(1)) {
            log.info("AliYun access token [{}][{}] expired.", accessToken, expireTime);
            return true;
        }
        return false;
    }

    public String getRefreshedToken() {
        if (isTokenExpired()) {
            log.debug("AliYun Access token [{}] expired, get new.", accessToken);
            accessToken = getAccessToken();
            return accessToken.getToken();
        }
        return null;
    }

    private AccessToken getAccessToken() {
        try {
            final AccessToken accessToken = new AccessToken(accessKey, accessSecret);
            accessToken.apply();
            log.debug("AliYun access token [{}] got.", accessToken);
            return accessToken;
        } catch (Exception e) {
            log.error("Get AliYun access token error caused.", e);
            throw new RuntimeException(e);
        }
    }
}
