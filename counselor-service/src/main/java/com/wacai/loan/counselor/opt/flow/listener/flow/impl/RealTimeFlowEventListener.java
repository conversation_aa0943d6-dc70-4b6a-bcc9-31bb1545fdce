/*
 * @Author: shunhua
 * @Date: 2025-07-22 17:25:50
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-22 19:23:21
 * @Description: 实时语音流 realtime flow event listener
 */
package com.wacai.loan.counselor.opt.flow.listener.flow.impl;

import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.opt.base.constant.AiFlowTypeEnums;
import com.wacai.loan.counselor.opt.base.dto.DialEventDTO;
import com.wacai.loan.counselor.opt.flow.listener.flow.AbstractFlowEventListener;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class RealTimeFlowEventListener extends AbstractFlowEventListener  {

    @Override
    protected AiFlowTypeEnums getSupportedFlowType() {
        return AiFlowTypeEnums.REALTIME;
    }

   
    @Override
    protected void handleRingEvent(DialEventDTO eventDTO) {
        
    }

    @Override
    protected void handleAnswerEvent(DialEventDTO eventDTO) {
         
    }

    @Override
    protected void handleHangupEvent(DialEventDTO eventDTO) {
        
    }

}
