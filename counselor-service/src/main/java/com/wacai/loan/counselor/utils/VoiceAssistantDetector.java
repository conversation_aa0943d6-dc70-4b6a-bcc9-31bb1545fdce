/*
 * @Author: shunhua
 * @Date: 2025-06-17 15:33:32
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-25 10:48:55
 * @Description:  语音助手匹配
 */
package com.wacai.loan.counselor.utils;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

public class VoiceAssistantDetector {

    private static final List<String> KEYWORDS = Arrays.asList("拨打", "电话", "暂时", "无法", "接通",
            "停机", "欠费", "关机", "语音", "助手", "服务", "通话", "留言", "录音", "提示","提示音","温馨提示");

    // 所有语音助理相关的正则规则（忽略大小写）
    private static final List<Pattern> ASSISTANT_PATTERNS = Arrays.asList(
            Pattern.compile(
                    "(?i)((您好|联系的用户|您拨打的电话).*?(电话|用户)?.*?(暂时|当前|无法|停机|欠费|关机)(.*?接通)?)"),
                    Pattern.compile("(?i).*(正在通话中|暂时无法接听|正忙|无法接听|无人接听|通话中|正在通话).*"),
            Pattern.compile(
                    "(?i).*(助理(小王|小志|小李)?|小(艾|爱|秘|助)|秘书|(AI|智能|电话|语音)?助手|语音服务|电话助理|通讯管家|机主|守护者|录音).*"),
            Pattern.compile("(?i).*((语音)?留(言)?|请(按一重录|留言)|滴声后留言|您所拨打的电话暂时无法接通).*"),
                            Pattern.compile(
                    "(?i)(请按\\s*(一|二|三|四|五|1|2|3|4|5)(号)?[,、]?(.*?请按\\s*(一|二|三|四|五|6|7|8|9|1|2|3|4|5)))"),
            Pattern.compile(
                    "(?i).*(语音服务|系统自动回复|语音提示|提示音|AI语音|确认.*接听|方便接听).*"),
            // ✅ 增强：对ASR不完整的内容有更高容错
            Pattern.compile("(?i).*您拨打的电话.*(无法|暂停|暂).*"), Pattern.compile("(?i).*语音.*(留言|助手|接听).*"),
            Pattern.compile("(?i).*欠费|停机|关机|不在服务区.*"), Pattern.compile("(?i).*(请留言|按一重录|转语音|滴声后).*")

    );

    /**
     * 判断给定文本是否为语音助理或语音提示内容
     *
     * @param text 客户的文本内容（已转写）
     * @return true 表示可能是语音助理/机器人自动回复，false 表示正常人工语音
     */
    /**
     * 判断文本是否为系统语音助手或语音留言提示
     */
    public static boolean isLikelyAssistantReply(String asrText) {
        if (asrText == null || asrText.trim().isEmpty())
            return false;

        // 正则强匹配
        for (Pattern pattern : ASSISTANT_PATTERNS) {
            if (pattern.matcher(asrText).find()) {
                return true;
            }
        }

        // 容错关键词匹配
        String lower = asrText.toLowerCase();
        long keywordMatches =
                KEYWORDS.stream().filter(k -> lower.contains(k.toLowerCase())).count();

        return keywordMatches >= 1;
    }

    public static void main(String[] args) {
        System.out.println(isLikelyAssistantReply("您拨打的用户。"));
    }
}
