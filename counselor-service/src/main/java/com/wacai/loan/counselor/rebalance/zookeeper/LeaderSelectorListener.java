package com.wacai.loan.counselor.rebalance.zookeeper;

import com.wacai.loan.counselor.rebalance.AgentInstanceManager;

import java.io.Closeable;
import java.util.concurrent.CountDownLatch;

import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.leader.LeaderSelector;
import org.apache.curator.framework.recipes.leader.LeaderSelectorListenerAdapter;
import org.apache.curator.framework.state.ConnectionState;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LeaderSelectorListener extends LeaderSelectorListenerAdapter implements Closeable {
    private final String name;
    private final LeaderSelector leaderSelector;

    // 用于控制takeLeadership方法不返回（一直阻塞）
    private static final CountDownLatch LATCH = new CountDownLatch(1);

    private AgentInstanceManager agentInstanceManager;

    public LeaderSelectorListener(CuratorFramework curatorFramework, String path, String name,
        AgentInstanceManager agentInstanceManager) {
        this.name = name;
        // 所有节点选举必须是同一个path
        leaderSelector = new LeaderSelector(curatorFramework, path, this);
        // 设置leaderSelector的存储Id
        leaderSelector.setId(name);
        // 放弃领导权时重新排队
        leaderSelector.autoRequeue();
        this.agentInstanceManager = agentInstanceManager;
    }

    public void start() throws Exception {
        // 异步的
        leaderSelector.start();
    }

    @Override
    public void close() {
        leaderSelector.close();
    }

    @Override
    public void stateChanged(CuratorFramework client, ConnectionState newState) {
        if (newState == ConnectionState.LOST || newState == ConnectionState.SUSPENDED) {
                log.warn("Leader State : {}",newState);
        }
    }

    @Override
    public void takeLeadership(CuratorFramework client) throws Exception {
        log.info("【{}】 成为Leader", name);
        agentInstanceManager.leaderAllot();
        // 控制该方法不返回，如果返回则释放了Leader, 不管你用什么代码实现，只要方法不返回，该leader就不会释放。 特别重要一定要注意！！！
        LATCH.await();
    }
}