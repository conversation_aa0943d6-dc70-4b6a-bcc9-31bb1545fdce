/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-06 19:37:42
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-22 15:40:16
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.handler;

import java.util.Objects;
import javax.annotation.Resource;
import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.expression.Expression;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.SpelCompilerMode;
import org.springframework.expression.spel.SpelParserConfiguration;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.dao.po.AiFlowNode;
import com.wacai.loan.counselor.opt.ai.service.OpenAIFacadeService;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowRepeatTimesLimitConfig;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowTransferLimitConfig;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowWaitVoiceConfig;
import com.wacai.loan.counselor.opt.flow.core.WorkFlowNode;
import com.wacai.loan.counselor.opt.flow.handler.pattern.NextNodeCodeAdapter;
import com.wacai.loan.counselor.opt.flow.handler.rule.Router;
import com.wacai.loan.counselor.opt.flow.model.AiFlowBO;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import jdk.nashorn.api.scripting.NashornScriptEngineFactory;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class NodeConverter {

    @Resource
    private OpenAIFacadeService openAIFacadeService;

    public WorkFlowNode fromAiFlowNode(AiFlowNode aiFlowNode) {
        WorkflowNodeConfig config = WorkflowNodeConfig.builder().workNodeCode(aiFlowNode.getCode())
                .tip(aiFlowNode.getTip())
                .voicePath(aiFlowNode.getVoicePath()).triggerInput(aiFlowNode.getTriggerInput())
                .end(aiFlowNode.getEnd()).tipExpression(covertExpression(aiFlowNode.getTip()))
                .invocable(covertInvocable(aiFlowNode.getScript()))
                .receiveSimpleDtmf(aiFlowNode.getReceiveSimpleDtmf())
                .voiceJsonConfig(BusinessParamHandler.buildVoiceConfig(aiFlowNode.getRemoteVoice()))
                .semanticsTransform(BusinessParamHandler
                        .buildSemanticsTransform(aiFlowNode.getSemanticsTransform()))
                .router(Router.buildTransferRule(aiFlowNode.getExtInfo(), openAIFacadeService))
                .waitVoiceCfg(WorkFlowWaitVoiceConfig.builderWaitVoice(aiFlowNode.getContext()))
                .repeatTimesLimitConfig(WorkFlowRepeatTimesLimitConfig
                        .builderRepeatTimesLimitConfig(aiFlowNode.getContext()))
                .nextNodeCodeAdapters(NextNodeCodeAdapter
                        .builderNextNodeAdapters(aiFlowNode.getNextNodeCodeAdapters()))
                .transferLimitConfig(WorkFlowTransferLimitConfig
                        .builderTransferLimitConfig(aiFlowNode.getContext(),aiFlowNode.getTransferQueueCode()))
                .build();
        return new WorkFlowNode(config);
    }


    public void fromAiFlowFillNode(AiFlowBO aiFlowBO, WorkFlowNode workFlowNode) {
        WorkFlowWaitVoiceConfig config = workFlowNode.getWorkflowNodeConfig().getWaitVoiceCfg();
        if (Objects.isNull(config)) {
            workFlowNode.getWorkflowNodeConfig().setWaitVoiceCfg(aiFlowBO.getWaitVoiceConfig());
        }
        WorkFlowRepeatTimesLimitConfig repeatTimesLimitConfig =
                workFlowNode.getWorkflowNodeConfig().getRepeatTimesLimitConfig();
        if (Objects.isNull(repeatTimesLimitConfig)) {
            workFlowNode.getWorkflowNodeConfig()
                    .setRepeatTimesLimitConfig(aiFlowBO.getRepeatTimesLimitConfig());
        }
        workFlowNode.getWorkflowNodeConfig().setTenantCode(aiFlowBO.getAiFlow().getTenantCode());
    }

    @SneakyThrows
    @SuppressWarnings("restriction")
    private Invocable covertInvocable(String script) {
        if (StringUtils.isEmpty(script)) {
            return null;
        }
        ScriptEngine engine = new NashornScriptEngineFactory()
                .getScriptEngine(WorkflowNodeConfig.getCtxtLoader());
        try {
            engine.eval(script);
            Invocable invocable = (Invocable) engine;
            return invocable;
        } catch (ScriptException e) {
            log.error("initInvocable err {}", script, e);
            throw e;
        }
    }

    private Expression covertExpression(String tip) {
        SpelParserConfiguration config = new SpelParserConfiguration(SpelCompilerMode.IMMEDIATE,
                WorkflowNodeConfig.getCtxtLoader());
        SpelExpressionParser parser = new SpelExpressionParser(config);
        return parser.parseExpression(tip, new TemplateParserContext());
    }

}
