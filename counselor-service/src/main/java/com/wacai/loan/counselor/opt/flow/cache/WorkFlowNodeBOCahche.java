/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-06 18:54:03
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-08 14:41:33
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.cache;

import java.util.Objects;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.dao.po.AiFlowNode;
import com.wacai.loan.counselor.opt.flow.core.WorkFlowNode;
import com.wacai.loan.counselor.opt.flow.handler.NodeConverter;
import com.wacai.loan.counselor.opt.flow.manager.AiFlowNodeManager;
import com.wacai.loan.counselor.opt.flow.model.AiFlowBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class WorkFlowNodeBOCahche {


  private final AiFlowNodeManager aiFlowNodeManager;


  private final NodeConverter nodeConverter;


  @Cacheable(value = "workflowNodes", key = "#flowNodeCode")
  public WorkFlowNode getNodeByCode(String flowNodeCode) {
    AiFlowNode aiFlowNode = aiFlowNodeManager.getFlowNodeByCode(flowNodeCode);
    if (Objects.isNull(aiFlowNode)) {
      return null;
    }
    return nodeConverter.fromAiFlowNode(aiFlowNode);
  }

  public void fromAiFlowFillNode(AiFlowBO aiFlowBO, WorkFlowNode workFlowNode) {
    nodeConverter.fromAiFlowFillNode(aiFlowBO, workFlowNode);
  }
}
