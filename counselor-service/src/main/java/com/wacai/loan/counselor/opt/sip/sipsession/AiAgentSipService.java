/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-30 14:43:59
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-31 17:06:29
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.sip.sipsession;

import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.pjsip.pjsua2.AccountConfig;
import org.pjsip.pjsua2.AccountRegConfig;
import org.pjsip.pjsua2.AccountSipConfig;
import org.pjsip.pjsua2.AuthCredInfo;
import org.pjsip.pjsua2.AuthCredInfoVector;
import org.pjsip.pjsua2.CodecInfo;
import org.pjsip.pjsua2.Endpoint;
import org.pjsip.pjsua2.EpConfig;
import org.pjsip.pjsua2.SipHeader;
import org.pjsip.pjsua2.TransportConfig;
import org.pjsip.pjsua2.UaConfig;
import org.pjsip.pjsua2.pjsip_transport_type_e;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;
import com.google.common.collect.Maps;
import com.wacai.loan.counselor.opt.base.threadpool.ExecutorServiceHandlerRegistry;
import com.wacai.loan.counselor.opt.sip.service.EndpointService;
import com.wacai.loan.yo.pjsua2.Pjsua2ShareLibLoader;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AiAgentSipService
        implements ApplicationListener<ApplicationReadyEvent>, DisposableBean {


    @Value("${pjsip.codecs:G729/8000/1,PCMA/8000/1}")
    private String codecs;

    @Resource
    private UaConfig uaConfig;
    @Resource
    private EpConfig endpointConfig;

    @Resource
    private TransportConfig transportConfig;

    @Resource
    private Endpoint endpoint;

    @Resource
    private EndpointService endpointService;

    // 监控活跃任务
    private static final AtomicLong activeSipTasks = new AtomicLong(0);

    private final AtomicBoolean stop = new AtomicBoolean(false);


    private ExecutorService taskExecutorService =
            ExecutorServiceHandlerRegistry.getExecutorServiceHandler(5, "sip-task");

    private ExecutorService sipExecutorService =
            ExecutorServiceHandlerRegistry.getExecutorServiceHandler(1, "pjsua2");


    @PostConstruct
    public void init() {
        sipExecutorService.execute(() -> {
            try {
                activeSipTasks.incrementAndGet();
                log.info("Init SIP endpoint, account.");
                endpoint.libCreate();
                endpoint.libInit(endpointConfig);
                setupCodecs();
                endpoint.transportCreate(pjsip_transport_type_e.PJSIP_TRANSPORT_UDP,
                        transportConfig);
                endpoint.libStart();
                log.info("SIP endpoint [{}] started [{}].", endpoint.libVersion().getFull(),
                        endpoint.libGetState());
                endpoint.audDevManager().setNullDev();
            } catch (Exception e) {
                log.error("Init PJSUA2 lib error", e);
                throw new IllegalStateException("Init PJSUA2 lib error", e);
            } finally {
                activeSipTasks.decrementAndGet();
            }
            log.info("SIP endpoint init complete.");
            runEventLoop();
        });
    }


    private void runEventLoop() {
        while (!stop.get()) {
            try {
                endpoint.libHandleEvents(10L);
                Thread.sleep(50L);
            } catch (Exception e) {
                log.error("Fetch PJSUA2 event error", e);
            }
        }
        log.info("PJSUA2 event loop exited.");
    }


    @SneakyThrows
    public void manualRegister(List<AiAgentAccount> accounts) {
        executePjsua2Task(() -> {
            if (CollectionUtils.isNotEmpty(accounts)) {
                try {
                    accounts.forEach(account -> {
                        try {
                            if (account.isValid()) {
                                account.setRegistration(Boolean.TRUE);
                            } else {
                                log.warn("Account: {}  is not valid, create.",
                                        account.getUsername());
                                registerPjsua2(Lists.newArrayList(account));
                            }
                        } catch (Exception e) {
                            log.error("Account: {}  manualRegister Exception:",
                                    account.getUsername());
                        }
                    });
                } catch (Exception e) {
                    log.error("Accounts: {}  manualRegister Exception:", accounts.stream()
                            .map(x -> x.getUsername()).collect(Collectors.joining(",")));
                }
            }
        });
    }

    public void registerPjsua2(final List<AiAgentAccount> accounts) {
        executePjsua2Task(() -> {
            accounts.forEach(account -> {
                try {

                    activeSipTasks.incrementAndGet();
                    String accRealm = account.getRealm();
                    String sipHost = account.getSiphost();
                    if (StringUtils.isBlank(accRealm)) {
                        accRealm = sipHost;
                    }
                    final String username = account.getUsername();
                    final String accountUri = "sip:" + username + "@" + accRealm;
                    log.info("Register account [{}].", accountUri);
                    final AccountConfig accountConfig = new AccountConfig();

                    accountConfig.setIdUri(accountUri);
                    final AccountRegConfig regConfig = accountConfig.getRegConfig();

                    final String password = account.getPassword();
                    if (StringUtils.isNotBlank(password)) {
                        final AuthCredInfo cred = new AuthCredInfo();
                        cred.setUsername(username);
                        cred.setDataType(0);
                        cred.setData(password);
                        final AccountSipConfig sipConfig = accountConfig.getSipConfig();
                        AuthCredInfoVector authCreds = sipConfig.getAuthCreds();
                        authCreds.clear();
                        authCreds.add(cred);
                    }
                    regConfig.setRegistrarUri("sip:" + sipHost);

                    SipHeader sipHeaderUserTenant = new SipHeader();
                    sipHeaderUserTenant.setHName("user-tenant");
                    sipHeaderUserTenant.setHValue(account.getSource());
                    regConfig.getHeaders().add(sipHeaderUserTenant);

                    regConfig.setTimeoutSec(580 + new Random().nextInt(10) * 6);
                    regConfig.setRetryIntervalSec(120);

                    accountConfig.setRegConfig(regConfig);
                    account.create(accountConfig);
                    log.info("Account [{}] registered Success.", accountUri);
                } catch (Exception e) {
                    log.error("Account registration failed: {}", account.getUsername(), e);
                } finally {
                    activeSipTasks.decrementAndGet();
                }
            });
        });
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        log.info("RobotServiceImpl initialized on application ready.");
    }



    @SneakyThrows
    private void setupCodecs() {
        final Map<String, Integer> mapCodecs = Maps.newHashMap();
        final int[] priority = {136};

        Stream.of(codecs.split("\\s*[,;]\\s*"))
                .forEach(codec -> mapCodecs.put(codec, priority[0]--));

        for (CodecInfo codecInfo : endpoint.codecEnum2()) {
            final String codecId = codecInfo.getCodecId();
            final Integer pr = mapCodecs.get(codecId);
            short shortPr = pr != null ? pr.shortValue() : 0;
            log.info("Set codec [{}] priority [{}] to [{}].", codecId, codecInfo.getPriority(),
                    shortPr);
            endpoint.codecSetPriority(codecId, shortPr);
        }
    }

    @PreDestroy
    @Override
    public void destroy() {
        stop.set(true);
        try {
            log.info("Destroy PJSUA2 lib.");
            endpointService.libRegisterThread("runTask Thread");
            endpoint.libDestroy();
        } catch (Exception e) {
            log.error("Destroy PJSUA2 lib error caused.", e);
        } finally {
            endpoint.delete();
            transportConfig.delete();
            endpointConfig.delete();
            uaConfig.delete();
            Pjsua2ShareLibLoader.offload();
            log.info("PJSUA2 resources released.");
        }
        log.info("RobotServiceImpl shutdown complete. Active SIP tasks: {}", activeSipTasks.get());
    }


    public void executePjsua2Task(Runnable task) {
        taskExecutorService.execute(() -> {
            try {
                endpointService.libRegisterThread("runTask Thread");
                task.run();
            } catch (Exception e) {
                log.error("Execute PJSUA2 task error", e);
            }
        });
    }
}
