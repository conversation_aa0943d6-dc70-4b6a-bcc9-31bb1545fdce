/*
 * @Author: shunhua
 * 
 * @Date: 2025-04-25 11:16:57
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-31 14:19:19
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.base.cache;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.base.util.SafeUrlDecoder;
import com.wacai.loan.counselor.utils.Utils;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/24 14:58
 */

@Slf4j
public class CallUserDataCache {

    private final static Cache<String, JSONObject> callUserDataCacheInner =
            CacheBuilder.newBuilder().expireAfterAccess(1, TimeUnit.HOURS).build();


    public static void cache(String callId, JSONObject userObject) {
        callUserDataCacheInner.put(callId, userObject);
        CallStateManager.getState(callId).putAll(Utils.flattenJson(userObject));
    }

    public static JSONObject getCache(String callId) {
        return callUserDataCacheInner.getIfPresent(callId);
    }

    public static JSONObject handle(String userData) {

        String userDataDecode = userData;
        if (SafeUrlDecoder.isUrlEncoded(userData)) {
            try {
                userDataDecode = URLDecoder.decode(userData, "utf-8");
            } catch (UnsupportedEncodingException e) {
            }
        }
        return JSON.parseObject(userDataDecode);
    }

    /**
     * 从 UserData 解析远程录音配置
     * 
     * @param userData
     * @return
     */
    public static Object getRemoteVoiceParam(JSONObject userData) {
        if (userData.containsKey("remoteVoiceParam")) {
            return userData.get("remoteVoiceParam");
        } else {
            JSONObject wrapped = new JSONObject();
            wrapped.put("jsonData", userData);
            return wrapped;
        }
    }

    public static String getTranferAgent(String callId) {
        JSONObject userData = callUserDataCacheInner.getIfPresent(callId);
        if (Objects.nonNull(userData)) {
            return userData.getString("transferAgent");
        }
        return StringUtils.EMPTY;
    }

    public static String getTranferQueueCode(String callId) {
        JSONObject userData = callUserDataCacheInner.getIfPresent(callId);
        if (Objects.nonNull(userData)) {
            return userData.getString("transferQueue");
        }
        return StringUtils.EMPTY;
    }

    /**
     * 历史遗留 script 使用
     * 
     * @param callId
     * @return
     */
    @Deprecated
    public  JSONObject acquireUserData(String callId) {
        return callUserDataCacheInner.getIfPresent(callId);
    }
}
