/*
 * @Author: shunhua
 * @Date: 2025-05-03 14:58:29
 * @LastEditors: shunhua
 * @LastEditTime: 2025-05-04 15:01:07
 * @Description: 
 */
package com.wacai.loan.counselor.opt.sip.config;

import java.util.HashMap;
import java.util.Map;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import lombok.Data;

/**
 * 
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-07-16 17:59
 */
@Data
@Component
@ConfigurationProperties(prefix = "counselor.inbound")
public class InboundConfig {

    private Map<String, String> flow = new HashMap<>();
}
