package com.wacai.loan.counselor.utils;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * 识别声音过滤器
 * 用于判断有没有声音，甚至可以判断声音的响度
 *
 * <AUTHOR>
 */
public class DetectSoundFilter {

    /**
     * 声音响度阈值
     */
    public static  double soundThreshold = -40; // db

    /**
     * 是否安静
     *
     * @param buffer
     * @param
     * @return
     */
    private static  boolean isSilence(final double[] buffer) {
        double currentSPL = soundPressureLevel(buffer);
        return currentSPL < soundThreshold;
    }

    /**
     * 声压计算
     *
     * @param buffer
     * @return
     */
    private static  double soundPressureLevel(final double[] buffer) {
        double value = Math.pow(localEnergy(buffer), 0.5);
        value = value / buffer.length;
        return linearToDecibel(value);
    }

    /**
     * 线性转换为dB值
     *
     * @param value
     * @return
     */
    private static  double linearToDecibel(final double value) {
        return 20.0 * Math.log10(value);
    }

    /**
     * 音频帧去负数
     *
     * @param buffer
     * @return
     */
    private static  double localEnergy(final double[] buffer) {
        double power = 0.0D;
        for (double element : buffer) {
            power += element * element;
        }
        return power;
    }

    public static Boolean isSilence(byte[] frame) {
         double[] byteToDouble = byteToDouble(frame);
        return  isSilence(byteToDouble);
    }

    private static double[] byteToDouble(byte[] data) {
        ByteBuffer buf = ByteBuffer.wrap(data);

        buf.order(ByteOrder.BIG_ENDIAN);
        int i = 0;
        double[] dData = new double[data.length / 2];

        while (buf.remaining() > 1) {
            short s = buf.getShort();

            dData[i] = (double) s / 32767.0; // real
            ++i;
        }
        return dData;
    }
}

