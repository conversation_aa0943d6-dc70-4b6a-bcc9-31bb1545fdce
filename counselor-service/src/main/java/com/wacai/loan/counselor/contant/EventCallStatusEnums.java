/*
 * @Author: shunhua
 * @Date: 2025-04-25 11:16:57
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-22 19:22:57
 * @Description: 
 */
package com.wacai.loan.counselor.contant;

import java.util.Arrays;

/**
 * 事件呼叫状态枚举类
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-12-18 13:49
 */
public enum EventCallStatusEnums {

    DIAL_ANSWE_RING,
    DIAL_RING_RING,
    DIAL_ANSWE_ANSWER,
    DIAL_REJECT_HANGUP,
    DIAL_SUCC_HANGUP,
    DIAL_UNKNOW;

    public enum DailRecordStatusEnums {

        DIAL_ANSWE,
        DIAL_SUCC,
        DIAL_REJECT,
        DIAL_FAIL,
        DIAL_RING,
        DIAL_ORGN,
        SEND_SUCC,
        LOSS;
    }

    public enum EventStatusEnums {

        ORIGINATE, RING, ANSWER, <PERSON>ANG<PERSON>, FAILURE, LOSS, DIAL_FAIL;
    }

    public  static  EventCallStatusEnums of(DailRecordStatusEnums statusEnums, EventStatusEnums eventStatusEnums) {
        String name = statusEnums.name() + "_" + eventStatusEnums.name();
        return Arrays.stream(EventCallStatusEnums.values()).filter(x -> x.name().equalsIgnoreCase(name)).findFirst()
            .orElse(DIAL_UNKNOW);
    }
}


