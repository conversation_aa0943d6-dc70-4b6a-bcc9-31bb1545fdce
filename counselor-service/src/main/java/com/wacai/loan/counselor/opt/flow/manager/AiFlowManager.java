/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-04 18:51:06
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-01 14:14:23
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.manager;

import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.dao.po.AiFlow;
import com.wacai.loan.counselor.dao.repository.AiFlowMapper;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class AiFlowManager {

    @Resource
    private AiFlowMapper aiFlowMapper;

    public AiFlow getAiFlowByCode(String flowCode) {
        AiFlow aiFlow = new AiFlow();
        aiFlow.setReleased(true);
        aiFlow.setCode(flowCode);
        return aiFlowMapper.selectOne(aiFlow);
    }

    public AiFlow findAiFlowByCampCode(String campCode) {
        try {
            AiFlow aiFlow = new AiFlow();
            aiFlow.setReleased(true);
            aiFlow.setCampaignCode(campCode);
            return aiFlowMapper.selectOne(aiFlow);
        } catch (Exception e) {
            log.error("campCode: {} ,err: {}", campCode, e.getMessage());
            return null;
        }
    }

    public List<AiFlow> list(Boolean released) {
        AiFlow aiFlow = new AiFlow();
        aiFlow.setReleased(released);
        return aiFlowMapper.select(aiFlow);
    }
}
