/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-15 17:48:52
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-25 17:47:31
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.service.impl;

import java.util.Objects;
import javax.annotation.Resource;
import javax.script.Invocable;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.flow.handler.limit.WorkFlowNodeRepeatTimesLimitHandler;
import com.wacai.loan.counselor.opt.flow.handler.rule.Router;
import com.wacai.loan.counselor.opt.flow.model.NodeAdapterDTO;
import com.wacai.loan.counselor.opt.flow.model.RouterResponse;
import com.wacai.loan.counselor.opt.flow.model.ScriptContextDTO;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import com.wacai.loan.counselor.opt.flow.service.NodeProcessor;
import com.wacai.loan.counselor.opt.sip.cache.CallContext;
import lombok.extern.slf4j.Slf4j;


@Service
@Slf4j
public class NodeProcessorServiceImpl implements NodeProcessor {

    @Resource
    private WorkFlowNodeRepeatTimesLimitHandler workFlowNodeRepeatTimesLimitHandler;

    @Resource
    private CallContext callContext;

     @Override
    public NodeAdapterDTO adapterTranferFlowNode(String callId,
            WorkflowNodeConfig workflowNodeConfig, String text) {

        Router transferTypeRule = workflowNodeConfig.getRouter();
        String mobile = CallStateManager.getMobile(callId);
        RouterResponse response =
                transferTypeRule.actionRouter(callId, workflowNodeConfig, mobile, text);
        return new NodeAdapterDTO(response.getResult(), response.getFlowNodeCode());
    }

    @Override
    public NodeAdapterDTO adapterScriptFlowNode(String callId,
            WorkflowNodeConfig workflowNodeConfig, String text) {
        try {
            Invocable invocable = workflowNodeConfig.getInvocable();
            String mobile = CallStateManager.getMobile(callId);
            ScriptContextDTO contextDTO = new ScriptContextDTO(callId,text, mobile);
            Object funcResult =
                    invocable.invokeFunction("acqSuitTipFlowCode", text, mobile, contextDTO, null);

            log.info("callId {} adapterScriptFlowNode result {}", callId, funcResult);

            if (Objects.nonNull(workflowNodeConfig.getSemanticsTransform())
                    && workflowNodeConfig.getSemanticsTransform().isEmpty()) {
                return new NodeAdapterDTO(funcResult.toString(),
                        workflowNodeConfig.getSemanticsTransform().get(funcResult.toString()));
            }
            return new NodeAdapterDTO(funcResult.toString(), funcResult.toString());
        } catch (Exception e) {
            log.error("callId {} acqSuitTipFlowCode err", callId, e);
        }
        return null;
    }

    @Override
    public Boolean flowNodeRepeatTimesLimit(String callId, WorkflowNodeConfig workflowNodeConfig) {
        if (Objects.isNull(workflowNodeConfig.getRepeatTimesLimitConfig())) {
            return true;
        }
        return workFlowNodeRepeatTimesLimitHandler.executeLimit(callId,
                workflowNodeConfig.getRepeatTimesLimitConfig(), workflowNodeConfig.getWorkNodeCode());
    }
}
