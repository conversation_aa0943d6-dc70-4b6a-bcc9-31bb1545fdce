/*
 * @Author: shunhua
 * @Date: 2024-12-20 19:27:27
 * @LastEditors: shunhua
 * @LastEditTime: 2025-04-09 11:18:53
 * @Description: 
 */
package com.wacai.loan.counselor.utils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-08-30 14:33
 */
public class VoiceUtils {

    
    private final static Map<String, AtomicInteger> voiceCountMap = new ConcurrentHashMap<>();

    //支持同一个流程配置多个语音，按顺序播放
    public static String getVoicePath(String callUUid, String voicePath) {

        if (StringUtils.isBlank(voicePath)) {
            return null;
        }

        List<String> voicePaths = Arrays.asList(voicePath.split(","));
        if (voicePaths.size() == 1) {
            return voicePath;
        } else {
            AtomicInteger count = voiceCountMap.computeIfAbsent(callUUid, k -> new AtomicInteger(0));
            int index = count.getAndIncrement() % voicePaths.size();
            return voicePaths.get(index);
        }
    }

    public static void clear(String callUUId) {
        voiceCountMap.remove(callUUId);
    }
}
