package com.wacai.loan.counselor.opt.sip.service.impl;

import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson2.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wacai.loan.counselor.opt.base.constant.EventCallStatusEnums;
import com.wacai.loan.counselor.opt.base.dto.DialEventDTO;
import com.wacai.loan.counselor.opt.base.event.SipEvent;
import com.wacai.loan.counselor.opt.flow.handler.AiFlowFactory;
import com.wacai.loan.counselor.opt.sip.cache.CallContext;
import com.wacai.loan.counselor.opt.sip.config.InboundConfig;
import com.wacai.loan.counselor.opt.sip.service.SipEventService;
import lombok.extern.slf4j.Slf4j;

/**
 * SIP EVNET Service
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-12-18 14:00
 */
@Service
@Slf4j
public class SIPEventServiceImpl implements SipEventService {

    @Resource
    private InboundConfig inboundConfig;

    @Resource
    private CallContext callContext;

    @Resource
    private AiFlowFactory aiFlowFactory;


    @Resource
    private ApplicationEventPublisher eventPublisher;


    /**
     * 事件缓存，用于防止重复处理
     */
    private static final Cache<String, Boolean> ringHandledCache =
            CacheBuilder.newBuilder().expireAfterAccess(30, TimeUnit.MINUTES).build();

    @Override
    public void handler(DialEventDTO eventDTO) {
        try {
            // 预处理消息
            if (!preProcessMessage(eventDTO)) {
                return;
            }
            publishEvent(eventDTO);
        } catch (Exception e) {
            log.error("Dial Event Flow failed :{}", JSON.toJSONString(eventDTO), e);
        }
    }



    private void publishEvent(DialEventDTO eventDTO) {
        EventCallStatusEnums callStatusEnums =
                EventCallStatusEnums.of(eventDTO.getCallStatus(), eventDTO.getEventName());
        switch (callStatusEnums) {
            case DIAL_ANSWE_ORIGINATE:
            case DIAL_ANSWE_RING:
                handleRingOnce(eventDTO);
                break;
            case DIAL_ANSWE_ANSWER:
                log.debug("发布应答事件: {}", eventDTO.getUuid());
                eventPublisher.publishEvent(new SipEvent.AnswerEvent(this, eventDTO));
                break;

            case DIAL_REJECT_HANGUP:
                log.debug("发布拒绝事件: {}", eventDTO.getUuid());
              //  eventPublisher.publishEvent(new SipEvent.RejectEvent(this, eventDTO));
                break;

            case DIAL_SUCC_HANGUP:
                log.debug("发布挂断事件: {}", eventDTO.getUuid());
               // eventPublisher.publishEvent(new SipEvent.HangupEvent(this, eventDTO));
                break;

            default:
                log.debug("发布未知事件: {}", eventDTO.getUuid());
                eventPublisher.publishEvent(new SipEvent.UnknownEvent(this, eventDTO));
                break;
        }
    }

    private void setInboundFlowCode(DialEventDTO eventDTO) {
        String userData = inboundConfig.getFlow().get(eventDTO.getShowNum());
        log.info("Inbound showNum  : {}, userData: {}", eventDTO.getShowNum(), userData);
        if (StringUtils.isNotBlank(userData)) {
            eventDTO.setUserData(userData);
        }
    }

    private void handleRingOnce(DialEventDTO eventDTO) {
        String callId = eventDTO.getUuid();
        // 只处理一次 Ring / Originate
        boolean first = ringHandledCache.asMap().putIfAbsent(callId, Boolean.TRUE) == null;
        if (first) {
           // log.info("callId: {} ring or originate first time", callId);
            // 发布合并的 Ring 事件
            eventPublisher.publishEvent(new SipEvent.RingEvent(this, eventDTO));
        } else {
            log.warn("callId: {} ring or originate not first time", callId);
        }
    }

    /**
     * 预处理消息
     *
     * @param eventDTO 事件数据
     * @return 是否继续处理
     */
    private boolean preProcessMessage(DialEventDTO eventDTO) {
        // 处理入站呼叫
        if (eventDTO.getCallType().equalsIgnoreCase("INBOUND")) {
            setInboundFlowCode(eventDTO);
        }

        // 检查必要字段
        if (StringUtils.isAnyBlank(eventDTO.getCallStatus().name(), eventDTO.getEventName().name(),
                eventDTO.getUuid())) {
            log.warn("事件数据缺少必要字段: {}", JSON.toJSONString(eventDTO));
            return false;
        }
        return true;
    }


}
