/*
 * @Author: shunhua
 * @Date: 2025-04-29 11:11:30
 * @LastEditors: shunhua
 * @LastEditTime: 2025-04-29 14:25:15
 * @Description: 
 */
package com.wacai.loan.counselor.opt.sip.handler;

import lombok.Builder;
import lombok.Data;

/**
 * SIP 消息信息
 * 
 * 用于存储解析后的 SIP 消息信息
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Builder
public class SipMessageInfo {
    

    private int sipCallid;
    /**
     * 呼叫 UUID
     */
    private String callUuid;
    
    
    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 用户数据
     */
    private String userData;
    
    /**
     * 入站通道 UUID
     */
    private String inboundChannelUuid;
    
}
