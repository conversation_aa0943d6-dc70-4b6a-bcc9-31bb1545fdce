package com.wacai.loan.counselor.utils;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

/**
 * 
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-07-31 22:46
 */
@Slf4j
public class OkHttpUtil {

    private static volatile OkHttpClient okHttpClient = null;

    private static volatile OkHttpUtil instance = null;

    private OkHttpUtil() {
        if (okHttpClient == null) {
            synchronized (OkHttpUtil.class) {
                if (okHttpClient == null) {
                    ConnectionPool pool = new ConnectionPool(20, 3600, TimeUnit.MINUTES);
                    okHttpClient = new OkHttpClient.Builder().connectionPool(pool)
                            .connectTimeout(30, TimeUnit.SECONDS).writeTimeout(30, TimeUnit.SECONDS)
                            .readTimeout(30, TimeUnit.SECONDS).retryOnConnectionFailure(true)
                            .build();
                }
            }
        }
    }

    public OkHttpClient getOkHttpClient() {
        return okHttpClient;
    }

    public static OkHttpUtil getInstance() {
        if (instance == null) {
            synchronized (OkHttpUtil.class) {
                if (instance == null) {
                    instance = new OkHttpUtil();
                }
            }
        }
        return instance;
    }


    @SneakyThrows
    public <T> T post(String url, Map<String, String> headerMaps, String body,
            TypeReference<T> typeReference) {
        try (Response response = postJson(url, headerMaps, body)) {
            if (response.isSuccessful()) {
                ResponseBody responseBody = response.body();
                if (responseBody != null) {
                    String result = responseBody.string(); // Check if the result is not empty
                                                           // before parsin
                    if (StringUtils.isNotBlank(result)) {
                        if (typeReference.getType().getTypeName()
                                .equals(String.class.getTypeName())) {
                            @SuppressWarnings("unchecked")
                            T t = (T) result;
                            return t;
                        }
                        return JSON.parseObject(result, typeReference);
                    }
                }
                return null;
            } else {
                throw new RuntimeException(
                        "POST request failed: " + response.code() + " " + response.message());
            }
        }
    }

    @SneakyThrows
    public <T> T postForm(String url, Map<String, ?> requestBody, TypeReference<T> typeReference) {
        RequestBody body =
                getRequestBody(JSONObject.parseObject(JSONObject.toJSONString(requestBody)));
        Request.Builder requestBuild = new Request.Builder().url(url).get().post(body);
        try (Response response = okHttpClient.newCall(requestBuild.build()).execute()) {
            if (response.isSuccessful()) {
                ResponseBody responseBody = response.body();
                if (responseBody != null) {
                    String result = responseBody.string();
                    if (StringUtils.isNotBlank(result)) {
                        if (typeReference.getType().getTypeName()
                                .equals(String.class.getTypeName())) {
                            @SuppressWarnings("unchecked")
                            T t = (T) result;
                            return t;
                        }
                        return JSON.parseObject(result, typeReference);
                    }
                }
                return null;
            } else {
                throw new RuntimeException(
                        "GET request failed: " + response.code() + " " + response.message());
            }
        }
    }

    @SneakyThrows
    public <T> T get(String url, Map<String, String> headerMaps, Map<String, Object> paramMap,
            TypeReference<T> typeReference) {
        try (Response response = get(url, headerMaps, paramMap)) {
            if (response.isSuccessful()) {
                ResponseBody responseBody = response.body();
                if (responseBody != null) {
                    String result = responseBody.string();
                    if (StringUtils.isNotBlank(result)) {
                        if (typeReference.getType().getTypeName()
                                .equals(String.class.getTypeName())) {
                            @SuppressWarnings("unchecked")
                            T t = (T) result;
                            return t;
                        }
                        return JSON.parseObject(result, typeReference);
                    }
                }
                return null;
            } else {
                throw new RuntimeException(
                        "GET request failed: " + response.code() + " " + response.message());
            }
        }
    }


    private Response postJson(String url, Map<String, String> headerMaps, String json)
            throws IOException {
        RequestBody requestBody =
                RequestBody.create(json,MediaType.parse("application/json; charset=utf-8"));
        Request.Builder request = new Request.Builder().post(requestBody).url(url);
        for (Map.Entry<String, String> entry : headerMaps.entrySet()) {
            request.addHeader(entry.getKey(), entry.getValue());
        }
        return okHttpClient.newCall(request.build()).execute();
    }

    private Response get(String url, Map<String, String> headerMaps, Map<String, Object> paramMap)
            throws IOException {
        Request.Builder request = new Request.Builder().get();
        StringBuilder urlBuilder = new StringBuilder(url);
        for (Map.Entry<String, String> entry : headerMaps.entrySet()) {
            request.addHeader(entry.getKey(), entry.getValue());
        }
        if (paramMap != null) {
            urlBuilder.append("?");
            try {
                for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                    urlBuilder.append(URLEncoder.encode(entry.getKey(), "utf-8")).append("=")
                            .append(URLEncoder.encode(entry.getValue().toString(), "utf-8"))
                            .append("&");
                }
            } catch (Exception e) {
                log.error("OkHttpUtils#get exception", e);
            }
            urlBuilder.deleteCharAt(urlBuilder.length() - 1);
        }
        request.url(urlBuilder.toString());
        return okHttpClient.newCall(request.build()).execute();
    }

    private static RequestBody getRequestBody(JSONObject requestBody) {
        FormBody.Builder formBody = new FormBody.Builder();
        for (Map.Entry<String, Object> entry : requestBody.entrySet()) {
            Object value = entry.getValue();
            if (value == null) {
                value = "";
            }
            formBody.add(entry.getKey(), value.toString());
        }
        return formBody.build();
    }
}
