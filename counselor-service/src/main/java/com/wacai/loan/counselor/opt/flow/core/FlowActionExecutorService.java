/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-25 17:05:49
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-09-05 10:24:11
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.core;

import java.util.concurrent.ExecutorService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.opt.base.constant.FlowActionEnums;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.flow.model.FlowActionDTO;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import com.wacai.loan.counselor.opt.flow.service.CallProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class FlowActionExecutorService {

  @Qualifier("normalCallProcessor")
  private final CallProcessor callProcessor;

  public void executeAction(FlowActionDTO dto, WorkFlowNode workFlowNode) {
    WorkflowNodeConfig workflowNodeConfig = workFlowNode.getWorkflowNodeConfig();
    switch (dto.getFlowActionEnums()) {
      case TRANSFER: {
        boolean transferSuccess = callProcessor.transfer(dto.getCallId(), workflowNodeConfig);
        if (!transferSuccess) {
          log.warn("Transfer failed for callId: {}, fallback to PLAY", dto.getCallId());

          executeAction(new FlowActionDTO(dto.getCallId(), FlowActionEnums.PLAY), workFlowNode);
        } else {
          CallStateManager.getState(dto.getCallId()).setStopReceiveVoiceFlag(true);
        }
        break;
      }
      case PLAY: {
        ExecutorService speechExecutorService =
            CallStateManager.getState(dto.getCallId()).getSpeechExecutor();
        speechExecutorService.execute(() -> {
          callProcessor.playVoice(dto.getCallId(), workflowNodeConfig.getTip(), dto.getFileName(),
              workflowNodeConfig);
        });
        break;
      }
      case RESUME_PLAY:
        callProcessor.resumePlay(dto.getCallId());
        break;
      case STOP_PLAY:
        callProcessor.stopPlay(dto.getCallId());
        break;
      default:
        break;
    }
  }
}
