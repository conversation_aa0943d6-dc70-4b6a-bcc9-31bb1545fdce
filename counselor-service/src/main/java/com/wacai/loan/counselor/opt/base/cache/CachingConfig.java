/*
 * @Author: shunhua
 * 
 * @Date: 2025-08-01 10:21:19
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-01 10:37:34
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.base.cache;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@Configuration
@EnableCaching
@EnableAsync
@EnableScheduling
public class CachingConfig {

    @Bean
    public CacheManager cacheManager() {
        return new ConcurrentMapCacheManager("workflowByCode", "workflowByCampCode",
                "workflowNodes");
    }
}
