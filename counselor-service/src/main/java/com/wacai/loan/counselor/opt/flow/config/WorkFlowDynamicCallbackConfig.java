/*
 * @Author: shunhua
 * 
 * @Date: 2025-09-04 15:08:25
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-09-04 15:11:37
 * 
 * @Description: 流程动态回调地址配置
 */
package com.wacai.loan.counselor.opt.flow.config;

import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import lombok.Data;

@Data
public class WorkFlowDynamicCallbackConfig {

    private String wsUrl;

    public static WorkFlowDynamicCallbackConfig builderDynamicCallbackConfig(String context) {
        if (StringUtils.isEmpty(context)) {
            return null;
        }
        Map<String, Object> callContext =
                JSON.parseObject(context, new TypeReference<Map<String, Object>>() {});
        Object workflowDynamicCallbackConfig = callContext.get("_workflowDynamicCallbackConfig");
        if (Objects.nonNull(workflowDynamicCallbackConfig)
                && workflowDynamicCallbackConfig instanceof JSONObject) {
            return ((JSONObject) workflowDynamicCallbackConfig)
                    .to(WorkFlowDynamicCallbackConfig.class);
        }
        return null;
    }
}
