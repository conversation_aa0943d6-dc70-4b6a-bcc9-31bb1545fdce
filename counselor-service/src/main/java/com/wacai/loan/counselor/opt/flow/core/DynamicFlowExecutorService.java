/*
 * @Author: shunhua
 * @Date: 2025-09-05 10:24:41
 * @LastEditors: shunhua
 * @LastEditTime: 2025-09-05 10:24:42
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.core;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.opt.flow.service.CallProcessor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class DynamicFlowExecutorService {

  @Qualifier("dynamicCallProcessor")
  private final CallProcessor callProcessor;
  
  
}
