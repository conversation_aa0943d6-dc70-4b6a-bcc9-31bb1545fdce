/*
 * @Author: shunhua
 * 
 * @Date: 2025-04-30 10:30:21
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-09-04 15:10:35
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.model;

import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson2.JSON;
import com.wacai.loan.counselor.dao.po.AiFlow;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowDynamicCallbackConfig;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowRemoteVoiceConfig;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowRepeatTimesLimitConfig;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowWaitVoiceConfig;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
@NoArgsConstructor
public class AiFlowBO {

    private AiFlow aiFlow;

    private WorkFlowRemoteVoiceConfig aiFlowJSONConfig;

    private WorkFlowWaitVoiceConfig waitVoiceConfig;

    private WorkFlowRepeatTimesLimitConfig repeatTimesLimitConfig;

    private WorkFlowDynamicCallbackConfig dynamicCallbackConfig;

    public AiFlowBO(AiFlow aiFlow) {
        this.aiFlow = aiFlow;
        init();
    }

    private void init() {
        String waitVoice = aiFlow.getWaitVoice();
        if (StringUtils.isNotEmpty(waitVoice) && JSON.isValid(waitVoice)) {
            aiFlowJSONConfig = WorkFlowRemoteVoiceConfig.buildSelfInstance(waitVoice);
        }
        String context = aiFlow.getContext();
        if (StringUtils.isNotEmpty(context)) {
            waitVoiceConfig =
                    WorkFlowWaitVoiceConfig.builderWaitVoice(context);
            repeatTimesLimitConfig = WorkFlowRepeatTimesLimitConfig
                    .builderRepeatTimesLimitConfig(context);
        dynamicCallbackConfig=WorkFlowDynamicCallbackConfig.builderDynamicCallbackConfig(context);
        }
    }
}
