/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-23 15:20:23
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-25 17:44:43
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.manager;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.wacai.loan.counselor.dao.po.UserInput;
import com.wacai.loan.counselor.dao.repository.UserInputMapper;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;

@Service
public class UserInputManager {

    @Resource
    private UserInputMapper userInputMapper;


    @Transactional
    public UserInput add(String message, String callId, String phone,
            WorkflowNodeConfig workflowNodeConfig,String tip) {
        UserInput userInput = new UserInput();
        userInput.setCallUuid(callId);
        userInput.setTip(tip);
        userInput.setPhone(phone);
        userInput.setInput(message);
        userInput.setFlowCode(workflowNodeConfig.getWorkNodeCode());
        userInputMapper.insert(userInput);
        return userInput;
    }

    public void update(Long id, String message, String aiResut) {
        UserInput userInput = findById(id);
        userInput.setInput(message);
        userInput.setAiSemantics(aiResut);
        userInput.setUpdatedTime(new Date());
        userInputMapper.updateByPrimaryKeySelective(userInput);
    }


    public UserInput findById(Long id) {
        return userInputMapper.selectByPrimaryKey(id);
    }


    public void update(UserInput userInput) {
        userInputMapper.updateByPrimaryKeySelective(userInput);
    }


    public List<UserInput> list(String callId) {
        UserInput userInput = new UserInput();
        userInput.setCallUuid(callId);
        return userInputMapper.select(userInput);
    }
}
