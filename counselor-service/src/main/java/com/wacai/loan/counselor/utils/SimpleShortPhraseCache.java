/*
 * @Author: shunhua
 * @Date: 2025-06-18 14:59:00
 * @LastEditors: shunhua
 * @LastEditTime: 2025-06-20 13:57:09
 * @Description: 
 */
package com.wacai.loan.counselor.utils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.lang3.StringUtils;

public class SimpleShortPhraseCache {

    private static final Map<String, StringBuilder> cache = new ConcurrentHashMap<>();

    private static final int MAX_LENGTH = 3;

    // 匹配所有常见中英文标点、空格、换行等
    private static final String PUNCTUATION_REGEX = "[\\p{Punct}，。？！；：“”‘’、\\s]";

    /**
     * 添加短语，拼接缓存。判断逻辑基于去标点长度。
     * 
     * @param callUuid 通话ID
     * @param phrase 新识别短语（可包含标点）
     * @return 若拼接后超过阈值，则返回拼接字符串并清空缓存；否则返回 null。
     */
     
    public static String addAndCheck(String callUuid, String phrase) {
        if (StringUtils.isBlank(phrase)) {
            return null;
        }
        phrase = phrase.trim();
        String cleanPhrase = phrase.replaceAll(PUNCTUATION_REGEX, "");

        // 如果当前一句话就足够长，直接打断 + 拼上前面缓存
        if (cleanPhrase.length() > MAX_LENGTH) {
            StringBuilder previous = cache.remove(callUuid);
            if (previous != null && previous.length() > 0) {
                return previous.append(phrase).toString();
            } else {
                return phrase;
            }
        }

        // 当前不够长 → 缓存起来
        StringBuilder sb = cache.computeIfAbsent(callUuid, k -> new StringBuilder());
        sb.append(phrase);
        return null;
       }

    /**
     * 清除指定通话ID的缓存内容。
     * 
     * @param callUuid 通话唯一ID
     */
    public static void clear(String callUuid) {
        cache.remove(callUuid);
    }

    /**
     * 可选辅助方法：去除标点
     * 
     * @param text 原文本
     * @return 清洗后的纯文本
     */
    public static String stripPunctuation(String text) {
        return text == null ? null : text.replaceAll(PUNCTUATION_REGEX, "");
    }
}
