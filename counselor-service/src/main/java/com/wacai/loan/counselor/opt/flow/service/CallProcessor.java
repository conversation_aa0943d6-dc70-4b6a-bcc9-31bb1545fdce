/*
 * @Author: shunhua
 * @Date: 2025-05-06 16:49:57
 * @LastEditors: shunhua
 * @LastEditTime: 2025-09-05 10:17:52
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.service;

import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;

public interface CallProcessor {
    
    boolean transfer(String callId, WorkflowNodeConfig workflowNodeConfig);

    void playVoice(String callId,String text,String fileName,WorkflowNodeConfig workflowNodeConfig);

    void cyclePlayVoice(String callId,String voicePath);

    void resumePlay(String callId);

    void stopPlay(String callId);

    void playRealtimeVoice(String callId,Byte[] voiceBytes); 
}
