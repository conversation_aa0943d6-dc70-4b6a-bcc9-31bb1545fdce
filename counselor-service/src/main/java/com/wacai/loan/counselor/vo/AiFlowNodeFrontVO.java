/*
 * @Author: shunhua
 * @Date: 2025-04-25 11:16:57
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-18 11:21:42
 * @Description: 
 */
package com.wacai.loan.counselor.vo;

import java.util.Map;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import com.wacai.loan.counselor.dao.po.AiFlowNode;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/12 14:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AiFlowNodeFrontVO extends AiFlowNode {

    String flowCode;

    /**
     * 节点之间的语义映射关系，实际是编辑前置节点的属性。一般是新增子节点的时候传入
     */
    AiFlowNodeFrontVO preAiFlowNode;

    /**
     * 单条的映射关系
     */
    Map<String, String> singleItemSemanticsTransform;

    public static String acqNewNodeCode(String flowCode, String newFlowCode, String aiFlowNodeCode) {
        if (StringUtils.isBlank(aiFlowNodeCode)) {
            return StringUtils.EMPTY;
        }

        String aiFlowNodeCodeNew;
        if (StringUtils.startsWith(aiFlowNodeCode, flowCode)) {
            aiFlowNodeCodeNew = RegExUtils.replaceFirst(aiFlowNodeCode, flowCode, newFlowCode);
        } else {
            aiFlowNodeCodeNew = newFlowCode + "_" + aiFlowNodeCode;
        }
        return aiFlowNodeCodeNew;
    }
    
    public AiFlowNode buildAiFlowNode() {
        final AiFlowNode aiFlowNode = new AiFlowNode();
        BeanUtils.copyProperties(this, aiFlowNode);
        return aiFlowNode;
    }

    public static AiFlowNodeFrontVO buildSelf(AiFlowNode flowNode) {
        final AiFlowNodeFrontVO aiFlowNodeFrontVO = new AiFlowNodeFrontVO();
        BeanUtils.copyProperties(flowNode, aiFlowNodeFrontVO);
        return aiFlowNodeFrontVO;
    }
}
