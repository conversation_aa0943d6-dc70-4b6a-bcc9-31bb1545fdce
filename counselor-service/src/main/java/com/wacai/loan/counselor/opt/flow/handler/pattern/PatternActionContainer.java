/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-08 16:32:40
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-08 17:17:07
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.handler.pattern;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

public class PatternActionContainer {


    public static String findFlowNodeCode(String callId, String text,
            Map<String, String> semanticsTransform,
            List<NextNodeCodeAdapter> nextNodeCodeAdapters) {
        Optional<NextNodeCodeAdapter> optional =
                findNextNodeCodeAdapter(callId, text, nextNodeCodeAdapters);
        if (optional.isPresent()) {
            return optional.get().queryFlowNodeCode(semanticsTransform);
        }
        return StringUtils.EMPTY;

    }
    

    private  static Optional<NextNodeCodeAdapter> findNextNodeCodeAdapter(String callId, String text,
            List<NextNodeCodeAdapter> nextNodeCodeAdapters) {
        return nextNodeCodeAdapters.stream().filter(x -> x.isSuit(callId, text)).findFirst();
    }
}
