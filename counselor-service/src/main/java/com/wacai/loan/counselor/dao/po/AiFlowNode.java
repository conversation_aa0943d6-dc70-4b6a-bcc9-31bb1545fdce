package com.wacai.loan.counselor.dao.po;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.wacai.loan.counselor.opt.base.jsonformat.JsonDataFormat;
import lombok.Data;
/**
 * ai流程节点
 *
 * @TableName ai_flow_node
 */
@Table(name = "ai_flow_node")
@Data
@Entity
public class AiFlowNode {
    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * ai提示
     */
    private String tip;

    /**
     * 短信模板 id
     */
    @Column(name = "sms_ruler_id")
    private String smsRulerId;

    /**
     * 用于动态返回节点的脚本
     */
    private String script;

    /**
     * 是否是终止节点
     */
    private Boolean end;

    /**
     * 是否触发用户输入
     */
    @Column(name = "trigger_input")
    private Boolean triggerInput;

    /**
     * 是否触发用户输入
     */
    @Column(name = "receive_simple_dtmf")
    private Boolean receiveSimpleDtmf;

    /**
     * 额外拓展属性，json 格式
     */
    @Column(name = "ext_info")
    private String extInfo;

    /**
     * 备注
     */
    private String comment;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    @JsonDataFormat
    private Date createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    @JsonDataFormat
    private Date updatedTime;

    @Column(name = "next_node_code_adapters")
    private String nextNodeCodeAdapters;

    @Column(name = "simple_next_flow_code")
    private String simpleNextFlowCode;

    @Column(name = "transfer_queue_code")
    private String transferQueueCode;

    @Column(name = "voice_path")
    private String voicePath;

    @Column(name = "semantics_map")
    private String semanticsMap;

    @Column(name = "semantics_transform")
    private String semanticsTransform;

    @Column(name = "remote_voice")
    private String remoteVoice;

    private String context;

    /**
     * 节点动作的配置
     */
    @Column(name = "node_action")
    private String nodeAction;

    private String tag;
}