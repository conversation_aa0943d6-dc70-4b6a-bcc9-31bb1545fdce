package com.wacai.loan.counselor.dao.po;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * ai匹配模式
 *
 * @TableName ai_pattern
 */
@Table(name = "ai_pattern")
@Data
@Entity
public class AiPattern implements Serializable {
    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 匹配模式，正则表达式。json 数组格式
     */
    private String patterns;

    /**
     * 自包含的编码。json 数组格式
     */
    @Column(name = "include_codes")
    private String includeCodes;

    /**
     * 备注
     */
    private String comment;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    private Date updatedTime;

    @Column(name = "need_check")
    private Boolean needCheck;

    private static final long serialVersionUID = 1L;
}