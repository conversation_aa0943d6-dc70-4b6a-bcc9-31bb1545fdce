package com.wacai.loan.counselor.opt.sip.sipsession;

import static org.pjsip.pjsua2.pjsip_status_code.PJSIP_SC_OK;
import static org.pjsip.pjsua2.pjsip_status_code.PJSIP_SC_RINGING;
import java.util.concurrent.ExecutorService;
import javax.annotation.Resource;
import org.pjsip.pjsua2.Account;
import org.pjsip.pjsua2.OnIncomingCallParam;
import org.pjsip.pjsua2.OnInstantMessageParam;
import org.pjsip.pjsua2.OnRegStateParam;
import org.pjsip.pjsua2.OnTypingIndicationParam;
import org.pjsip.pjsua2.SipRxData;
import org.springframework.context.ApplicationContext;
import com.alibaba.fastjson2.JSON;
import com.wacai.loan.counselor.opt.base.dto.DialEventDTO;
import com.wacai.loan.counselor.opt.base.event.AccountRegistryStateEvent;
import com.wacai.loan.counselor.opt.base.threadpool.ExecutorServiceHandlerRegistry;
import com.wacai.loan.counselor.opt.sip.cache.CallContext;
import com.wacai.loan.counselor.opt.sip.handler.SipMessageHandler;
import com.wacai.loan.counselor.opt.sip.handler.SipMessageInfo;
import com.wacai.loan.counselor.opt.sip.service.SipEventService;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * SIP 账号类
 *
 * 负责处理 SIP 账号注册、呼叫接听等功能 纯粹的 SIP 功能，不包含业务逻辑
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
@RequiredArgsConstructor
public class AiAgentAccount extends Account {

    // SIP 账号信息
    private final String realm;

    private final String number;

    private final String username;

    private final String password;

    private final String siphost;

    private final String source;

    // 依赖服务
    @Resource
    private ApplicationContext appContext;

    @Resource
    private CallContext callContext;

    @Resource
    private SipEventService sipEventService;


    @Resource
    private SipMessageHandler sipMessageHandler;

    private static ExecutorService executorService=ExecutorServiceHandlerRegistry.getExecutorServiceHandler(20,"sip");

    /**
     * 处理账号注册状态变更
     *
     * @param param 注册状态参数
     */
    @Override
    public void onRegState(final OnRegStateParam param) {
        log.info("On account [{}] register state changed, code [{}], status [{}].", username,
                param.getCode(), param.getStatus());

        if (log.isDebugEnabled()) {
            final SipRxData rdata = param.getRdata();
            log.debug("On account [{}] [{}] register message [{}].", username, rdata.getInfo(),
                    rdata.getWholeMsg());
        }
        
      appContext.publishEvent(
                new AccountRegistryStateEvent(username, source, param.getCode()));
    }

    /**
     * 处理来电
     *
     * @param param 来电参数
     */
    @Override
    public void onIncomingCall(final OnIncomingCallParam param) {
        final SipRxData sipRxData = param.getRdata();
        final String sipMessage = sipRxData.getWholeMsg();
        log.info("Incoming call SIP message: [{}]", sipMessage);

        // 释放SIP数据
        sipRxData.delete();

        // 解析SIP消息
        SipMessageInfo messageInfo =
                sipMessageHandler.parseSipMessage(param.getCallId(), sipMessage);

        // 创建呼叫对象
        AiAgentCall call = createAiAgentCall(messageInfo);

        call.answer(PJSIP_SC_RINGING);

        log.info("Answer call [{}] OK.", messageInfo.getCallUuid());

        call.answer(PJSIP_SC_OK);
    }

    /**
     * 创建呼叫对象
     *
     * @param callId 呼叫ID
     * @param messageInfo SIP消息信息
     * @return 呼叫对象
     */
    private AiAgentCall createAiAgentCall(SipMessageInfo messageInfo) {

        AiAgentCall AiAgentCall = new AiAgentCall(this, messageInfo);
        // 保存呼叫对象
        callContext.put(messageInfo.getCallUuid(), AiAgentCall);

        return AiAgentCall;
    }

    /**
     * 处理即时消息
     *
     * @param prm 即时消息参数
     */
    @Override
    public void onInstantMessage(OnInstantMessageParam prm) {
        super.onInstantMessage(prm);
        final String msgBody = prm.getMsgBody();
        log.info("onInstantMessage {}", msgBody);
        executorService.execute(() -> {
            DialEventDTO eventDTO = JSON.parseObject(msgBody, DialEventDTO.class);
            sipEventService.handler(eventDTO);
        });
    }

    /**
     * 处理打字指示
     *
     * @param prm 打字指示参数
     */
    @Override
    public void onTypingIndication(OnTypingIndicationParam prm) {
        super.onTypingIndication(prm);
        log.info("onTypingIndication {}", prm);
    }
}
