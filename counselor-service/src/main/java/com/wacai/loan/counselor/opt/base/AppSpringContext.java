/*
 * @Author: shunhua
 * @Date: 2025-04-30 13:47:26
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-18 17:58:51
 * @Description: 
 */
package com.wacai.loan.counselor.opt.base;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/4 15:50
 */
@Component
public class AppSpringContext implements ApplicationContextAware {

    @Getter
    private static ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        if (AppSpringContext.context == null) {
            AppSpringContext.context = applicationContext;
        }
    }

    public static <T> T getBean(Class<T> requiredType) {
        return context.getBean(requiredType);
    }

    public static Object getBean(String name) {
        return context.getBean(name);
    }

    public static <T> T getBean(Class<T> requiredType, String name) {
        return context.getBean(name, requiredType);
    }
}
