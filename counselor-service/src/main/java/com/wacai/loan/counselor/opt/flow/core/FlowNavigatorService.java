/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-25 17:36:18
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-25 17:46:23
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.core;

import java.util.Objects;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.opt.base.constant.FlowActionEnums;
import com.wacai.loan.counselor.opt.base.constant.FlowConstants;
import com.wacai.loan.counselor.opt.base.state.CallState;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.flow.cache.WorkFlowLoadCache;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowWaitVoiceConfig;
import com.wacai.loan.counselor.opt.flow.model.FlowActionDTO;
import com.wacai.loan.counselor.opt.flow.service.UserInputService;
import com.wacai.loan.counselor.opt.sip.cache.CallContext;
import com.wacai.loan.counselor.opt.sip.sipsession.AiAgentCall;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class FlowNavigatorService {

    private final CallContext callContext;

    private final WorkFlowLoadCache workFlowLoadCache;

    private final UserInputService userInputService;

    private final FlowActionExecutorService flowActionExecutorService;

    public void adapterNodeWait(String callId, WorkFlowNode workFlowNode) {
        WorkFlowWaitVoiceConfig voiceConfig =
                workFlowNode.getWorkflowNodeConfig().getWaitVoiceCfg();
        if (Objects.nonNull(voiceConfig) && !voiceConfig.isEnable()) {
            return;
        }
        if (workFlowNode.getWorkflowNodeConfig().isEnd()) {
            log.info("callId: {} isEnd,so hangup", callId);
            hangup(callId);
            return;
        }
        CallState callState = CallStateManager.getState(callId);
        if (workFlowNode.getWorkflowNodeConfig().getTransferLimitConfig().isTransfer(callId)) {
            callState.setStopReceiveVoiceFlag(true);
        }
        if (!callState.isRunning()) {
            return;
        }

        // 清除old定时任务
        callState.clearWaitHandle();
        String flowNodeCodeTimeout =
                workFlowNode.getWorkflowNodeConfig().getWorkNodeCode() + "_timeout";
        int timeoutCount = callState.getOrDefault(flowNodeCodeTimeout, Integer.class, 0);
        if (timeoutCount >= voiceConfig.getTimes()) {
            if (StringUtils.isNotBlank(voiceConfig.getMuteEndFlowCode())) {
                callState.setWaitHandle(CallStateManager.getScheduler().schedule(() -> {
                    processByFlowCode(callId, voiceConfig.getMuteEndFlowCode());
                }, voiceConfig.getInterval(), TimeUnit.SECONDS));
                return;
            } else {
                log.info("callId: {} waitTimes greater config,so hangup", callId);
                hangup(callId);
                return;
            }
        } else {
            callState.setWaitHandle(CallStateManager.getScheduler().schedule(() -> {
                if (!callState.isRunning() || callState.isPlaying()) {
                    return;
                }
                callState.put(flowNodeCodeTimeout, timeoutCount + 1);
                log.info("callId: {} again play wait voice times : {}", callId, timeoutCount + 1);
                waitPlay(callId, workFlowNode);
            }, voiceConfig.getInterval(), TimeUnit.SECONDS));
        }
    }

    private void processByFlowCode(String callId, String muteEndFlowCode) {
        log.info("designateAction callId: {}  | nextFlowCode: {}", callId, muteEndFlowCode);
        AiAgentCall call = callContext.get(callId);
        if (Objects.isNull(call)) {
            log.warn("callId: {} is null,so return", callId);
            return;
        }
        WorkFlowNode workFlowNode = workFlowLoadCache
                .getWorkFlowNodeOfFill(call.getAiFlowBO().getAiFlow().getCode(), muteEndFlowCode);
        userInputService.add("", callId, CallStateManager.getMobile(callId),
                workFlowNode.getWorkflowNodeConfig());
        CallStateManager.getState(callId).setCurrentNode(workFlowNode);
        flowActionExecutorService.executeAction(new FlowActionDTO(callId, FlowActionEnums.PLAY),
                workFlowNode);
    }

    private void waitPlay(String callId, WorkFlowNode workFlowNode) {
        WorkFlowWaitVoiceConfig waitVoiceCfg =
                workFlowNode.getWorkflowNodeConfig().getWaitVoiceCfg();
        if (Objects.nonNull(waitVoiceCfg) && waitVoiceCfg.isRepeatSelf()) {
            flowActionExecutorService.executeAction(new FlowActionDTO(callId, FlowActionEnums.PLAY),
                    workFlowNode);
        } else {
            String fileName = "";
            String waitVoice = callContext.get(callId).getAiFlowBO().getAiFlow().getWaitVoice();
            if (StringUtils.isNotBlank(waitVoice)
                    && Objects.isNull(callContext.get(callId).getAiFlowBO().getWaitVoiceConfig())) {
                fileName = waitVoice;
            } else {
                fileName = FlowConstants.getWavPath() + "common_hztm.wav";
            }
            flowActionExecutorService.executeAction(
                    new FlowActionDTO(callId, fileName, FlowActionEnums.PLAY), workFlowNode);

        }
    }

    public void hangup(String callId) {
        AiAgentCall call = callContext.get(callId);
        if (Objects.isNull(call)) {
            log.warn("callId: {} need hangup,but call is null", callId);
            return;
        }
        call.hangup();
    }
}
