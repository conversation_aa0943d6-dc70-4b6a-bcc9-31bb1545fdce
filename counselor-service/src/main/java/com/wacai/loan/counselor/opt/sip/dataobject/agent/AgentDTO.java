/*
 * @Author: shunhua
 * @Date: 2025-04-25 11:16:57
 * @LastEditors: shunhua
 * @LastEditTime: 2025-05-04 15:01:16
 * @Description: 
 */
package com.wacai.loan.counselor.opt.sip.dataobject.agent;

import com.google.common.base.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by katana 2023/5/11 15:35
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgentDTO   {

    private String name;
    private String sipAccount;
    private String sipPassword;


    private String   userTenant;
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AgentDTO)) {
            return false;
        }
        AgentDTO agentDTO = (AgentDTO) o;
        return Objects.equal(sipAccount, agentDTO.sipAccount);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(sipAccount);
    }
}
