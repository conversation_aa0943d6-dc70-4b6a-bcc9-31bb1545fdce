package com.wacai.loan.counselor.opt.sip.sipsession;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wacai.loan.counselor.opt.base.alarm.AlarmService;
import com.wacai.loan.counselor.rebalance.AgentInstanceManager;
import com.wacai.loan.counselor.utils.AccountUtils;

import com.wacai.loan.counselor.opt.sip.dataobject.agent.AgentDTO;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: qiushui
 * @Date: 2020/9/4 2:09 PM
 */
@Component
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AiAgentRegistryService {


    @Value("${linkup.account.loader.siphost:}")
    private String sipHost;

    @Resource
    private ConfigurableApplicationContext applicationContext;

    @Resource
    private AiAgentSipService aiAgentSipService;
    @Resource
    private AgentInstanceManager agentInstanceManager;

    @Getter
    private Map<String, AiAgentAccount> robotAccountBeanMap = Maps.newHashMap();

    @Resource
    AlarmService alarmService;

     

    @SneakyThrows
    public void registryAccount(List<AgentDTO> accounts) {
        List<String> notAlloctAccounts = Lists.newArrayList();
        DefaultListableBeanFactory beanFactory = (DefaultListableBeanFactory) applicationContext.getBeanFactory();
        accounts.stream().forEach(x -> {
            if (!applicationContext.containsBeanDefinition(AccountUtils.beanAccount(x.getSipAccount()))) {
                registerBeanDefinition(beanFactory, x);
                notAlloctAccounts.add(x.getSipAccount());
            } else {
                log.warn("Account :{} is exist", x.getSipAccount());
            }
        });

        List<AiAgentAccount> robotAccounts = getRobotAccounts(notAlloctAccounts);
        log.info("Account : {} registerPjsua2",
            robotAccounts.stream().map(x -> x.getUsername()).collect(Collectors.toList()));
        if (!robotAccounts.isEmpty()) {
            aiAgentSipService.registerPjsua2(robotAccounts);
        }
        agentInstanceManager.setZkInstanceData(accounts);
    }

    public  List<AiAgentAccount> getRobotAccounts(List<String> notAlloctAccounts) {
        Map<String, AiAgentAccount> robotAccountMap = applicationContext.getBeansOfType(
                AiAgentAccount.class);
        List<AiAgentAccount> robotAccounts = robotAccountMap.values()
            .stream()
            .filter(x -> notAlloctAccounts.contains(x.getUsername()))
            .collect(Collectors.toList());
        return robotAccounts;
    }

    private void registerBeanDefinition(DefaultListableBeanFactory beanFactory, AgentDTO x) {
        AiAgentAccount aiAgentAccount = new AiAgentAccount("", x.getSipAccount(), x.getName(), x.getSipPassword(), sipHost,
            x.getUserTenant());
        beanFactory.registerBeanDefinition(AccountUtils.beanAccount(x.getSipAccount()),
            BeanDefinitionBuilder.genericBeanDefinition(
                        AiAgentAccount.class, () -> aiAgentAccount).getBeanDefinition());
        robotAccountBeanMap.put(AccountUtils.beanAccount(x.getSipAccount()), aiAgentAccount);
    }

    public List<AgentDTO> getLocalAccount() {
        return robotAccountBeanMap.values()
            .stream()
            .map(x -> new AgentDTO(x.getUsername(), x.getUsername(), x.getPassword(), x.getSource()))
            .collect(Collectors.toList());
    }
   
}
