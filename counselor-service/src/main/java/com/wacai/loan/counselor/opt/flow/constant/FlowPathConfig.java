/*
 * @Author: shunhua
 * @Date: 2025-04-15 11:27:38
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-17 17:07:25
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.constant;

import javax.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.wacai.loan.counselor.opt.base.constant.FlowConstants;

@Component
@Slf4j
public class FlowPathConfig {

    @Value("${flow.wav-path}")
    private String wavPath;

    @PostConstruct
    public void init() {
        FlowConstants.setWavPath(wavPath);
    }
}
