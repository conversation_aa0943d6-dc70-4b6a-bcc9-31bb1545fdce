package com.wacai.loan.counselor.utils;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/26 16:26
 */
@UtilityClass
@Slf4j
public class MessageDigestUtils {

    public static String getSHA256(String input) {
        try {
            // 获取MessageDigest实例，指定SHA-256算法
            MessageDigest md = MessageDigest.getInstance("SHA-256");

            // 更新要计算的消息摘要的数据
            byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));

            // 将字节数组转换为16进制字符串
            BigInteger number = new BigInteger(1, messageDigest);
            StringBuilder hashText = new StringBuilder(number.toString(16));
            // Now we need to zero pad it if you actually want the full 64 chars.
            while (hashText.length() < 64) {
                hashText.insert(0, "0");
            }

            return hashText.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("getSHA256 err %s", input, e);
        }
        return null;
    }
}

