/*
 * @Author: shun<PERSON>
 * @Date: 2025-06-06 16:29:21
 * @LastEditors: shunhua
 * @LastEditTime: 2025-06-06 16:29:22
 * @Description: 
 */
package com.wacai.loan.counselor.utils;

import java.util.concurrent.atomic.AtomicLong;

public class GlobalAlarmLimiter {

    // 上次发告警的时间戳
    private static final AtomicLong lastAlarmTime = new AtomicLong(0);
    // 告警间隔，单位毫秒（1分钟）
    private static final long INTERVAL = 60 * 1000;

    public static boolean shouldAlert() {
        long now = System.currentTimeMillis();
        long lastTime = lastAlarmTime.get();

        if (now - lastTime >= INTERVAL) {
            // 尝试更新为当前时间，确保只有一个线程通过
            return lastAlarmTime.compareAndSet(lastTime, now);
        }
        return false;
    }
}
