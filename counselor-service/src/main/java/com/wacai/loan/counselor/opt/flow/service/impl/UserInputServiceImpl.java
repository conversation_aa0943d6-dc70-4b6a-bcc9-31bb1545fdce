/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-12 11:11:24
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-26 11:28:16
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Lists;
import com.wacai.loan.counselor.dao.po.UserInput;
import com.wacai.loan.counselor.opt.base.cache.CallUserDataCache;
import com.wacai.loan.counselor.opt.base.constant.FlowConstants;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.base.threadpool.ExecutorServiceHandlerRegistry;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowRemoteVoiceConfig;
import com.wacai.loan.counselor.opt.flow.manager.UserInputManager;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import com.wacai.loan.counselor.opt.flow.service.UserInputService;
import com.wacai.loan.counselor.utils.FlowTipUtils;
import com.wacai.loan.counselor.utils.Utils;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class UserInputServiceImpl implements UserInputService {

    private static ExecutorService executorService =
            ExecutorServiceHandlerRegistry.getExecutorServiceHandler(1, "user-input");

    @Resource
    private UserInputManager userInputManager;

    @Override
    public void add(String message, String callId, String phone,
            WorkflowNodeConfig workflowNodeConfig) {
        executorService.execute(() -> {
            UserInput userInput = userInputManager.add(message, callId, phone, workflowNodeConfig,
                    getFormatTip(callId, workflowNodeConfig));
            CallStateManager.getState(callId).put(FlowConstants.USER_INPUT, userInput.getId());
        });
    }

    @Override
    public void update(String callId, String message, String aiResut) {
        executorService.execute(() -> {
            Long id = CallStateManager.getState(callId).get(FlowConstants.USER_INPUT, Long.class);
            if (Objects.isNull(id)) {
                log.warn("callId: {} , UserInput cache Id is null", callId);
                return;
            }
            userInputManager.update(id, message, aiResut);
        });
    }


    @Override
    public void updateUserInputOfVoiceAssistant(String callId, String text) {
        Long id = CallStateManager.getState(callId).get(FlowConstants.USER_INPUT, Long.class);
        if (Objects.isNull(id)) {
            log.warn("callId: {} , UserInput cache Id is null", callId);
            return;
        }
        UserInput userInput = userInputManager.findById(id);
        List<String> nodeContexts = Lists.newArrayList();
        if (StringUtils.isNotEmpty(userInput.getNodeContext())) {
            nodeContexts = JSONObject.parseObject(userInput.getNodeContext(),
                    new TypeReference<List<String>>() {});
        }
        nodeContexts.add(text);
        userInput.setUpdatedTime(new Date());
        userInput.setNodeContext(JSONObject.toJSONString(nodeContexts));
        userInputManager.update(userInput);
    }

    @Override
    public List<UserInput> listUserInputByCallId(String callId) {
        return userInputManager.list(callId);
    }


    private String getFormatTip(String callId, WorkflowNodeConfig workflowNodeConfig) {
        String tip = workflowNodeConfig.getTip();
        try {
            WorkFlowRemoteVoiceConfig voiceJsonConfig = workflowNodeConfig.getVoiceJsonConfig();
            if (Objects.nonNull(voiceJsonConfig)
                    && !CollectionUtils.isEmpty(voiceJsonConfig.getFallbackTemplates())) {
                JSONObject userData = CallUserDataCache.getCache(callId);
                Object paramObj = CallUserDataCache.getRemoteVoiceParam(userData);
                String cusParam = WorkFlowRemoteVoiceConfig.acqJsonData(paramObj);
                WorkFlowRemoteVoiceConfig mergedParam =
                        voiceJsonConfig.buildMergedParamCopyWithFallback(cusParam);
                if (StringUtils.isNotEmpty(mergedParam.getTip())) {
                    log.info("callId: {} get tip from mergedParam: {}", callId,
                            mergedParam.getTip());
                    tip = mergedParam.getTip();
                }
            }

            if (FlowTipUtils.isFormat(tip)) {
                JSONObject userData = CallUserDataCache.getCache(callId);
                Object paramObj = CallUserDataCache.getRemoteVoiceParam(userData);
                String cusParam = WorkFlowRemoteVoiceConfig.acqJsonData(paramObj);
                Map<String, Object> map = Utils
                        .flattenJson(StringUtils.isNotEmpty(cusParam) ? JSON.parseObject(cusParam)
                                : new JSONObject());
                return FlowTipUtils.format(tip, map);
            }
        } catch (Exception e) {
            log.error("callId: {} get tip error: {}", callId, e.getMessage(), e);
            return tip;
        }
        return tip;
    }
}
