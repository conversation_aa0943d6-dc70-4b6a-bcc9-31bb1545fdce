/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-07 16:27:33
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-09-04 15:13:11
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.cache;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.dao.po.AiFlowNode;
import com.wacai.loan.counselor.opt.flow.core.WorkFlowNode;
import com.wacai.loan.counselor.opt.flow.manager.AiFlowManager;
import com.wacai.loan.counselor.opt.flow.manager.AiFlowNodeManager;
import com.wacai.loan.counselor.opt.flow.model.AiFlowBO;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class WorkFlowLoadCache {

    @Resource
    private WorkFlowNodeBOCahche workFlowNodeBOCahche;

    @Resource
    private WorkFlowBOCache workFlowBOCache;

    @Resource
    private WorkFlowNodeLoadBuilder workFlowNodeLoadBuilder;

    @Resource
    private AiFlowManager aiFlowManager;

    @Resource
    private AiFlowNodeManager aiFlowNodeManager;

    @Resource
    private CacheManager cacheManager;

    @Getter
    private Map<String, List<WorkflowNodeConfig>> aiFlowNodeOfCampsMaps = Maps.newHashMap();

    private Map<String, List<WorkflowNodeConfig>> aiFlowNodeOfCodeMaps = Maps.newHashMap();

    public void initWorkFlow() {
        clearCache();
        initWorkFlowCache();
        initWorkFlowNodeCache();
        advanceFlowNodeOfRemoteVoice();
    }

    public void clearCache() {
        cacheManager.getCache("workflowByCode").clear();
        cacheManager.getCache("workflowByCampCode").clear();
        cacheManager.getCache("workflowNodes").clear();
    }

    private void advanceFlowNodeOfRemoteVoice() {
        WorkFlowNodeLoadBuilder.FlowNodeMapResult result = workFlowNodeLoadBuilder.buildMaps();
        aiFlowNodeOfCampsMaps.clear();
        aiFlowNodeOfCampsMaps.putAll(result.getCampMap());
        log.info("------加载提前生成变量话术缓存----------");
        StringBuffer buffer = new StringBuffer();
        aiFlowNodeOfCampsMaps.forEach((x, y) -> {
            buffer.append("campCode: ").append(x);
            buffer.append(", size:").append(y.size()).append("条\n");
        });
        log.info(buffer.toString());
        aiFlowNodeOfCodeMaps.clear();
        aiFlowNodeOfCodeMaps.putAll(result.getFlowCodeMap());

        log.info("*******加载生成变量话术缓存********");
        StringBuffer flowBuffer = new StringBuffer();
        aiFlowNodeOfCodeMaps.forEach((x, y) -> {
            flowBuffer.append("flowCode : ").append(x);
            flowBuffer.append(", size:").append(y.size()).append("条\n");
        });
        log.info(flowBuffer.toString());
        log.info("*******加载缓存 SUCC ********");
    }


    public List<WorkflowNodeConfig> getByCampCode(String campCode) {
        return aiFlowNodeOfCampsMaps.getOrDefault(campCode, Collections.emptyList());
    }


    public List<WorkflowNodeConfig> getByFlowCode(String flowCode) {
        return aiFlowNodeOfCodeMaps.getOrDefault(flowCode, Collections.emptyList());
    }



    public void initWorkFlowCache() {
        aiFlowManager.list(true).stream().forEach(x -> {
            workFlowBOCache.getAiFlowByCode(x.getCode());
        });
    }

    public void initWorkFlowNodeCache() {
        log.info("Init Load WorkFlowNode start....");
        List<AiFlowNode> list = aiFlowNodeManager.listAll();
        list.stream().forEach(x -> {
            workFlowNodeBOCahche.getNodeByCode(x.getCode());
            log.info("Init Load WorkFlowNode code: {}", x.getCode());
        });
        log.info("Init Load WorkFlowNode end .....");
    }


    public WorkFlowNode getWorkFlowNodeOfFill(String flowCode, String flowNodeCode) {
        AiFlowBO aiFlowBO = workFlowBOCache.getAiFlowByCode(flowCode);
        WorkFlowNode workFlowNode = workFlowNodeBOCahche.getNodeByCode(flowNodeCode);
        if (Objects.isNull(workFlowNode)) {
            return null;
        }
        workFlowNodeBOCahche.fromAiFlowFillNode(aiFlowBO, workFlowNode);
        return workFlowNode;
    }

    public AiFlowBO getAiFlowByCode(String flowCode) {
        return workFlowBOCache.getAiFlowByCode(flowCode);
    }
}
