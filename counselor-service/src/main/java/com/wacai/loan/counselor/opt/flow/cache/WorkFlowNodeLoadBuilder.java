/*
 * @Author: shunhua
 * 
 * @Date: 2025-04-15 19:22:29
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-25 17:24:30
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.cache;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.stereotype.Component;
import com.wacai.loan.counselor.dao.po.AiFlowToNode;
import com.wacai.loan.counselor.opt.flow.core.WorkFlowNode;
import com.wacai.loan.counselor.opt.flow.manager.AiFlowManager;
import com.wacai.loan.counselor.opt.flow.manager.AiFlowToNodeManager;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class WorkFlowNodeLoadBuilder {

    private final AiFlowManager aiFlowManager;

    private final AiFlowToNodeManager aiFlowToNodeManager;
    
    private final WorkFlowNodeBOCahche workFlowNodeBOCahche;

    public FlowNodeMapResult buildMaps() {
        Map<String, List<WorkflowNodeConfig>> campMap = Maps.newConcurrentMap();
        Map<String, List<WorkflowNodeConfig>> flowCodeMap = Maps.newConcurrentMap();

        aiFlowManager.list(true).stream().forEach(aiFlow -> {
            String flowCode = aiFlow.getCode();
            String campCode = aiFlow.getCampaignCode();

            List<AiFlowToNode> nodes = aiFlowToNodeManager.listByAiFlowCode(flowCode);
            List<WorkflowNodeConfig> allVoiceNodes = Lists.newArrayList();
            List<WorkflowNodeConfig> preprocessNodes = Lists.newArrayList();

            for (AiFlowToNode node : nodes) {
                WorkFlowNode aiFlowNodeBO =
                        workFlowNodeBOCahche.getNodeByCode(node.getAiFlowNodeCode());
                if (Objects.nonNull(aiFlowNodeBO) && Objects
                        .nonNull(aiFlowNodeBO.getWorkflowNodeConfig().getVoiceJsonConfig())) {
                    allVoiceNodes.add(aiFlowNodeBO.getWorkflowNodeConfig()); // 所有配置了语音的都加入 flowMap
                                                                             // 使用
                    if (aiFlowNodeBO.getWorkflowNodeConfig().getVoiceJsonConfig()
                            .isPreprocessFlag()) {
                        preprocessNodes.add(aiFlowNodeBO.getWorkflowNodeConfig()); // 只加入需要预处理的供
                                                                                   // campMap 使用
                    }
                }
            }

            // flowCode 分组不需要过滤 preprocessFlag，去重即可
            if (!allVoiceNodes.isEmpty()) {
                List<WorkflowNodeConfig> unique = new ArrayList<>(allVoiceNodes.stream()
                        .collect(Collectors.toMap(n -> n.getVoiceJsonConfig().hash(), n -> n,
                                (n1, n2) -> n1))
                        .values())
                                .stream()
                                .sorted(Comparator
                                        .comparingInt(n -> n.getVoiceJsonConfig().getSort()))
                                .collect(Collectors.toList());
                flowCodeMap.put(flowCode, unique);
            }

            // campCode 分组需要过滤 preprocessFlag
            if (StringUtils.isNotBlank(campCode) && !preprocessNodes.isEmpty()) {
                List<WorkflowNodeConfig> uniquePre =
                        new ArrayList<>(preprocessNodes.stream().collect(Collectors
                                .toMap(n -> n.getVoiceJsonConfig().hash(), n -> n, (n1, n2) -> n1))
                                .values());
                campMap.put(campCode, uniquePre);
            }
        });
        return new FlowNodeMapResult(campMap, flowCodeMap);
    }

    @Getter
    @AllArgsConstructor
    public static class FlowNodeMapResult {
        private final Map<String, List<WorkflowNodeConfig>> campMap;
        private final Map<String, List<WorkflowNodeConfig>> flowCodeMap;
    }
}
