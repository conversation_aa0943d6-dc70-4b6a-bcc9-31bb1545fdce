/*
 * @Author: shunhua
 * 
 * @Date: 2025-04-01 16:12:47
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-01 11:33:58
 * 
 * @Description: 订阅预测呼叫列表 判断该预测外呼是否需要提前缓存自定义变量声音
 */
package com.wacai.loan.counselor.opt.base.hermes;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson2.JSON;
import com.google.common.base.Charsets;
import com.wacai.hermes.bridge.schema.resp.Message;
import com.wacai.hermes.spring.annotation.EnableBootHermes;
import com.wacai.hermes.spring.annotation.SpringConsumer;
import com.wacai.loan.counselor.opt.base.hermes.dto.PredictCallingListDTO;
import com.wacai.loan.counselor.opt.flow.cache.WorkFlowLoadCache;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@EnableBootHermes(autoStart = true)
public class PredictCallingListDrcConsumer {

    private static ExecutorService executor = Executors.newFixedThreadPool(10);

    @Resource
    private PredictCallingListService drcPredictCallingListService;

    @Resource
    private WorkFlowLoadCache workFlowLoadCache;

    @SpringConsumer(topic = "link.predict.calling.list.ai",
            group = "counselor_service_predict_calling_list.group")
    public void syncDrcConsumer(List<Message> messages) {
        messages.stream().forEach(x -> {
            executor.execute(() -> {
                String message = new String(x.getValue(), Charsets.UTF_8);
                try {
                    PredictCallingListDTO callingListDTO =
                            JSON.parseObject(message, PredictCallingListDTO.class);

                    if (Objects.isNull(callingListDTO)
                            || StringUtils.isEmpty(callingListDTO.getUserData())) {
                        return;
                    }
                    List<WorkflowNodeConfig> workflowNodeConfigs =
                            workFlowLoadCache.getByCampCode(callingListDTO.getCampKey());

                    if ("AI".equalsIgnoreCase(callingListDTO.getListType())) {
                        drcPredictCallingListService.preprocessVoice(callingListDTO,
                                workflowNodeConfigs);
                    }
                } catch (Exception ex) {
                    log.error("DrcConsumer msg : 【filed:{}】 exception ", message, ex);
                }
            });
        });
    }
}
