/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-06 16:25:20
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-08 15:47:29
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.core;


import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Getter
@AllArgsConstructor
@Slf4j
public class WorkFlowNode {

  private final WorkflowNodeConfig workflowNodeConfig;

  public void setCurrentNode(String callId) {
    CallStateManager.getState(callId).setCurrentNode(this);
    CallStateManager.getState(callId).addWorkFlowList(workflowNodeConfig.getWorkNodeCode());
  }

}
