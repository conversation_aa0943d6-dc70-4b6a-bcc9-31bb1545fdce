package com.wacai.loan.counselor.opt.sip.sipsession;

import static com.wacai.loan.counselor.opt.base.util.CounselorFileUtils.getTempDir;
import static org.pjsip.pjsua2.pjmedia_file_player_option.PJMEDIA_FILE_NO_LOOP;
import static org.pjsip.pjsua2.pjmedia_type.PJMEDIA_TYPE_AUDIO;
import static org.pjsip.pjsua2.pjsip_inv_state.PJSIP_INV_STATE_CONNECTING;
import static org.pjsip.pjsua2.pjsip_inv_state.PJSIP_INV_STATE_DISCONNECTED;
import java.io.File;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.pjsip.pjsua2.AudioMedia;
import org.pjsip.pjsua2.AudioMediaRecorder;
import org.pjsip.pjsua2.Call;
import org.pjsip.pjsua2.CallInfo;
import org.pjsip.pjsua2.CallMediaInfo;
import org.pjsip.pjsua2.CallMediaInfoVector;
import org.pjsip.pjsua2.CallOpParam;
import org.pjsip.pjsua2.OnCallMediaStateParam;
import org.pjsip.pjsua2.OnCallStateParam;
import org.pjsip.pjsua2.OnDtmfDigitParam;
import org.pjsip.pjsua2.OnDtmfEventParam;
import org.pjsip.pjsua2.OnInstantMessageParam;
import org.pjsip.pjsua2.OnInstantMessageStatusParam;
import org.pjsip.pjsua2.OnTypingIndicationParam;
import org.pjsip.pjsua2.SipHeader;
import org.pjsip.pjsua2.pjsua_call_media_status;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Supplier;
import com.wacai.loan.counselor.opt.base.AppSpringContext;
import com.wacai.loan.counselor.opt.base.state.CallState;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.flow.core.FlowDtmfHandlerService;
import com.wacai.loan.counselor.opt.flow.core.WorkFlowNode;
import com.wacai.loan.counselor.opt.flow.handler.AiFlowFactory;
import com.wacai.loan.counselor.opt.flow.model.AiFlowBO;
import com.wacai.loan.counselor.opt.sip.dataobject.dtmf.DtmfStreamBO;
import com.wacai.loan.counselor.opt.sip.handler.SipMessageInfo;
import com.wacai.loan.counselor.opt.sip.service.EndpointService;
import com.wacai.loan.counselor.opt.speech.model.AsrModel;
import com.wacai.loan.counselor.utils.ExceptionUtil;
import com.wacai.loan.counselor.utils.SimpleShortPhraseCache;
import com.wacai.loan.counselor.utils.VoiceUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

/**
 * SIP 呼叫类
 *
 * 负责处理 SIP 呼叫相关功能 纯粹的 SIP 功能，不包含业务逻辑
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Data
@EqualsAndHashCode(callSuper = false)
public class AiAgentCall extends Call {

    private AudioMediaRecorder recorder;

    private String callId;

    private AiFlowBO aiFlowBO;

    private long startTime;

    private long endTime;

    private AiFlowFactory aiFlowFactory;

    private EndpointService endpointService;

    private volatile AiAgentAudioMediaPlayer player;

    private File recorderFile;

    private AiAgentAccount account;


    private AsrModel asrModel;

    private ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(3);

    private final AtomicBoolean isDealingDtmfAtomic = new AtomicBoolean(false);

    private DtmfStreamBO dtmfStreamBO;

    private final AtomicBoolean isRunning = new AtomicBoolean(true);

    /**
     * 构造函数
     *
     * @param builder 构建器
     */
    public AiAgentCall(AiAgentAccount account, SipMessageInfo message) {
        super(account, message.getSipCallid());
        this.account = account;
        this.aiFlowFactory = AppSpringContext.getBean(AiFlowFactory.class);
        this.endpointService = AppSpringContext.getBean(EndpointService.class);
        this.callId = message.getCallUuid();
        packageFields(message);
        initAudioMediaFile(message);
    }



    /**
     * 呼叫媒体状态变更回调
     *
     * @param prm 媒体状态参数
     */
    @Override
    public void onCallMediaState(OnCallMediaStateParam prm) {
        final Pair<CallMediaInfo, AudioMedia> audioMedia = getAudioMedia();
        try {
            if (Objects.nonNull(audioMedia)) {
                final CallMediaInfo mediaInfo = audioMedia.getKey();
                final int mediaStatus = mediaInfo.getStatus();
                log.info("CallId: {} media status [{}].", callId, mediaStatus);
                final AudioMedia audMed = audioMedia.getValue();
                if (pjsua_call_media_status.PJSUA_CALL_MEDIA_ACTIVE == mediaStatus) {
                    if (Objects.nonNull(player)) {
                        log.warn("callId: {} Start transmit player [{}].", callId, player);
                        if (isActive() && hasMedia()) {
                            player.startTransmit(audMed);
                        } else {
                            log.warn(
                                    "callId: {} Call is not active or has no media, ignore transmit player",
                                    callId);
                            return;
                        }
                    }


                    if (isActive() && hasMedia()) {
                        log.info("callId: {} Start transmit recorder [{}].", callId, recorder);
                        audMed.startTransmit(recorder);
                    } else {
                        log.warn(
                                "callId: {} Call is not active or has no media, ignore transmit recorder",
                                callId);
                        return;
                    }

                } else {
                    // 媒体异常
                    log.info("callId:{} Call media is inactive, stop transmit.", callId);
                    if (Objects.nonNull(player)) {
                        log.info("callId:{} Stop transmit player [{}].", callId, player);
                        player.stopTransmit(audMed);
                    }

                    if (Objects.nonNull(recorder)) {
                        log.info("callId:{} Stop transmit recorder [{}].", callId, recorder);
                        audMed.stopTransmit(recorder);
                    }
                }
            }
        } catch (Exception ex) {
            log.error("callId:{} onCallMediaState error caused.", callId, ex);
        }
    }


    @Override
    public CallInfo getInfo() {
        try {
            return super.getInfo();
        } catch (final Exception e) {
            log.error("Get call info error caused.", e);
            throw ExceptionUtil.keepThrow(e);
        }
    }


    public Pair<CallMediaInfo, AudioMedia> getAudioMedia() {
        final CallInfo callInfo = getInfo();

        if (callInfo == null) {
            log.warn("Could not get call [{}] info.", callId);
            return null;
        }
        final CallMediaInfoVector media = callInfo.getMedia();
        log.debug("Get audio media, media size [{}].", media.size());
        // Iterate all the call medias
        for (int i = 0; i < media.size(); i++) {
            final CallMediaInfo callMediaInfo = media.get(i);
            log.debug("Call media info [{}].", callMediaInfo);
            if (callMediaInfo.getType() == PJMEDIA_TYPE_AUDIO) {
                return Pair.of(callMediaInfo, AudioMedia.typecastFromMedia(getMedia(i)));
            }
        }
        log.warn("Call [{}] no media found.", callId);

        return null;
    }

    /**
     * 呼叫状态变更回调
     *
     * @param prm 状态参数
     */
    @Override
    public void onCallState(OnCallStateParam prm) {
        try {
            CallInfo ci = getInfo();
            if (ci.getState() == PJSIP_INV_STATE_CONNECTING) {
                startTime = System.currentTimeMillis();
            } else if (ci.getState() == PJSIP_INV_STATE_DISCONNECTED) {
                log.info("Call [{}] disconnected, duration [{}]ms.", callId, getDuration());
                releaseCall();
            }
        } catch (Exception e) {
            log.error("Call [{}] state error caused.", callId, e);
        }
    }

    /**
     * DTMF数字回调
     *
     * @param prm DTMF参数
     */
    @Override
    public void onDtmfDigit(OnDtmfDigitParam prm) {
        super.onDtmfDigit(prm);
        try {
            endpointService.libRegisterThread();
            String digit = prm.getDigit();
            log.info("Call [{}] DTMF digit [{}].", callId, JSON.toJSONString(prm));
            CallState callState = CallStateManager.getState(callId);
            if (Objects.nonNull(callState)) {
                Optional<WorkFlowNode> curOpt = callState.getCurrentNode();

                if (curOpt.isPresent()) {
                    if (curOpt.get().getWorkflowNodeConfig().isTriggerInput()) {
                        handleMultiDigit(callId, curOpt.get(), digit);
                        return;
                    }
                    if (curOpt.get().getWorkflowNodeConfig().isReceiveSimpleDtmf()
                            && isDealingDtmfAtomic.compareAndSet(false, true)) {
                        handleSingleDigit(callId, curOpt.get(), digit);
                        return;
                    }
                } else {
                    log.warn("Call [{}] current node is not present.", callId);
                }
            } else {
                log.warn("Call [{}] state is not present.", callId);
            }
        } catch (Exception e) {
            log.error("Call [{}] onDtmfDigit error caused.", callId, e);
        }
    }

    private void handleSingleDigit(String callId, WorkFlowNode workFlowNode, String digit) {
        try {
            AppSpringContext.getBean(FlowDtmfHandlerService.class).dtmfHandler(callId, workFlowNode,
                    digit);
        } finally {
            isDealingDtmfAtomic.set(false);
        }
    }



    /*
     * 多个按键，以 # 结尾
     */
    private synchronized void handleMultiDigit(String callId, WorkFlowNode workFlowNode,
            String digit) {
        if (Objects.nonNull(dtmfStreamBO)) {
            dtmfStreamBO = new DtmfStreamBO();
        }
        if ("#".equals(digit)) {
            String input = dtmfStreamBO.getDtmfsStr();
            log.info("callId: {} multi digit done: {})", callId, input);
            dtmfStreamBO = null;
            AppSpringContext.getBean(FlowDtmfHandlerService.class).dtmfHandler(digit, workFlowNode,
                    input);

        } else {
            dtmfStreamBO.getDtmfs().add(digit);
            log.info("append digit to stream: {}", digit);
        }
    }



    @Override
    public void onInstantMessage(OnInstantMessageParam prm) {
        super.onInstantMessage(prm);
        log.info("CallonInstantMessage {}", JSONObject.toJSONString(prm));
    }

    @Override
    public void onInstantMessageStatus(OnInstantMessageStatusParam prm) {
        super.onInstantMessageStatus(prm);
        log.info("CallonInstantMessageStatus {}", JSONObject.toJSONString(prm));
    }

    @Override
    public void onTypingIndication(OnTypingIndicationParam prm) {
        super.onTypingIndication(prm);
        log.info("CallonTypingIndication {}", JSONObject.toJSONString(prm));
    }

    @Override
    public void xfer(final String dest, final CallOpParam prm) {
        log.info("Transfer to [{}].", dest);
        try {
            super.xfer(dest, prm);
        } catch (Exception e) {
            log.error("Call transfer [{}] error caused.", dest);
        }

    }

    /**
     * DTMF事件回调
     *
     * @param prm DTMF事件参数
     */
    @Override
    public void onDtmfEvent(OnDtmfEventParam prm) {
        log.info("Call [{}] DTMF event [{}].", callId, prm.getDigit());
    }


    public void hangup() {
        if (CallStateManager.getState(callId).isRunning() && isActive()) {
            try {
                CallStateManager.getState(callId).setRunning(false);
                endpointService.libRegisterThread(this.getClass().getSimpleName());
                CallOpParam param = new CallOpParam(true);
                SipHeader sipHeaderUserTenant = new SipHeader();
                sipHeaderUserTenant.setHName("user-tenant");
                sipHeaderUserTenant.setHValue(account.getSource());
                param.getTxOption().getHeaders().add(sipHeaderUserTenant);
                hangup(param);
            } catch (Exception e) {
                log.error("callId: {},Hangup call error caused.", callId, e);
            }
        } else {
            log.warn("callId: {} Call is not running, ignore hangup.", callId);
        }
    }


    public void answer(int code) {
        final CallOpParam param = new CallOpParam();
        param.setStatusCode(code);
        try {
            super.answer(param);
        } catch (final Exception e) {
            log.error("Call answer [{}] error caused.", param, e);
        } finally {
            param.delete();
        }
    }

    /**
     * 获取通话时长
     *
     * @return 通话时长（毫秒）
     */
    public long getDuration() {
        return endTime > 0 ? endTime - startTime : System.currentTimeMillis() - startTime;
    }


    /**
     * 初始化音频媒体文件 远端录音文件
     *
     * @param message
     */
    private void initAudioMediaFile(SipMessageInfo message) {
        String recordDir = Objects.nonNull(aiFlowBO)
                && StringUtils.isNotBlank(aiFlowBO.getAiFlow().getRecordPath())
                        ? aiFlowBO.getAiFlow().getRecordPath()
                        : getTempDir();
        recorderFile = new File(recordDir, message.getCallUuid() + ".wav");
        recorder = new AudioMediaRecorder();
        try {
            recorder.createRecorder(recorderFile.getAbsolutePath());
        } catch (Throwable e) {
            log.error("callId: {},Create recorder [{}] error caused.", message.getCallUuid(),
                    recorderFile, e);
            throw new RuntimeException(e);
        }
    }

    private void packageFields(SipMessageInfo message) {
        if (StringUtils.isNotBlank(message.getUserData())) {
            this.aiFlowBO = aiFlowFactory.buildAiFlowBO(callId, message.getUserData());
        }
        // 初始化 state
        CallStateManager.initCallState(callId);
        CallStateManager.put(callId, "mobile", message.getMobile());
    }



    public AiAgentAudioMediaPlayer createPlayer(File file) {
        try {
            AiAgentAudioMediaPlayer player = new AiAgentAudioMediaPlayer(callId, file);
            player.createPlayer(file.getAbsolutePath(), PJMEDIA_FILE_NO_LOOP);
            return player;
        } catch (final Exception e) {
            log.error("Create player [{} {}] error caused.", file, callId, e);
        }
        return null;
    }


    /**
     * 释放资源
     */
    public void releaseCall() {
        log.info("callId: {} releaseCall start ..... ", callId);
        isRunning.set(false);
        if (Objects.nonNull(asrModel)) {
            asrModel.stop();
            log.info("callId : {} asr stop ..... ", callId);
        }
        VoiceUtils.clear(callId);
        SimpleShortPhraseCache.clear(callId);
        lazyRelease();
        CallStateManager.removeState(callId);
        account.getCallContext().remove(callId);
        log.info("callId: {} releaseCall end...........", callId);
    }

    private void lazyRelease() {
        long duration = getDuration();
        if (3000 >= duration) {
            scheduler.schedule(() -> {
                try {
                    releasePjsu2();
                    log.info("callId: {} releasePjsu2 lazy 30s.", callId);
                } catch (Exception e) {
                    log.error("endpoint.libRegisterThread failed phone {}", callId, e);
                }
            }, 30, TimeUnit.SECONDS);
        } else {
            releasePjsu2();
        }
    }



    private void releasePjsu2() {
        endpointService.libRegisterThread(Thread.currentThread().getName());
        if (Objects.nonNull(recorder)) {
            recorder.delete();
        }
        if (Objects.nonNull(player)) {
            player.delete();
        }
        if (Objects.nonNull(recorderFile)) {
            // FileUtils.deleteQuietly(recorderFile);
        }
        delete();
    }


    @Override
    public boolean isActive() {
        if (isRunning.get()) {
            return super.isActive();
        }
        return false;
    }

    /**
     * 线程安全地初始化 AiFlowBO
     */
    public AiFlowBO getOrInitAiFlowBO(Supplier<AiFlowBO> initializer) {
        if (aiFlowBO == null) {
            synchronized (this) {
                if (aiFlowBO == null) {
                    aiFlowBO = initializer.get();
                }
            }
        }
        return aiFlowBO;
    }
}
