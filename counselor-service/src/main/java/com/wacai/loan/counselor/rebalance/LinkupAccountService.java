/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-30 16:26:38
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-17 10:07:42
 * 
 * @Description:
 */
package com.wacai.loan.counselor.rebalance;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.wacai.loan.counselor.opt.base.constant.EnvEnums;
import com.wacai.loan.counselor.opt.base.constant.PersonTypeEnums;
import com.wacai.loan.counselor.opt.sip.dataobject.agent.AgentDTO;
import com.wacai.loan.counselor.utils.OkHttpUtil;
import com.wacai.loan.counselor.utils.Utils;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class LinkupAccountService {


    @Value("#{'${counselor.voice.type:}'.isEmpty() ? T(java.util.Collections).emptyList() : '${counselor.voice.type:}'.split(',')}")
    private List<String> tenantCodes;

    @Value("${linkup.agent.url}")
    private String host;

    @Value("#{'${voice.robot.test.num:}'.isEmpty() ? T(java.util.Collections).emptyList() : '${voice.robot.test.num:}'.split(',')}")
    private List<String> testAccounts;

    @Value("${spring.env.active:}")
    private String env;

    @Getter
    private List<AgentDTO> accounts = Lists.newArrayList();

    @SneakyThrows
    public List<AgentDTO> listRobotAgent() {
        List<AgentDTO> lists = tenantCodes.stream().flatMap(tenantCode -> {
            String param = Utils.asUrlParams(ImmutableMap.of("tenantCode", tenantCode, "pageSize",
                    "1000", "active", "true", "type", PersonTypeEnums.ROBOT.name()));
            List<AgentDTO> list = Lists.newArrayList();
            try {
                JSONObject jsonObject = OkHttpUtil.getInstance()
                        .get(host + "/node/out-api/agents/query?" + param, Maps.newHashMap(), null,
                                new TypeReference<JSONObject>() {});
                    if ("0".equals(jsonObject.getString("code"))
                            && StringUtils.isNotBlank(jsonObject.getString("data"))) {
                        JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("data");
                        for (int i = 0; i < jsonArray.size(); i++) {
                            String type = jsonArray.getJSONObject(i).getString("type");
                            if (PersonTypeEnums.ROBOT.name().equalsIgnoreCase(type)) {
                                String account = jsonArray.getJSONObject(i).getString("telExt");
                                String password = jsonArray.getJSONObject(i).getString("telExtPwd");
                                list.add(new AgentDTO(account, account, password, tenantCode));
                            }
                        }
                    }
            } catch (Exception e) {
                log.error("List Agent tenantCode: {} Exception", tenantCode, e);
            }
            return list.stream();
        }).filter(x -> Objects.nonNull(x)).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(testAccounts)) {
            if (EnvEnums.DEV.toString().equalsIgnoreCase(env)) {
                return lists.stream().filter(x -> testAccounts.contains(x.getSipAccount()))
                        .collect(Collectors.toList());
            } else if (EnvEnums.TEST.toString().equalsIgnoreCase(env)) {
                return lists.stream().filter(x -> !testAccounts.contains(x.getSipAccount()))
                        .collect(Collectors.toList());
            } else {
                return lists;
            }
        } else {
            return lists;
        }
    }

    public Boolean allotAccount(List<AgentDTO> accounts, String host) throws IOException {
        JSONObject jsonObject =  OkHttpUtil.getInstance().post(
                StringUtils.join("http://", host, ":8080/voice/allotAccount"), Maps.newHashMap(),
                JSON.toJSONString(accounts), new TypeReference<JSONObject>() {});
            if ("0".equals(jsonObject.getString("code"))) {
                return Boolean.TRUE;
            }           
        return Boolean.FALSE;
    }

    public static List<AgentDTO> spreadTenantsBalanced(List<AgentDTO> agentList) {
        Map<String, Queue<AgentDTO>> grouped = agentList.stream().collect(Collectors
                .groupingBy(AgentDTO::getUserTenant, Collectors.toCollection(LinkedList::new)));
        List<AgentDTO> result = new ArrayList<>(agentList.size());
        List<String> tenantList = new ArrayList<>(grouped.keySet());
        while (!grouped.isEmpty()) {
            Collections.shuffle(tenantList); // 每轮打乱，避免固定顺序
            Iterator<String> iterator = tenantList.iterator();
            while (iterator.hasNext()) {
                String tenant = iterator.next();
                Queue<AgentDTO> queue = grouped.get(tenant);
                if (queue != null && !queue.isEmpty()) {
                    result.add(queue.poll());
                }
                if (queue == null || queue.isEmpty()) {
                    iterator.remove();
                    grouped.remove(tenant);
                }
            }
        }
        return result;
    }
}
