/*
 * @Author: shunhua
 * @Date: 2025-05-30 19:19:47
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-18 19:16:04
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.handler.rule;

import com.wacai.loan.counselor.opt.ai.service.OpenAIFacadeService;
import com.wacai.loan.counselor.opt.flow.model.RouterResponse;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class PatternRouter extends Router{


    @Override
    public void init(OpenAIFacadeService openAIFacadeService) {
      
    }

    @Override
    public RouterResponse actionRouter(String callId,WorkflowNodeConfig workflowNodeConfig,String phone,
            String text) {
       return null;
    }

}
