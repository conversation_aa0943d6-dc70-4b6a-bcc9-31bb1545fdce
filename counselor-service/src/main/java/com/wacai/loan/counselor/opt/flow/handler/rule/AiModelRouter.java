/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-15 11:09:53
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-18 19:16:56
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.handler.rule;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import com.alibaba.fastjson2.JSONObject;
import com.wacai.loan.counselor.opt.ai.service.OpenAIFacadeService;
import com.wacai.loan.counselor.opt.base.cache.CallUserDataCache;
import com.wacai.loan.counselor.opt.flow.model.RouterResponse;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import com.wacai.loan.counselor.utils.Utils;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class AiModelRouter extends Router {

    private String name;

    private String prompt;


    private List<SemanticsItem> semanticsItems;

    private OpenAIFacadeService openAIFacadeService;

    @Override
    public void init(OpenAIFacadeService openAIFacadeService) {
        this.openAIFacadeService=openAIFacadeService;
    }


    @Override
    public RouterResponse actionRouter(String callId,
            WorkflowNodeConfig workflowNodeConfig, String phone, String text) {
        Map<String, Object> paramMap = buildOpenAiParam(callId, phone, text);

        String result = openAIFacadeService.acquireAiModalResult(callId,
                workflowNodeConfig.getWorkNodeCode(),prompt,
                covertPrompt(paramMap, workflowNodeConfig.getTip(), text), name);
        String nextFlowCode =
                workflowNodeConfig.getSemanticsTransform().get(Utils.filterAlphanumeric(result));
        if (StringUtils.isNotEmpty(nextFlowCode)) {
            return new RouterResponse(result, nextFlowCode);
        }
        return new RouterResponse("fail", result);
    }



    private Map<String, Object> buildOpenAiParam(String callId, String phone, String text) {
        JSONObject jsonObject = CallUserDataCache.getCache(callId);
        Map<String, Object> innerMap = Maps.newHashMap();
        if (Objects.nonNull(jsonObject)) {
            innerMap = Utils.flattenJson(jsonObject);
        }
        innerMap.put("callUuid", callId);
        innerMap.put("mobile", phone);
        innerMap.put("input", text);
        return innerMap;
    }


    private static String covertPrompt(Map<String, Object> paramMap, String prompt, String input) {
        if (!prompt.contains("${")) {
            StringBuffer buffer = new StringBuffer("\n对话内容：\n");
            buffer.append("专员: ").append(prompt).append("\n");
            buffer.append("客户: ").append(input);
            return buffer.toString();
        }
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            prompt = prompt.replace("${" + entry.getKey() + "}", entry.getValue().toString());
        }
        StringBuffer buffer = new StringBuffer("\n对话内容：\n");
        buffer.append("专员: ").append(prompt).append("\n");
        buffer.append("客户: ").append(input);
        return buffer.toString();
    }

}
