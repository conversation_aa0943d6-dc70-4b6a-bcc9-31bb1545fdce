/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-18 19:19:13
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-04 11:34:59
 * 
 * @Description: 转接
 */
package com.wacai.loan.counselor.opt.flow.handler.transfer;

import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.wacai.loan.counselor.common.model.WebApiResponse;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.utils.OkHttpUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class TransferService {

    @Value("${ai-action.gateway-host}")
    private String gatewayHost;

    @Value("${link.state.url}")
    private String stateHost;

    @Value("${ai-flow-node-service.default-queue-code}")
    private String defaultQueueCode;

    @Value("${linkup.account.loader.siphost}")
    private String opensipsFs;

    /**
     * ! 获取转接目标 ? 1. 如果transferAgent不为空，则优先转接transferAgent ?
     * 2.如果transferAgent为空，则从队列中获取转接目标(队列不为空或不为默认值【1 表示这个节点需要转接逻辑】)
     * 
     * @param callId
     * @param transferAgent
     * @param tenantCode
     * @param queueCode
     * @return
     */
    public String acqTransfer(String callId, String transferAgent, String tenantCode,
            String queueCode) {

        String tranferSip = selectAgentOfState(callId, transferAgent, tenantCode);
        if (StringUtils.isEmpty(tranferSip)) {
            return selectAgentFromQueue(callId, queueCode);
        } else {
            return tranferSip;
        }
    }


    private String selectAgentOfState(String callId, String transferAgent, String tenantCode) {

        if (StringUtils.isAnyBlank(transferAgent, tenantCode)) {
            return StringUtils.EMPTY;
        }
        try {
            WebApiResponse<String> response = OkHttpUtil.getInstance().get(
                    stateHost + "/agent/transfer/availability/" + transferAgent + "/" + tenantCode,
                    Maps.newHashMap(), null, new TypeReference<WebApiResponse<String>>() {});

            log.info("callId: {} transferAgent: {}, Transfer Agent result: {}", callId,
                    transferAgent, JSON.toJSONString(response));

            if (!response.isSuccess()) {
                return null;
            }
            if (StringUtils.isNotBlank(response.getData())) {
                return String.format("sip:%s@%s", response.getData(), opensipsFs);
            } else {
                return null;
            }

        } catch (Exception e) {
            log.error("callId: {} transferAgent: {}, doPost failed: {}", callId, transferAgent,
                    e.getMessage());
        }
        return null;
    }


    private String selectAgentFromQueue(String callId, String queueCode) {

        // 如果队列代码为空或为默认值，则不进行转接
        if (StringUtils.isBlank(queueCode) || "1".equalsIgnoreCase(queueCode)) {
            return StringUtils.EMPTY;
        }

        String phone = CallStateManager.getMobile(callId);


        try {

            WebApiResponse<String> response = OkHttpUtil.getInstance().postForm(
                    gatewayHost + "/inbound/select-agent-from-queue",
                    ImmutableMap.of("queueCode", queueCode, "caller", phone),
                    new TypeReference<WebApiResponse<String>>() {});


            log.info("callId: {},queueCode: {}, phone: {}, Transfer Queue result: {}", callId,
                    queueCode, phone, JSON.toJSONString(response));
            if (!response.isSuccess()) {
                return null;
            }
            if (StringUtils.isNotBlank(response.getData())) {
                return String.format("sip:%s@%s", response.getData(), opensipsFs);
            }

        } catch (Exception e) {
            log.error("callId: {} queueCode: {}, phone: {}, doPost failed: {}", callId, queueCode,
                    phone, e.getMessage());
        }

        return null;
    }

}
