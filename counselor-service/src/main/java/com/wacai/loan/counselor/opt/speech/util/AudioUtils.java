/*
 * @Author: shunhua
 * @Date: 2025-05-04 14:57:05
 * @LastEditors: shunhua
 * @LastEditTime: 2025-05-04 15:05:02
 * @Description: 
 */
package com.wacai.loan.counselor.opt.speech.util;

import lombok.extern.slf4j.Slf4j;

import javax.sound.sampled.AudioFileFormat;
import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;

/**
 * 音频工具类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class AudioUtils {
    
    /**
     * 将PCM数据转换为WAV文件
     * 
     * @param pcmData PCM数据
     * @param outputFile 输出文件
     * @param sampleRate 采样率
     * @param channels 通道数
     * @param sampleSizeInBits 采样位数
     * @return 是否成功
     */
    public static boolean pcmToWav(byte[] pcmData, File outputFile, float sampleRate, int channels, int sampleSizeInBits) {
        try {
            AudioFormat format = new AudioFormat(sampleRate, sampleSizeInBits, channels, true, false);
            ByteArrayInputStream bais = new ByteArrayInputStream(pcmData);
            AudioInputStream ais = new AudioInputStream(bais, format, pcmData.length / format.getFrameSize());
            AudioSystem.write(ais, AudioFileFormat.Type.WAVE, outputFile);
            return true;
        } catch (IOException e) {
            log.error("Convert PCM to WAV failed", e);
            return false;
        }
    }
    
    /**
     * 获取WAV文件的时长(毫秒)
     * 
     * @param wavFile WAV文件
     * @return 时长(毫秒)
     */
    public static long getWavDuration(File wavFile) {
        try {
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(wavFile);
            AudioFormat format = audioInputStream.getFormat();
            long frames = audioInputStream.getFrameLength();
            return (long) (frames / format.getFrameRate() * 1000);
        } catch (Exception e) {
            log.error("Get WAV duration failed", e);
            return 0;
        }
    }
}
