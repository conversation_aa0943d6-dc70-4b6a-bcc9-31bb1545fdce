/*
 * @Author: shunhua
 * 
 * @Date: 2025-09-04 16:51:46
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-09-05 10:04:08
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.service.impl;

import java.io.FileInputStream;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.WebSocketSession;
import com.wacai.loan.counselor.opt.base.threadpool.ExecutorServiceHandlerRegistry;
import com.wacai.loan.counselor.opt.flow.handler.wss.DynamicWebSocketClient;
import com.wacai.loan.counselor.opt.flow.service.CallProcessor;
import com.wacai.loan.counselor.opt.flow.service.DynamicFlowService;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class DynamicFlowServiceImpl implements DynamicFlowService {

    @Qualifier("dynamicCallProcessor")
    @Resource
    private CallProcessor callProcessor;

    private static final Map<String, WebSocketSession> sessionMap = new ConcurrentHashMap<>();

    private static final ExecutorService executorService =
            ExecutorServiceHandlerRegistry.getExecutorServiceHandler("dynamic-voice-pool");

        
    @Override
    public void connectWebSocket(String callId, String mobile, String recordPath, String wsUrl) {
        try {
            DynamicWebSocketClient webSocketClient = new DynamicWebSocketClient(callId, mobile);
            ListenableFuture<?> future = webSocketClient.connect(wsUrl);
            future.addCallback(result -> {
                log.info("WebSocket connection established for callId: {}, mobile: {}", callId,
                        mobile);
                sessionMap.put(callId, webSocketClient.getSession());
                sendByteMessage(webSocketClient.getSession(), callId, recordPath);
            }, ex -> {
                log.error("WebSocket connection failed for callId: {}, mobile: {}", callId, mobile,
                        ex);
            });
        } catch (Exception ex) {
            log.error("start dynamic webSocket client error callId= {},mobile={}", callId, mobile,
                    ex);
        }
    }

    private void sendByteMessage(WebSocketSession session, String callId, String recordPath) {
        executorService.execute(() -> {
            String filePath = recordPath + "/" + callId + ".wav";
            try (final FileInputStream ins = new FileInputStream(filePath)) {
                byte[] buffer = new byte[3200]; // 3200 bytes for 100ms of audio at 16kHz, 16-bit,
                                                // mono
                final byte[] silenceData = new byte[320];
                while (true) {
                    final int readLength = ins.read(buffer);
                    if (session.isOpen()) {
                        if (readLength > 0) {
                            BinaryMessage message = new BinaryMessage(buffer);
                            session.sendMessage(message);
                        } else {
                            BinaryMessage message = new BinaryMessage(silenceData);
                            session.sendMessage(message);
                            TimeUnit.MILLISECONDS.sleep(20);
                        }
                    } else {
                        log.warn("WebSocket session is closed for callId: {}", callId);
                        break;
                    }
                }
                log.info("Finished sending audio data for callId: {}", callId);
            } catch (Exception e) {
                log.error("Error sending audio data for callId: {}", callId, e);
            } finally {

            }
        });
    }



    @Override
    public void closeWebSocket(String callId) {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'closeWebSocket'");
    }

}
