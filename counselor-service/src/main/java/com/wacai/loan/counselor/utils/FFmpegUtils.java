/*
 * @Author: shunhua
 * @Date: 2025-04-08 10:12:10
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-17 17:21:04
 * @Description: 
 */
package com.wacai.loan.counselor.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;

import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import com.wacai.loan.counselor.opt.base.constant.FlowConstants;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FFmpegUtils {


     /**
     * @param filePath       source full path file
     * @param targetFileName
     *
     * @return
     *
     * @throws IOException
     */
  
    public static File transferVoiceEncode(String filePath, String targetFileName) {
       try{
         // 设置FFmpeg命令
        String[] cmd = { "ffmpeg", "-y", "-i", filePath, "-acodec", "pcm_s16le", "-f", "wav", targetFileName };
        ProcessBuilder pb = new ProcessBuilder(cmd);
        pb.redirectErrorStream(true);
        Process process = pb.start();

        // 简单读取输出，防止阻塞
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            while (reader.readLine() != null) {} // 丢弃输出
        }

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new RuntimeException("FFmpeg 转换失败，退出码: " + exitCode);
        }
        File targetFile = new File(targetFileName);
        if (!targetFile.exists()) {
            throw new RuntimeException("目标文件未生成: " + targetFileName);
        }
        return targetFile;
       }catch (Exception e) {
           log.error("transferVoiceEncode error:", e);
          return null;
       }
    }


    public static  String getTargetName(String hash) {
        String subDir = hash.substring(0, 2); // 使用hash前两位作为子目录
        String targetDir = FlowConstants.getVariableVoicePath() + subDir + "/";
        new File(targetDir).mkdirs();
        return targetDir + hash + ".wav";
    }

     public static  void delMayExistFile(Path path) {
        try {
            Files.deleteIfExists(path);
        } catch (IOException e) {
            log.error("Files.deleteIfExists err {}",path.toString(),e);
        }
    }

    public static  void writeToFile(Path targetPath, DataBuffer dataBuffer) {
        byte[] bytes = new byte[dataBuffer.readableByteCount()];
        dataBuffer.read(bytes);
        DataBufferUtils.release(dataBuffer);

        try (FileOutputStream outputStream = new FileOutputStream(targetPath.toFile(), true)) {
            outputStream.write(bytes);
        } catch (IOException e) {
            log.error("FileOutputStream err {}", targetPath, e);
            throw new RuntimeException("Failed to write file", e);
        }

    }

    public static  void writeToFile(Path targetPath, byte[] bytes) {
        try (FileOutputStream outputStream = new FileOutputStream(targetPath.toFile(), true)) {
            outputStream.write(bytes);
        } catch (IOException e) {
            log.error("FileOutputStream err {}", targetPath, e);
            throw new RuntimeException("Failed to write file", e);
        }
    }
}
