package com.wacai.loan.counselor.opt.speech.asr;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 阿里云账号选择器
 * 根据业务参数选择合适的阿里云账号
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
public class AliyunAccountSelector {
    
    @Autowired
    private Environment environment;
    
    // 缓存业务类型与账号的映射关系
    private final Map<String, String> businessTypeAccountMap = new HashMap<>();
    
    /**
     * 根据业务类型选择账号
     * 
     * @param businessType 业务类型
     * @return 账号名称
     */
    public String selectAccountByBusinessType(String businessType) {
        if (StringUtils.isBlank(businessType)) {
            return "default";
        }
        
        // 从缓存中获取
        String accountName = businessTypeAccountMap.get(businessType);
        if (accountName != null) {
            return accountName;
        }
        
        // 从配置中获取
        String propertyKey = "speech.aliyun.account-selector.business-type." + businessType;
        accountName = environment.getProperty(propertyKey);
        
        // 如果配置中没有，使用默认账号
        if (StringUtils.isBlank(accountName)) {
            accountName = environment.getProperty("speech.aliyun.account-selector.business-type.default", "default");
        }
        
        // 缓存结果
        businessTypeAccountMap.put(businessType, accountName);
        
        log.debug("Selected account [{}] for business type [{}]", accountName, businessType);
        return accountName;
    }
    
    /**
     * 根据场景类型选择账号
     * 
     * @param sceneType 场景类型
     * @return 账号名称
     */
    public String selectAccountBySceneType(String sceneType) {
        if (StringUtils.isBlank(sceneType)) {
            return "default";
        }
        
        // 从配置中获取
        String propertyKey = "speech.aliyun.account-selector.scene-type." + sceneType;
        String accountName = environment.getProperty(propertyKey);
        
        // 如果配置中没有，使用默认账号
        if (StringUtils.isBlank(accountName)) {
            accountName = environment.getProperty("speech.aliyun.account-selector.scene-type.default", "default");
        }
        
        log.debug("Selected account [{}] for scene type [{}]", accountName, sceneType);
        return accountName;
    }
    
    /**
     * 根据自定义参数选择账号
     * 
     * @param paramName 参数名
     * @param paramValue 参数值
     * @return 账号名称
     */
    public String selectAccountByParam(String paramName, String paramValue) {
        if (StringUtils.isBlank(paramName) || StringUtils.isBlank(paramValue)) {
            return "default";
        }
        
        // 从配置中获取
        String propertyKey = "speech.aliyun.account-selector." + paramName + "." + paramValue;
        String accountName = environment.getProperty(propertyKey);
        
        // 如果配置中没有，使用默认账号
        if (StringUtils.isBlank(accountName)) {
            accountName = environment.getProperty("speech.aliyun.account-selector." + paramName + ".default", "default");
        }
        
        log.debug("Selected account [{}] for param [{}={}]", accountName, paramName, paramValue);
        return accountName;
    }
}
