/*
 * @Author: shunhua
 * 
 * @Date: 2025-04-02 10:56:22
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-07 17:27:33
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.base.hermes;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.curator.shaded.com.google.common.base.Stopwatch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Maps;
import com.wacai.loan.counselor.opt.base.alarm.AlarmService;
import com.wacai.loan.counselor.opt.base.cache.CallUserDataCache;
import com.wacai.loan.counselor.opt.base.hermes.dto.PredictCallingListDTO;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowRemoteVoiceConfig;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import com.wacai.loan.counselor.opt.flow.service.impl.RemoteVoiceServiceImpl;
import com.wacai.loan.counselor.utils.OkHttpUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class PredictCallingListService {

    @Resource
    private RemoteVoiceServiceImpl remoteVoiceService;

    private static ExecutorService executors = Executors.newFixedThreadPool(20);

    @Value("${call-user-data-cache.campaign-host}")
    private String campHost;

    @Resource
    private AlarmService alarmService;

    /**
     * ? 预处理语音
     * 
     * @param binlogDTO
     */

    public void preprocessVoice(PredictCallingListDTO dto,
            List<WorkflowNodeConfig> workflowNodeConfigs) {

        JSONObject userJsonObject = CallUserDataCache.handle(dto.getUserData());
        Object remoteVoiceObject = CallUserDataCache.getRemoteVoiceParam(userJsonObject);
        String remoteVoice = WorkFlowRemoteVoiceConfig.acqJsonData(remoteVoiceObject);

        workflowNodeConfigs.stream().forEach(x -> {
            try {
                Stopwatch stopwatch = Stopwatch.createStarted();
                File file = remoteVoiceService.acquireVoice(
                        Objects.nonNull(dto.getId()) ? dto.getId().toString()
                                : UUID.randomUUID().toString(),
                        x.getWorkNodeCode(),
                        x.getVoiceJsonConfig().buildMergedParamSmart(remoteVoice), false);
                stopwatch.stop();
                log.debug(
                        "preprocessVoice 耗时: {} 毫秒， aiFlowCode : {}, preprocess variable voice File : {}",
                        stopwatch.elapsed(TimeUnit.MILLISECONDS), x.getWorkNodeCode(),
                        Objects.isNull(file) ? "null" : file.getName());
            } catch (Exception ex) {
                log.error("aiFlowCode: {},userData: {}, preprocessVoice variable error",
                        x.getWorkNodeCode(), dto.getUserData(), ex);
                alarmService.alert(String.format("生成话术异常: \n  campCode:%s,Id :%s \n  Message: %s",
                        dto.getCampKey(), dto.getId(), ex.toString()));
            }
        });

        executors.execute(() -> campCallbackResult(dto));
    }

    /**
     * 振铃事件 Event_ring 处理远程带有变量的话术
     *
     */
    public void remoteVoiceCache(String callUUid, String userData,
            List<WorkflowNodeConfig> aiFlowNodeBOs) {

        JSONObject userJsonObject = CallUserDataCache.handle(userData);
        Object remoteVoiceObject = CallUserDataCache.getRemoteVoiceParam(userJsonObject);
        String cusParam = WorkFlowRemoteVoiceConfig.acqJsonData(remoteVoiceObject);
        List<WorkflowNodeConfig> unAiFlowBos = new ArrayList<>(
                aiFlowNodeBOs.stream().filter(node -> node.getVoiceJsonConfig() != null)
                        .collect(Collectors.toMap(node -> node.getVoiceJsonConfig().hash(),
                                node -> node, (n1, n2) -> n1))
                        .values());

        // 优先返回第一个节点
        if (!unAiFlowBos.isEmpty()) {
            WorkflowNodeConfig firstNode = unAiFlowBos.remove(0);
            try {
                log.debug("calluuid : {},[同步处理第一个] aiFlowCode : {}", callUUid, firstNode.getWorkNodeCode());
                Stopwatch stopwatch = Stopwatch.createStarted(); // 开始计时
                File file = remoteVoiceService.acquireVoice(callUUid, firstNode.getWorkNodeCode(),
                        firstNode.getVoiceJsonConfig().buildMergedParamSmart(cusParam), false);
                stopwatch.stop(); // 结束计时
                log.debug(
                        "calluuid: {},[同步处理第一个] 耗时: {} 毫秒 aiFlowCode : {}, Ring Event variable voice file : {}",
                        callUUid, stopwatch.elapsed(TimeUnit.MILLISECONDS), firstNode.getWorkNodeCode(),
                        Objects.isNull(file) ? "null" : file.getName());
            } catch (Exception e) {
                log.error("calluuid: {}, 同步处理第一个节点失败 aiFlowCode : {}, error: {}", callUUid,
                        firstNode.getWorkNodeCode(), e.getMessage(), e);
                alarmService.alert(String.format(
                        "同步处理第一个节点失败: \n callId: %s \n aiFlowCode:%s \n userData :%s \n  Message: %s",
                        callUUid, firstNode.getWorkNodeCode(), userData, e.getMessage()));
            }
        }

        unAiFlowBos.stream().forEach(x -> {
            WorkFlowRemoteVoiceConfig voiceCacheParam = x.getVoiceJsonConfig();
            executors.execute(() -> {
                try {
                    log.debug("calluuid : {},[异步处理] aiFlowCode : {}", callUUid, x.getWorkNodeCode());
                    Stopwatch stopwatch = Stopwatch.createStarted(); // 开始计时
                    File file = remoteVoiceService.acquireVoice(callUUid, x.getWorkNodeCode(),
                            voiceCacheParam.buildMergedParamSmart(cusParam), false);
                    stopwatch.stop(); // 结束计时
                    log.debug(
                            "calluuid: {}, 耗时:[异步处理]  {} 毫秒 aiFlowCode : {}, Ring Event variable voice file : {}",
                            callUUid, stopwatch.elapsed(TimeUnit.MILLISECONDS), x.getWorkNodeCode(),
                            Objects.isNull(file) ? "null" : file.getName());
                } catch (Exception ex) {
                    alarmService.alert(String.format(
                            "同步处理第一个节点失败: \n callId: %s \n aiFlowCode:%s \n userData :%s \n  Message: %s",
                            callUUid, x.getWorkNodeCode(), userData, ex.getMessage()));
                }
            });
        });
    }



    /**
     * 回执结果 无论成功提前生成变量话术，都需要修改记录状态，否则不会拨打
     */

    private void campCallbackResult(PredictCallingListDTO dto) {
        try {
            if (Objects.isNull(dto.getId())) {
                return;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("campaignKey", dto.getCampKey());
            jsonObject.put("dialId", dto.getId());
            WebResult<Object> resp = OkHttpUtil.getInstance().post(campHost + "/api/call/reset-init",Maps.newHashMap(),
                    JSON.toJSONString(jsonObject), new TypeReference<WebResult<Object>>() {});
             log.info("predictCalling id: {} callback status result : {}", dto.getId(),
                    0 == resp.getCode() ? "SUCC" : "FAIL");
            if (!resp.isSucc()) {
                alarmService
                        .alert(String.format("预测外呼活动状态修改失败: \n  campCode:%s,Id :%s \n  Message: %s",
                                dto.getCampKey(), dto.getId(), resp.getError()));
            }
        } catch (Exception ex) {
            log.error("predictCalling id: {},callback status Exception ", dto.getId(), ex);
            alarmService.alert(String.format("预测外呼活动状态修改异常: \n  campCode:%s,Id :%s \n  Message: %s",
                    dto.getCampKey(), dto.getId(), ex.toString()));
        }
    }
}
