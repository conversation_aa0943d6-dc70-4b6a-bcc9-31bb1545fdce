/*
 * @Author: shunhua
 * @Date: 2025-08-13 19:40:46
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-22 17:31:08
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.handler.pattern;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.data.annotation.Transient;
import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
@NoArgsConstructor
public class NextNodeCodeAdapter {

    private List<String> suitWords;

    private String semantics;

    @JsonIgnore
    @Transient
    private List<Pattern> patterns;

    public NextNodeCodeAdapter(List<String> suitWords, String semantics) {
        this.suitWords = suitWords;
        this.semantics = semantics;
    }

    public void initProps() {

        if (CollectionUtils.isNotEmpty(suitWords)) {
            patterns = suitWords.stream().map(Pattern::compile).collect(Collectors.toList());
        }
    }

    public static List<NextNodeCodeAdapter> builderNextNodeAdapters(String nextNodeCodeAdapters) {
        if (StringUtils.isNotEmpty(nextNodeCodeAdapters)) {
            List<NextNodeCodeAdapter> nextNodeCodeAdapterLists =
                    JSON.parseArray(nextNodeCodeAdapters, NextNodeCodeAdapter.class);
            nextNodeCodeAdapterLists.forEach(NextNodeCodeAdapter::initProps);
            return nextNodeCodeAdapterLists;
        } else {
            return Lists.newArrayList();
        }
    }

    public boolean isSuit(String callId, String input) {
        final boolean anyMatch = patterns.stream().anyMatch(p -> {
            try {
                Matcher matcher = p.matcher(input);
                if (matcher.find()) {
                    log.info("【Pattern匹配成功】callId: {} | input: {} | pattern: {} | flowNodeCode: {}",
                            callId, input, p, semantics);
                    return true;
                }
            } catch (Exception e) {
                log.error("【Pattern匹配异常】callId: {} | input: {} | pattern {} | error: {}", callId,
                        input, p, e.getMessage());
                return false;
            }
            return false;
        });

        return anyMatch;
    }

    public String queryFlowNodeCode(Map<String, String> semanticsTransform) {
        final String semantics = this.semantics;

        // semantics Transform
        if (StringUtils.isNotBlank(semantics) && MapUtils.isNotEmpty(semanticsTransform)) {
            final String code = semanticsTransform.get(semantics);
            if (StringUtils.isNotBlank(code)) {
                return code;
            }
        }
        return null;
    }
}
