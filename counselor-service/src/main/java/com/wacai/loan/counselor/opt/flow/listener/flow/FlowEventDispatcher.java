/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-22 17:39:33
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-05 10:44:32
 * 
 * @Description: SIP 事件分发器
 */
package com.wacai.loan.counselor.opt.flow.listener.flow;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.wacai.loan.counselor.opt.base.alarm.AlarmService;
import com.wacai.loan.counselor.opt.base.alarm.AlarmTemplateCst;
import com.wacai.loan.counselor.opt.base.alarm.NotifierContext;
import com.wacai.loan.counselor.opt.base.constant.AiFlowTypeEnums;
import com.wacai.loan.counselor.opt.base.dto.DialEventDTO;
import com.wacai.loan.counselor.opt.base.event.SipEvent;
import com.wacai.loan.counselor.opt.flow.handler.AiFlowFactory;
import com.wacai.loan.counselor.opt.flow.model.AiFlowBO;
import com.wacai.loan.counselor.opt.sip.cache.CallContext;
import com.wacai.loan.counselor.opt.sip.sipsession.AiAgentCall;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class FlowEventDispatcher {

    @Resource
    private List<AbstractFlowEventListener> flowEventListeners;

    private final Map<AiFlowTypeEnums, AbstractFlowEventListener> listenerMap = Maps.newHashMap();


    @Resource
    private CallContext callContext;

    @Resource
    private AiFlowFactory aiFlowFactory;

    @Resource
    private AlarmService alarmService;

    @PostConstruct
    public void init() {
        for (AbstractFlowEventListener listener : flowEventListeners) {
            listenerMap.put(listener.getSupportedFlowType(), listener);
            log.info("注册监听器类型: {}", listener.getSupportedFlowType());
        }
    }

    private AbstractFlowEventListener getListenerByCallId(DialEventDTO eventDTO) {
        try {
            AiAgentCall call = callContext.get(eventDTO.getUuid());
            if (Objects.isNull(call)) {
                log.warn("callId: {} 不存在", eventDTO.getUuid());
                return null;
            }
            AiFlowBO flowBO =
                    call.getOrInitAiFlowBO(() -> aiFlowFactory.getDialEventDTO(eventDTO.getUuid(),
                            eventDTO.getUserData(), eventDTO.getCampCode()));

            if (Objects.isNull(flowBO)) {
                log.error("callId {} aiFlowBO is null", eventDTO.getUuid());
                alarmService.alert(NotifierContext.builder().content(
                        String.format(AlarmTemplateCst.AI_FLOW_ERROR, JSON.toJSONString(eventDTO)))
                        .build());
                return null;
            }
            AiFlowTypeEnums type = AiFlowTypeEnums.fromCode(flowBO.getAiFlow().getType());
            return listenerMap.get(type);
        } catch (Exception e) {
            log.warn("callId: {} 获取类型异常: ", eventDTO.getUuid(), e);
            return null;
        }
    }

    @Async
    @EventListener
    public void onRingEvent(SipEvent.RingEvent event) {
        dispatch(event.getEventDTO(), FlowEventType.RING);
    }

    @Async
    @EventListener
    public void onAnswerEvent(SipEvent.AnswerEvent event) {
        dispatch(event.getEventDTO(), FlowEventType.ANSWER);
    }

    /**
     * 处理挂断事件 不用
     * 
     * @param event
     */
    public void onHangupEvent(SipEvent.HangupEvent event) {
        dispatch(event.getEventDTO(), FlowEventType.HANGUP);
    }

    private void dispatch(DialEventDTO eventDTO, FlowEventType type) {
        AbstractFlowEventListener listener = getListenerByCallId(eventDTO);
        if (listener == null) {
            log.warn("未找到处理该类型的监听器 callId: {}", eventDTO.getUuid());
            return;
        }
        try {
            switch (type) {
                case RING:
                    listener.handleRingEvent(eventDTO);
                    break;
                case ANSWER:
                    listener.handleAnswerEvent(eventDTO);
                    break;
                case HANGUP:
                    listener.handleHangupEvent(eventDTO);
                    break;
            }
        } catch (Exception e) {
            log.error("调用监听器处理异常，callId: {}, type: {}, err: {}", eventDTO.getUuid(), type,
                    e.getMessage(), e);
        }
    }

    enum FlowEventType {
        RING, ANSWER, HANGUP
    }
}
