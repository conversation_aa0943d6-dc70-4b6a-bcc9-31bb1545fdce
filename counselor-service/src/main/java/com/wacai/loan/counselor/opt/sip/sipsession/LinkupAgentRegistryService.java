/*
 * @Author: shunhua
 * 
 * @Date: 2025-06-06 11:10:07
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-10 19:17:33
 * 
 * @Description:
 */
/*
 * @Author: shunhua
 * 
 * @Date: 2025-06-06 11:10:07
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-06-06 14:03:59
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.sip.sipsession;

import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.wacai.loan.counselor.common.model.WebApiResponse;
import com.wacai.loan.counselor.opt.base.alarm.AlarmService;
import com.wacai.loan.counselor.opt.base.alarm.NotifierContext;
import com.wacai.loan.counselor.opt.base.event.AccountRegistryStateEvent;
import com.wacai.loan.counselor.opt.sip.dataobject.agent.AgentDTO;
import com.wacai.loan.counselor.utils.GlobalAlarmLimiter;
import com.wacai.loan.counselor.utils.OkHttpUtil;
import com.wacai.loan.counselor.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class LinkupAgentRegistryService {

    @Resource
    private AiAgentRegistryService aiAgentRegistryService;

    @Resource
    private AiAgentSipService aiAgentSipService;

    @Value("${checkin.interval.minutes:5}")
    private long intervalMinutes;


    @Value("${link.state.url}")
    private String stateDomain;

    @Resource
    private AlarmService alarmService;

    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(
            new ThreadFactoryBuilder().setNameFormat("checkin-scheduler-%d").build());



    @PostConstruct
    public void init() {
        scheduler.scheduleAtFixedRate(() -> {
            try {
                List<AgentDTO> agents = aiAgentRegistryService.getLocalAccount();
                agents.forEach(agent -> {
                    Pair<Boolean, String> pair = existLinkupAgentState(agent.getSipAccount());
                    if (pair.getLeft()) {
                        checkinLinkupAgent(agent.getSipAccount(), pair.getRight());
                    }
                });
            } catch (Exception e) {
                log.error("Error during scheduled check-in", e);
            }
        }, intervalMinutes, intervalMinutes, java.util.concurrent.TimeUnit.MINUTES);
    }

    @EventListener(classes = AccountRegistryStateEvent.class)
    public void onAccountRegister(final AccountRegistryStateEvent event) {
        final String account = event.getAccount();
        log.info("Account {} registry state changed, source: {}, code: {}", account,
                event.getSource(), event.getCode());
        if (200 != event.getCode()) {
            log.error("Account {} registry failed, source: {}, code: {}", account,
                    event.getSource(), event.getCode());
            aiAgentSipService.manualRegister(
                    aiAgentRegistryService.getRobotAccounts(Lists.newArrayList(account)));
        } else {
            Mono.fromRunnable(() -> {
                Pair<Boolean, String> pair = existLinkupAgentState(account);
                if (pair.getLeft()) {
                    checkinLinkupAgent(account, pair.getRight());
                }
            }).delaySubscription(Duration.ofSeconds(5)).subscribe();
        }
    }


    private Pair<Boolean, String> existLinkupAgentState(String num) {
        WebApiResponse<JSONObject> response =
                OkHttpUtil.getInstance().get(stateDomain + "/agent/state/" + num, Maps.newHashMap(), null,
                        new TypeReference<WebApiResponse<JSONObject>>() {});
        if (response.isSuccess()) {
            JSONObject data = response.getData();
            if (Objects.nonNull(data)) {
                Integer status = data.getInteger("status");
                if (Objects.isNull(status)) {
                    return Pair.of(false, null);
                }
                if (status != 0 && status != 7) {
                    log.info("num {} is in status {} already, check in not done", num, status);
                    return Pair.of(false, null);
                } else {
                    return Pair.of(true, data.getString("freeSwitch"));
                }
            }
        }
        return Pair.of(false, null);
    }

    private void checkinLinkupAgent(String num, String freeSwitch) {
        try {
            WebApiResponse<Boolean> response = OkHttpUtil.getInstance().post(
                    Utils.packageUrl(freeSwitch) + "/agent/hard-phone/checkin/" + num,
                    Maps.newHashMap(), "{}", new TypeReference<WebApiResponse<Boolean>>() {});
            if (response.isSuccess()) {
              //  log.info("Agent {} check-in successful, freeSwitch: {}", num, freeSwitch);
            } else {
                log.error("Agent {} check-in failed, freeSwitch: {}, response: {}", num, freeSwitch,
                        response);
                if (GlobalAlarmLimiter.shouldAlert()) {
                    alarmService.alert(NotifierContext.builder()
                            .content(String.format(
                                    "【批量告警】Agent check-in 出现失败，freeSwitch: %s，请检查部署或注册状态",
                                    freeSwitch))
                            .build());
                }
            }
        } catch (Exception e) {
            log.error("Error during agent check-in for {}", num, e);
        }
    }

}
