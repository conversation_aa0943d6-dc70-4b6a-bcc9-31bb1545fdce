/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-15 19:18:05
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-17 17:23:47
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.ai.service;

import static com.wacai.loan.counselor.opt.base.alarm.AlarmTemplateCst.OPENAI_ERROR;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.base.Stopwatch;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableMap;
import com.wacai.loan.counselor.opt.ai.model.OpenAiRequest;
import com.wacai.loan.counselor.opt.base.AppSpringContext;
import com.wacai.loan.counselor.opt.base.alarm.AlarmService;
import com.wacai.loan.counselor.opt.base.alarm.NotifierContext;
import com.wacai.loan.counselor.utils.OkHttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2024-07-04 18:00
 */

@Slf4j
public class OpenAiChatService {


    private final static Cache<String, String> aiCache = CacheBuilder.newBuilder().build();

    public static String getOpenChat(String callUUid, String flowCode, String systemPrompt,
            String userPrompt, String openAiUrl, String apiKey) {
        String result = "";
        try {
            String cacheResult = aiCache.getIfPresent(flowCode + ":" + userPrompt);
            if (StringUtils.isNotEmpty(cacheResult)) {
                log.info("Cache hit, call-uuid: {}, flowCode : {}, content: {}, result: {}",
                        callUUid, flowCode, userPrompt, cacheResult);
                return cacheResult;
            }
            Stopwatch stopwatch = Stopwatch.createStarted();
            String message = JSON.toJSONString(new OpenAiRequest(
                    Arrays.asList(new OpenAiRequest.Message("system", systemPrompt),
                            new OpenAiRequest.Message("user", userPrompt))));
                result = OkHttpUtil.getInstance().post(openAiUrl,
                    ImmutableMap.of("api-key", apiKey), message,new TypeReference<String>() {});
            String doResult = coverHsResult(result);
            log.info(
                    " Processed request successfully, call-uuid: {}, flowCode : {}, context: {}, result: 【{}】, CostTime: {} 毫秒",
                    callUUid, flowCode, userPrompt, doResult,
                    stopwatch.elapsed(TimeUnit.MILLISECONDS));
            return doResult;
        } catch (IllegalStateException ex) {
            log.error(
                    "IllegalStateException processing request with  call-uuid: {}, flowCode : {}, context: {}, result : {} ",
                    callUUid, flowCode, userPrompt, result);
            AppSpringContext.getBean(AlarmService.class).alert(
                    NotifierContext.builder().content(String.format(OPENAI_ERROR, result)).build());
        } catch (Exception e) {
            log.error(
                    "Error processing request with  call-uuid: {}, flowCode : {}, context: {}, result : {} ",
                    callUUid, flowCode, userPrompt, result, e);
            AppSpringContext.getBean(AlarmService.class).alert(NotifierContext.builder()
                    .content(String.format(OPENAI_ERROR, e.getMessage())).build());
        }
        return "fail";
    }

    private static String coverHsResult(String response) {
        JSONObject jsonObject = JSON.parseObject(response);
        if (Objects.isNull(jsonObject.getJSONArray("choices"))) {
            throw new IllegalStateException();
        }
        // 取得 choices 数组中的第一个对象
        JSONObject firstChoice = jsonObject.getJSONArray("choices").getJSONObject(0);

        // 从 firstChoice 中取得 message 对象
        JSONObject message = firstChoice.getJSONObject("message");

        // 从 message 对象中获取 content 的值
        String content = message.getString("content");
        return content;
    }
}
