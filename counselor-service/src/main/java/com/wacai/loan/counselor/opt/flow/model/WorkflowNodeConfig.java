/*
 * @Author: shunhua
 * @Date: 2025-05-06 16:39:23
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-08 15:28:17
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.model;

import java.util.List;
import java.util.Map;

import javax.script.Invocable;

import org.springframework.expression.Expression;

import com.wacai.loan.counselor.opt.flow.config.WorkFlowRemoteVoiceConfig;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowRepeatTimesLimitConfig;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowTransferLimitConfig;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowWaitVoiceConfig;
import com.wacai.loan.counselor.opt.flow.handler.pattern.NextNodeCodeAdapter;
import com.wacai.loan.counselor.opt.flow.handler.rule.Router;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;


@Data
@Builder
public class WorkflowNodeConfig {

    @Getter
    private  static final ClassLoader ctxtLoader = Thread.currentThread().getContextClassLoader();

    private String workNodeCode;

    private String tenantCode;

    private String tip;

    private String voicePath;

    private boolean triggerInput;

    private boolean end;

    private boolean receiveSimpleDtmf;

    // 远程语音配置
    private WorkFlowRemoteVoiceConfig voiceJsonConfig;

    //节点 action 动作
    private WorkFlowRemoteVoiceConfig nodeActionJsonConfig;
    
    //等待话术播放配置
    private WorkFlowWaitVoiceConfig waitVoiceCfg;

    //节点播放次数限制配置
    private WorkFlowRepeatTimesLimitConfig repeatTimesLimitConfig;

    // 语义转换
    private Map<String, String> semanticsTransform;

    private Invocable invocable;

    private  Expression tipExpression;

    private Router router;

    private List<NextNodeCodeAdapter> nextNodeCodeAdapters;

    /**
     * 转接限制配置
     */
    private WorkFlowTransferLimitConfig transferLimitConfig;

}
