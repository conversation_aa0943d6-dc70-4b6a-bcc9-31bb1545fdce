package com.wacai.loan.counselor.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <br>
 * <EMAIL> <br>
 * Jul 25, 2018 18:03
 */
@Slf4j
@Deprecated
public class SipMessageUtils {
    private static final Pattern CALL_UUID_PATTERN =
        Pattern.compile("X-Genesys-CallUUID:\\s+([\\w\\-]+)");
    private static final Pattern CALL_ID_PATTERN = Pattern.compile("Call-ID:\\s+([\\w\\-]+)");
    private static final Pattern USER_DATA_PATTERN = Pattern.compile("X-User-Data:\\s*(\\S+)\\s*");
    private static final Pattern X_CALL_NUMBER = Pattern.compile("X-Call-Number:\\s+([\\w\\-]+)");
    private static final Pattern X_INBOUND_CHANNEL_UUID =
            Pattern.compile("X-linkup-inbound-channel-uuid:\\s+([\\w\\-]+)");
    private static final Pattern FROM_DN_PATTERN =
        Pattern.compile("From: \"Extension \\d+\" <sip:(\\d+)@.*?>;tag=.*");

    private static final Pattern INVITE_DN_PATTERN =
        Pattern.compile("INVITE\\s+sip:([^@]+)");
    private static final Pattern CONTACT_PATTERN =
        Pattern.compile("Contact:\\s+\\<sip:([^@]+)");
    private static final Pattern DIVERSION_PATTERN =
        Pattern.compile("Diversion:\\s+\\<\\w+:([^>]+)\\>");

    /**
     * extract call uuid from
     * INVITE sip:7202@************:45061;ob SIP/2.0
     * From: <sip:906513524700973@************:5060>;tag=00849EA0-0616-1CB0-86B4-0B2E10ACAA77-458539198
     * To: <sip:3147@************:5060>
     * Call-ID: 00849E8C-0616-1CB0-86B4-0B2E10ACAA77-373737041@************
     * CSeq: 1 INVITE
     * Content-Length: 0
     * Via: SIP/2.0/UDP ************:5060;branch=z9hG4bK00849EAA-0616-1CB0-86B4-0B2E10ACAA77-576554611
     * Contact: <sip:850313524700973@************:5060>
     * X-Genesys-CallInfo: routed
     * Allow: ACK, BYE, CANCEL, INFO, INVITE, MESSAGE, NOTIFY, OPTIONS, PRACK, REFER, UPDATE
     * Max-Forwards: 70
     * X-Genesys-CallUUID: 02B3EK062OEB11LK1CN11B5AES2JA25M
     * Session-Expires: 1800;refresher=uac
     * Min-SE: 90
     * Supported: uui,100rel,timer
     * @return
     */
    public static String extractCallUuid(final String sipMessage) {
        log.debug("Extract call uuid from sip message [{}].", sipMessage);
        return extractPattern(sipMessage, CALL_UUID_PATTERN);
    }

    public static String extractCallId(final String sipMessage) {
        log.debug("Extract call id from sip message [{}].", sipMessage);
        return extractPattern(sipMessage, CALL_ID_PATTERN);
    }

    public static String extractUserData(final String sipMessage) {
        log.debug("Extract userData from sip message [{}].", sipMessage);
        return extractPattern(sipMessage, USER_DATA_PATTERN);
    }

    /**
     * extract from dn
     * @param sipMessage sip message
     * @return from dn
     */
    public static String extractFromDn(final String sipMessage) {
        log.debug("Extract From sip message [{}].", sipMessage);
        return extractPattern(sipMessage, FROM_DN_PATTERN);
    }

    /**
     * extract invite DN
     * @param sipMessage sip message
     * @return to dn
     */
    public static String extractInviteDn(final String sipMessage) {
        log.debug("Extract To sip message [{}].", sipMessage);
        return extractPattern(sipMessage, INVITE_DN_PATTERN);
    }

    /**
     * extract contact dn, contact dn == trunk + from_dn
     * @param sipMessage sip message
     * @return from contact dn
     */
    public static String extractContactDn(final String sipMessage) {
        log.debug("Extract Contact sip message [{}].", sipMessage);
        return extractPattern(sipMessage, CONTACT_PATTERN);
    }

    public static String getCallNum(final String sipMessage) {
        log.debug("Extract Contact sip message [{}].", sipMessage);
        return extractPattern(sipMessage, X_CALL_NUMBER);
    }

    /**
     * extract diversion
     * "Diversion: <tel:18868110711>;reason=unknown;counter=1;screen=no;privacy=off\n"
     * @param sipMessage
     * @return
     */
    public static String extractDiversion(final String sipMessage) {
        log.debug("Extract diversion from sip message [{}].", sipMessage);
        return extractPattern(sipMessage, DIVERSION_PATTERN);
    }

    /**
     * extract inboundChannelUuid
     * "X-linkup-inbound-channel-uuid: 0d14e227-483f-487d-be62-4622722bb766\n"
     *
     * @param sipMessage
     *
     * @return
     */
    public static String extractInboundChannelUuid(final String sipMessage) {
        log.debug("Extract diversion from sip message [{}].", sipMessage);
        return extractPattern(sipMessage, X_INBOUND_CHANNEL_UUID);
    }

    /**
     * X-Call-Type: PREDICTION
     *
     * @param sipMessage
     *
     * @return
     */
    public static String extractCallType(final String sipMessage) {
        log.debug("Extract diversion from sip message [{}].", sipMessage);
        return extractPattern(sipMessage, Pattern.compile("X-Call-Type:\\s+(\\w+)"));
    }

    private static String extractPattern(final String sipMessage, Pattern pattern) {
        final Matcher matcher = pattern.matcher(sipMessage);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}
