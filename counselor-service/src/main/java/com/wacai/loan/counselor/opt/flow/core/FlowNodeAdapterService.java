/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-25 17:28:04
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-01 14:30:41
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.core;

import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.opt.flow.handler.pattern.PatternActionContainer;
import com.wacai.loan.counselor.opt.flow.model.NodeAdapterDTO;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import com.wacai.loan.counselor.opt.flow.service.NodeProcessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class FlowNodeAdapterService {

    private final NodeProcessor nodeProcessor;

    public NodeAdapterDTO adapterFlowNode(String callId, WorkflowNodeConfig workflowNodeConfig,
            String text) {

        // NextCodeAdapter 优先适配
        if (CollectionUtils.isNotEmpty(workflowNodeConfig.getNextNodeCodeAdapters())) {
            String nextFlowCode = PatternActionContainer.findFlowNodeCode(callId, text,
                    workflowNodeConfig.getSemanticsTransform(),
                    workflowNodeConfig.getNextNodeCodeAdapters());
            if (StringUtils.isNotEmpty(nextFlowCode)) {
                return new NodeAdapterDTO(nextFlowCode, nextFlowCode);
            }
        }
        if (Objects.nonNull(workflowNodeConfig.getRouter())) {
            return nodeProcessor.adapterTranferFlowNode(callId, workflowNodeConfig, text);
        }

        if (Objects.nonNull(workflowNodeConfig.getInvocable())) {
            return nodeProcessor.adapterScriptFlowNode(callId, workflowNodeConfig, text);
        }
        return new NodeAdapterDTO();
    }


    public Boolean adapterRepeatTimesLimit(String callId,
            WorkFlowNode workFlowNode) {
        return nodeProcessor.flowNodeRepeatTimesLimit(callId, workFlowNode.getWorkflowNodeConfig());
    }
}
