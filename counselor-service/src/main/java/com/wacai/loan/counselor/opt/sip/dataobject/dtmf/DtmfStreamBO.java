/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-18 17:48:36
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-18 17:48:38
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.sip.dataobject.dtmf;

import java.util.List;
import java.util.stream.Collectors;
import com.google.common.collect.Lists;
import lombok.Data;

@Data
public class DtmfStreamBO {
    private List<String> dtmfs = Lists.newArrayListWithExpectedSize(20);

    public String getDtmfsStr() {
        return dtmfs.stream().collect(Collectors.joining());
    }
}
