package com.wacai.loan.counselor.dao.po;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.wacai.loan.counselor.opt.base.jsonformat.JsonDataFormat;
import lombok.Data;

/**
 * ai流程
 *
 * @TableName ai_flow
 */
@Table(name = "ai_flow")
@Data
@Entity
public class AiFlow{
    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 流程首个节点
     */
    @Column(name = "first_flow_node_code")
    private String firstFlowNodeCode;

    /**
     * 备注
     */
    private String comment;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    @JsonDataFormat
    private Date createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    @JsonDataFormat
    private Date updatedTime;

    @Column(name = "call_event_call_back_url")
    private String callEventCallBackUrl;

    @Column(name = "not_receive_voice_input")
    Boolean notReceiveVoiceInput;

    @Column(name = "record_path")
    String recordPath;

    @Column(name = "wait_voice")
    String waitVoice;

    @Column(name = "voice_end_call_back_url")
    String voiceEndCallBackUrl;

    private String context;

    /**
     * 类型：normal，dynamic
     */
    private String type;

    /**
     * 已发布
     */
    private Boolean released;

    /**
     * 活动code
     */
    @Column(name = "campaign_code")
    private String campaignCode;

    /**
     * 业务线编码
     */
    @Column(name = "tenant_code")
    private String tenantCode;

    /**
     * 创建人
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * ui数据
     */
    @Column(name = "ui_data")
    private String uiData;

}