/*
 * @Author: shunhua
 * @Date: 2025-05-04 14:53:39
 * @LastEditors: shunhua
 * @LastEditTime: 2025-05-04 15:12:52
 * @Description: 
 */
package com.wacai.loan.counselor.opt.base.threadpool.impl;

import java.util.concurrent.ExecutorService;
import com.wacai.loan.counselor.opt.base.threadpool.ExecutorServiceHandler;
import com.wacai.loan.counselor.opt.base.threadpool.ExecutorServiceObject;

/**
 * 
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2019-03-21 18:57
 */
public final class DefaultExecutorServiceHandler implements ExecutorServiceHandler {
    @Override
    public ExecutorService createExecutorService(String type) {
        return new ExecutorServiceObject("inner-executor-" + type, Runtime.getRuntime().availableProcessors() * 2 + 1)
                .createExecutorService();
    }

    @Override
    public ExecutorService createExecutorService(int coreNum, String type) {
        return new ExecutorServiceObject("inner-executor-" + type,
                coreNum).createExecutorService();
    }
}
