/*
 * @Author: shunhua
 * @Date: 2025-06-06 11:12:25
 * @LastEditors: shunhua
 * @LastEditTime: 2025-06-06 11:12:26
 * @Description: 
 */
package com.wacai.loan.counselor.opt.base.event;

import java.io.Serializable;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

@Data
@ToString
@RequiredArgsConstructor
public class AccountRegistryStateEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    private final String account;

    private final String source;
    
    private final int code;
}
