/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-04 14:56:27
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-22 17:52:46
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.config;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMethod;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.wacai.loan.counselor.utils.MessageDigestUtils;
import com.wacai.loan.counselor.utils.Utils;
import lombok.Data;

@Data
public class WorkFlowRemoteVoiceConfig {

    @JSONField(name = "url")
   private  String url;

    @JSONField(name = "method")
    private RequestMethod method;
    /**
     * validate when post
     */
    @JSONField(name = "param")
    private String param;

    // 远程语音是否提前加载 默认 fasle
    @JSONField(name = "preprocessFlag")
    private boolean preprocessFlag;

    // 加载顺序
    @JSONField(name = "sort")
    private int sort;

    private String tip;

    private List<AiFlowNodeVoiceFallbackParam> fallbackTemplates;

    /**
     * 从 remoteVoiceObject 获取 remoteVoice（remoteVoiceParam 的 JSON 字符串形式）。 如果 remoteVoiceObject 是
     * String，直接返回；如果是 JSONObject，确保 jsonData 是字符串后序列化。
     *
     * @param remoteVoiceObject remoteVoiceParam 对象，可能是 String 或 JSONObject
     * @return remoteVoice 序列化后的 JSON 字符串，或 null 如果 remoteVoiceObject 无效
     */
    public static String acqJsonData(Object remoteVoiceObject) {
        if (Objects.isNull(remoteVoiceObject)) {
            return null;
        }
        if (remoteVoiceObject instanceof String) {
            return (String) remoteVoiceObject;
        } else {
            JSONObject jsonObject = (JSONObject) remoteVoiceObject;
            Object jsonData = jsonObject.get("jsonData");
            if (jsonData instanceof JSONObject) {
                // 如果 jsonData 是 JSON 对象，转换为字符串
                jsonObject.put("jsonData", JSON.toJSONString(jsonData));
            }
            return JSON.toJSONString(jsonObject);
        }
    }

    /**
     * 从 remoteVoice JSON 字符串构建 AiFlowNodeVoiceCacheParam 实例。 如果 param 是 JSONObject，转换为 JSON 字符串。
     *
     * @param remoteVoice 远程语音参数的 JSON 字符串
     * @return AiFlowNodeVoiceCacheParam 实例
     */
    public static WorkFlowRemoteVoiceConfig buildSelfInstance(String remoteVoice) {
        final JSONObject jsonObject = JSON.parseObject(remoteVoice);
        final Object param = jsonObject.get("param");
        if (Objects.nonNull(param) && param instanceof JSONObject) {
            jsonObject.put("param", JSON.toJSONString(param));
        }
        return JSON.to(WorkFlowRemoteVoiceConfig.class, jsonObject);
    }


    public WorkFlowRemoteVoiceConfig buildMergedParamSmart(String cusParam) {
        // 有 fallback 就自动走 fallback
        return Objects.nonNull(fallbackTemplates) && !fallbackTemplates.isEmpty()
                ? buildMergedParamCopyWithFallback(cusParam)
                : buildMergedParamCopy(cusParam);

    }



    public  WorkFlowRemoteVoiceConfig buildMergedParamCopyWithFallback(String cusParam) {
        // 默认就是 this
        WorkFlowRemoteVoiceConfig target = new WorkFlowRemoteVoiceConfig();
        // 如果配置了 fallbackTemplates，检查是否要切换
        JSONObject cusObj =
                StringUtils.isNotEmpty(cusParam) ? JSON.parseObject(cusParam) : new JSONObject();
        Map<String, Object> cusMap = Utils.flattenJson(cusObj);
        Set<String> availableKeys = cusMap.keySet();
        Optional<AiFlowNodeVoiceFallbackParam> fallbackOpt = fallbackTemplates.stream()
                // 保证 cusMap 至少包含所有要求的字段
                .filter(template -> availableKeys.containsAll(template.getContainKeys()))
                // 按 sort 优先级排序（大的优先）
                .sorted((t1, t2) -> Integer.compare(t2.getSort(), t1.getSort())).findFirst();

        if (fallbackOpt.isPresent()) {
            target.url = fallbackOpt.get().getUrl();
            target.method = this.method; // method 沿用
            target.preprocessFlag = this.preprocessFlag;
            target.sort = this.sort;
            if (method == RequestMethod.GET) {
                target.param = cusParam;
                return target;
            }
            target.tip = fallbackOpt.get().getTip();
            String baseParam = StringUtils.isNotEmpty(param) ? this.param : "{}";
            JSONObject baseParamObj = JSON.parseObject(baseParam);
            JSONObject customParamObj = JSON.parseObject(cusParam);
            baseParamObj.putAll(customParamObj);
            target.param = JSON.toJSONString(baseParamObj);
            return target;
        } else {
            return buildMergedParamCopy(cusParam);
        }
    }

    /**
     * 创建当前对象的副本，并将 param 和 customParam 合并到新对象的 param 字段。 - 对于 GET 请求，新对象的 param 设为 customParam。 -
     * 对于其他请求，将 customParam 的字段合并到 param，customParam 覆盖同名字段。
     *
     * @param customParam 自定义参数（remoteVoiceParam 的 JSON 字符串）
     * @return 新的 AiFlowNodeVoiceCacheParam 实例，包含合并后的 param
     */
    private WorkFlowRemoteVoiceConfig buildMergedParamCopy(String cusParam) {
        final WorkFlowRemoteVoiceConfig cacheParam = new WorkFlowRemoteVoiceConfig();
        cacheParam.url = this.url;
        cacheParam.method = this.method;
        cacheParam.param = this.param;
        cacheParam.preprocessFlag = this.preprocessFlag;

        if (StringUtils.isEmpty(cusParam)) {
            return cacheParam;
        }
        String baseParam = StringUtils.isNotEmpty(param) ? this.param : "{}";

        if (method == RequestMethod.GET) {
            cacheParam.param = cusParam;
            return cacheParam;
        }
        JSONObject baseParamObj = JSON.parseObject(baseParam);
        JSONObject customParamObj = JSON.parseObject(cusParam);
        baseParamObj.putAll(customParamObj);
        cacheParam.param = JSON.toJSONString(baseParamObj);
        return cacheParam;
    }

    public JSONObject getParsedParam() {
        if (StringUtils.isEmpty(param)) {
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(param);
        jsonObject.put("uuid", hash());
        return jsonObject;
    }

    public String hash() {
        return MessageDigestUtils.getSHA256(url + method + param);
    }

}


@Data
class AiFlowNodeVoiceFallbackParam {

    @JSONField(name = "url")
    private String url;

    @JSONField(name = "containKeys")
    private List<String> containKeys;

    //sort 越大，优先级越高
    @JSONField(name = "sort")
    private int sort;

    @JSONField(name = "tip")
    private String tip;
}
