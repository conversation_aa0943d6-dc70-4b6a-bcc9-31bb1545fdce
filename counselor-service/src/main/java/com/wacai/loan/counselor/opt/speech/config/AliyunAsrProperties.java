package com.wacai.loan.counselor.opt.speech.config;

import java.util.HashMap;
import java.util.Map;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import lombok.Data;

/**
 * 阿里云语音识别配置属性
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "speech.aliyun")
public class AliyunAsrProperties {
    
    /**
     * 默认配置
     */
    private AliyunAsrConfig defaultConfig = new AliyunAsrConfig();
    
    /**
     * 多账号配置
     */
    private Map<String, AliyunAsrConfig> accounts = new HashMap<>();
    
    /**
     * 获取指定名称的配置
     * 
     * @param name 配置名称
     * @return 配置
     */
    public AliyunAsrConfig getConfig(String name) {
        if (name == null || name.isEmpty() || "default".equals(name)) {
            return defaultConfig;
        }
        
        AliyunAsrConfig config = accounts.get(name);
        return config != null ? config : defaultConfig;
    }
    
    /**
     * 获取激活的账号配置
     * 
     * @return 激活的账号配置
     */
    public Map<String, AliyunAsrConfig> getActiveAccounts() {
        Map<String, AliyunAsrConfig> activeAccounts = new HashMap<>();
        
        for (Map.Entry<String, AliyunAsrConfig> entry : accounts.entrySet()) {
            if (entry.getValue().getActive() == 1) {
                activeAccounts.put(entry.getKey(), entry.getValue());
            }
        }
        
        return activeAccounts;
    }
}
