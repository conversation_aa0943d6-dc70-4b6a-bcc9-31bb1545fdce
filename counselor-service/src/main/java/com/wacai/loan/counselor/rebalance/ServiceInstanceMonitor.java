/*
 * @Author: shunhua
 * @Date: 2025-04-17 15:26:38
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-07 10:02:58
 * @Description: 
 */
package com.wacai.loan.counselor.rebalance;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;

/**
 * server monitor
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2025-04-17 15:26
 */
@Component
@EnableScheduling
@Slf4j
public class ServiceInstanceMonitor {

    @Value("${spring.application.name}")
   private  String app;

    @Resource
    private DiscoveryClient discoveryClient;

    @PostConstruct
    public void init() {
        List<ServiceInstance> instances = discoveryClient.getInstances(app);
        log.info("Initial instances for {}: {}", app, instances.stream()
            .map(JSON::toJSONString)
            .collect(Collectors.joining(",")));
    }

    // @Scheduled(fixedDelay = 10000, initialDelay = 10000)
    // public void monitorInstances() {
    //     List<ServiceInstance> instances = discoveryClient.getInstances(app);
    //     log.info("Instances for {}: {}", app, instances.stream()
    //         .map(JSON::toJSONString)
    //         .collect(Collectors.joining(",")));
    // }

    public  List<String> listServiceInstance(){
        List<ServiceInstance> instances = discoveryClient.getInstances(app);
        return instances.stream().map(x->x.getUri().toString()).collect(Collectors.toList());
    }
}
