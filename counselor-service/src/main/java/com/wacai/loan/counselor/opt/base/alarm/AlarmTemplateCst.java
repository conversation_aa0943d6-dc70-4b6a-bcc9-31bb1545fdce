/*
 * @Author: shunhua
 * @Date: 2025-05-30 15:58:08
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-05 10:40:30
 * @Description: 
 */

package com.wacai.loan.counselor.opt.base.alarm;

/**
 * 告警模板
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-15 13:40
 */
public final  class AlarmTemplateCst {

    public static final String ACCOUNT_WS_ERROR="告警类型: AI坐席【%s】websocket告警 \n 告警信息:%s\n";

    public static final String ACCOUNT_REGISTER_ERROR="告警类型: AI坐席【%s】注册告警 \n 告警信息:%s\n";

    public static final String ACCOUNT_ALLOT_ERROR="告警类型: 多实例坐席分配告警 \n 告警信息:实例:【%s】,分配坐席列表:【%s】失败，详细:%s\n";

    public static final String OPENAI_ERROR="告警类型: 大模型接口异常 \n 告警信息:%s\n";

    public static final String TTS_FILE_ALARM = "告警类型: 流程话术文件异常,默认TTS生成文件 \n 告警信息:callId: %s\n text: %s\n fileName: %s";

    public static final String AI_FLOW_ERROR="告警类型: 流程活动不存在或配置错误\n告警信息：业务参数：%s\n";
}
