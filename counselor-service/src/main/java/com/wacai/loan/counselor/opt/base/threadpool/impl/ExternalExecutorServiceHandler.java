package com.wacai.loan.counselor.opt.base.threadpool.impl;

import java.util.concurrent.ExecutorService;
import com.wacai.loan.counselor.opt.base.threadpool.ExecutorServiceHandler;
import com.wacai.loan.counselor.opt.base.threadpool.ExecutorServiceObject;

/**
 * 
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2019-03-21 18:57
 */
public final class ExternalExecutorServiceHandler implements ExecutorServiceHandler {
    @Override
    public ExecutorService createExecutorService(String type) {
        return new ExecutorServiceObject("inner-executor-" + type,
                Runtime.getRuntime().availableProcessors() * 4 + 1).createExecutorService();
    }

    @Override
    public ExecutorService createExecutorService(int coreNum, String type) {
        return new ExecutorServiceObject("inner-executor-" + type, coreNum).createExecutorService();
    }
}
