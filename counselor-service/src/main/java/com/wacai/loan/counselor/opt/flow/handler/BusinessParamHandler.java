/*
 * @Author: shunhua
 * 
 * @Date: 2025-04-30 11:29:18
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-07 16:00:51
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.handler;

import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.wacai.loan.counselor.opt.flow.config.WorkFlowRemoteVoiceConfig;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BusinessParamHandler {

    public static WorkFlowRemoteVoiceConfig buildVoiceConfig(String remoteVoice) {
        if (StringUtils.isEmpty(remoteVoice)) {
            return null;
        }
        final JSONObject jsonObject = JSON.parseObject(remoteVoice);
        final Object param = jsonObject.get("param");
        if (Objects.nonNull(param) && param instanceof JSONObject) {
            jsonObject.put("param", JSON.toJSONString(param));
        }
        return JSON.to(WorkFlowRemoteVoiceConfig.class, jsonObject);
    }

    public static Map<String, String> buildSemanticsTransform(String semanticsTransform) {
        if (StringUtils.isNotBlank(semanticsTransform)) {
            return JSON.parseObject(semanticsTransform,
                    new TypeReference<Map<String, String>>() {
                    });
        }
        return null;
    }
}
