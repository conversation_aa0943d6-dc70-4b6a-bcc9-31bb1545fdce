/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-03 21:38:44
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-01 15:44:45
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.listener.flow.impl;

import java.util.Objects;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson2.JSONObject;
import com.wacai.loan.counselor.dao.po.AiFlow;
import com.wacai.loan.counselor.opt.base.cache.CallUserDataCache;
import com.wacai.loan.counselor.opt.base.constant.AiFlowTypeEnums;
import com.wacai.loan.counselor.opt.base.dto.DialEventDTO;
import com.wacai.loan.counselor.opt.base.hermes.PredictCallingListService;
import com.wacai.loan.counselor.opt.base.state.CallStateManager;
import com.wacai.loan.counselor.opt.flow.cache.WorkFlowLoadCache;
import com.wacai.loan.counselor.opt.flow.core.WorkFlowNode;
import com.wacai.loan.counselor.opt.flow.listener.flow.AbstractFlowEventListener;
import com.wacai.loan.counselor.opt.flow.service.WorkFlowNodeService;
import com.wacai.loan.counselor.opt.sip.sipsession.AiAgentCall;
import com.wacai.loan.counselor.opt.speech.asr.AliyunRealtimeAsrServiceManager;
import com.wacai.loan.counselor.opt.speech.asr.impl.AliyunRealtimeAsrService;
import com.wacai.loan.counselor.opt.speech.model.AsrModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class NormalFlowEventListener extends AbstractFlowEventListener {


     
    private final WorkFlowNodeService workFlowNodeService;

  
    private final AliyunRealtimeAsrServiceManager alibabaAsrServiceManager;

    
    private final PredictCallingListService predictCallingListService;

     
    private  final WorkFlowLoadCache workFlowLoadCache;


    protected AiFlowTypeEnums getSupportedFlowType() {
        return AiFlowTypeEnums.NORMAL;
    }


    @Override
    protected void handleRingEvent(DialEventDTO eventDTO) {
        AiAgentCall call = callContext.get(eventDTO.getUuid());
        if (Objects.isNull(call)) {
            log.warn("aiFlow is null, startAsr by default {}", eventDTO.getUuid());
            return;
        }
        if (CallStateManager.getState(eventDTO.getUuid()).compareAndSetMediaActive(false, true)) {
            Boolean notReceiveVoiceInput = call.getAiFlowBO().getAiFlow().getNotReceiveVoiceInput();
            if (Objects.nonNull(notReceiveVoiceInput) && notReceiveVoiceInput) {
                log.warn("callId : {} media active ,not start asr", eventDTO.getUuid());
                return;
            }

            log.info("callId: {}  media active,Start ASR ", eventDTO.getUuid());
            AliyunRealtimeAsrService asrService = alibabaAsrServiceManager
                    .getService(call.getAiFlowBO().getAiFlow().getTenantCode());
            AsrModel asrModel = asrService.start(eventDTO.getUuid(), call.getRecorderFile());
            asrModel.setStartTime(System.currentTimeMillis());
            call.setAsrModel(asrModel);
           cacheNodeVoice(eventDTO);
        }
    }

    @Override
    protected void handleAnswerEvent(DialEventDTO eventDTO) {
        AiAgentCall call = callContext.get(eventDTO.getUuid());
        AiFlow aiFlow = call.getAiFlowBO().getAiFlow();
        log.info("callId: {} workFlowCode:{}, firstCode: {} start...", eventDTO.getUuid(),
                aiFlow.getCode(), aiFlow.getFirstFlowNodeCode());
        WorkFlowNode workFlowNode = workFlowLoadCache.getWorkFlowNodeOfFill(aiFlow.getCode(),
                aiFlow.getFirstFlowNodeCode());
        workFlowNodeService.startFirstFlow(call.getCallId(), workFlowNode);
    }

    @Override
    protected void handleHangupEvent(DialEventDTO eventDTO) {
        AiAgentCall call = callContext.get(eventDTO.getUuid());
        if (Objects.nonNull(call)) {
            call.hangup();
        }
    }

    private void cacheNodeVoice(DialEventDTO eventDTO) {
        JSONObject userDataJsonObject = CallUserDataCache.getCache(eventDTO.getUuid());
        if (Objects.nonNull(userDataJsonObject)) {
            String flowCode = userDataJsonObject.getString("flowCode");
            predictCallingListService.remoteVoiceCache(eventDTO.getUuid(), eventDTO.getUserData(),
                    workFlowLoadCache.getByFlowCode(flowCode));
        }
    }
}
