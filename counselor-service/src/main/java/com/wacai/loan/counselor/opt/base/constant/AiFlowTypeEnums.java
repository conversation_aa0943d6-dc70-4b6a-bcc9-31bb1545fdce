/*
 * @Author: shunhua
 * 
 * @Date: 2025-04-30 11:06:53
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-23 10:39:16
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.base.constant;

public enum AiFlowTypeEnums {

    NORMAL("normal"), DYNAMIC("dynamic"), REALTIME("realtime");


    private final String code;

    AiFlowTypeEnums(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static AiFlowTypeEnums fromCode(String code) {
        for (AiFlowTypeEnums type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return NORMAL; // 默认类型
    }
}
