/*
 * @Author: shunhua
 * @Date: 2025-04-30 14:46:39
 * @LastEditors: shunhua
 * @LastEditTime: 2025-05-04 15:04:46
 * @Description: 
 */
package com.wacai.loan.counselor.opt.speech.tts;

import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerListener;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerResponse;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.concurrent.CountDownLatch;

/**
 * 阿里云文本转语音监听器
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class AliyunTtsListener extends SpeechSynthesizerListener {
    
    private final File outputFile;
    private final CountDownLatch latch;
    private FileOutputStream outputStream;
    private long startTime;
    private long endTime;
    
    public AliyunTtsListener(File outputFile, CountDownLatch latch) {
        this.outputFile = outputFile;
        this.latch = latch;
        this.startTime = System.currentTimeMillis();
    }
    
    @Override
    public void onComplete(SpeechSynthesizerResponse response) {
        // 合成完成
        endTime = System.currentTimeMillis();
        log.info("TTS task complete, duration: {}ms", endTime - startTime);
        
        // 关闭输出流
        closeOutputStream();
        
        // 释放锁
        latch.countDown();
    }
    
    @Override
    public void onFail(SpeechSynthesizerResponse response) {
        // 合成失败
        log.error("TTS task failed: {}", response.getStatus());
        
        // 关闭输出流
        closeOutputStream();
        
        // 释放锁
        latch.countDown();
    }
    
    @Override
    public void onMessage(ByteBuffer data) {
        // 接收音频数据
        try {
            if (outputStream == null) {
                outputStream = new FileOutputStream(outputFile);
            }
            byte[] bytesArray = new byte[data.remaining()];
            data.get(bytesArray, 0, bytesArray.length);
            outputStream.write(bytesArray);
        } catch (IOException e) {
            log.error("Write TTS data failed", e);
        }
    }
    
    /**
     * 关闭输出流
     */
    private void closeOutputStream() {
        if (outputStream != null) {
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("Close output stream failed", e);
            }
        }
    }
    
    /**
     * 获取合成时长
     */
    public long getDuration() {
        return endTime - startTime;
    }
}
