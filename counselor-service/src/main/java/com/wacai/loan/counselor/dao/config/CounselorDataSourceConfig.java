/*
 * @Author: shunhua
 * @Date: 2023-04-23 16:47:05
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-10 14:38:54
 * @Description: 
 */
package com.wacai.loan.counselor.dao.config;

import javax.sql.DataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import com.wacai.secure.ds.autocfg.SecureDataSourceConfiguration;
import tk.mybatis.spring.annotation.MapperScan;

/**
 * <AUTHOR>
 * 资源位 数据源
 */
@Configuration
@MapperScan(basePackages = "com.wacai.loan.counselor.dao.repository", sqlSessionFactoryRef = "aiSqlSessionFactory")
public class CounselorDataSourceConfig extends SecureDataSourceConfiguration {

    @Bean
    @ConfigurationProperties("spring.secure.ds.default")
    FactoryBean<DataSource> aiDs() {
        return createSecureDataSourceFactoryBean();
    }

    @Bean(name = "aiSqlSessionFactory")
    public SqlSessionFactory adSqlSessionFactory(@Qualifier("aiDs") DataSource aiDs) throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(aiDs);
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        sessionFactory.setConfiguration(configuration);
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));
        return sessionFactory.getObject();
    }

}
