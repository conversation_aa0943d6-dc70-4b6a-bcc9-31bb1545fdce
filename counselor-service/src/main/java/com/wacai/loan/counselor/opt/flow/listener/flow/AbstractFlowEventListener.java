/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-03 21:22:25
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-22 17:53:42
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.listener.flow;

import javax.annotation.Resource;
import com.wacai.loan.counselor.opt.base.constant.AiFlowTypeEnums;
import com.wacai.loan.counselor.opt.base.dto.DialEventDTO;
import com.wacai.loan.counselor.opt.sip.cache.CallContext;

public abstract class AbstractFlowEventListener {

    @Resource
    protected CallContext callContext;

    protected abstract AiFlowTypeEnums getSupportedFlowType();

    protected abstract void handleRingEvent(DialEventDTO eventDTO);

    protected abstract void handleAnswerEvent(DialEventDTO eventDTO);

    protected abstract void handleHangupEvent(DialEventDTO eventDTO);
}
