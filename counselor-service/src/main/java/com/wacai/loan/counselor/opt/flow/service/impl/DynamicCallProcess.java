/*
 * @Author: shunhua
 * @Date: 2025-09-05 10:07:14
 * @LastEditors: shunhua
 * @LastEditTime: 2025-09-05 10:12:39
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.service.impl;

import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import com.wacai.loan.counselor.opt.flow.service.CallProcessor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class DynamicCallProcess implements CallProcessor{

    @Override
    public boolean transfer(String callId, WorkflowNodeConfig workflowNodeConfig) {
        return false;
    }

    @Override
    public void playVoice(String callId, String text, String fileName,
            WorkflowNodeConfig workflowNodeConfig) {
        
    }

    @Override
    public void cyclePlayVoice(String callId, String voicePath) {
        
    }

    @Override
    public void resumePlay(String callId) {
        
    }

    @Override
    public void stopPlay(String callId) {
        
    }

}
