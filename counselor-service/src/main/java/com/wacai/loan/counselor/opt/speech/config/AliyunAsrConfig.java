/*
 * @Author: shun<PERSON>
 * @Date: 2025-05-04 14:57:05
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-01 16:33:04
 * @Description: 
 */
package com.wacai.loan.counselor.opt.speech.config;
import lombok.Data;

/**
 * 阿里云语音识别配置
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class AliyunAsrConfig {

    /**
     * 配置名称
     */
    private String name;

    /**
     * 是否激活 (1: 激活, 0: 不激活)
     */
    private int active = 1;

    /**
     * 应用Key
     */
    private String appKey;

    /**
     * 访问密钥ID
     */
    private String accessKey;

    /**
     * 访问密钥密码
     */
    private String accessSecret;

     

    /**
     * 采样率
     */
    private int sampleRate = 8000;

    /**
     * 发送数据大小
     */
    private int sendSize = 1600;

    /**
     * 发送间隔(毫秒)
     */
    private long sendInterval = 200;

    /**
     * 开始发送延迟(毫秒)
     */
    private long startSendDelay = 300;

    /**
     * 发送失败中断次数
     */
    private long sendFailBreakTimes = 32;

    /**
     * 是否返回中间结果
     */
    private boolean tempResult = true;

    /**
     * 是否启用标点符号
     */
    private boolean enablePunctuation = true;

    /**
     * 是否启用ITN
     */
    private boolean enableItn = true;

    /**
     * 语音噪声阈值
     */
    private float speechNoiseThreshold = 0.7f;

    /**
     * 最大句子静音时长
     */
    private int maxSentenceSilence = 500;
}
