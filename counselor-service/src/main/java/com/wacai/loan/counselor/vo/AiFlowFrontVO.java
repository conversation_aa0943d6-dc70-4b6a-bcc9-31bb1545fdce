/*
 * @Author: shunhua
 * @Date: 2025-08-13 19:40:46
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-18 11:19:44
 * @Description: 
 */
package com.wacai.loan.counselor.vo;

import org.springframework.beans.BeanUtils;
import com.wacai.loan.counselor.dao.po.AiFlow;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/10/12 14:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AiFlowFrontVO extends AiFlow {
    public enum Type {
        normal,
        dynamic
    }

    public AiFlow buildAiFlow() {
        final AiFlow aiFlow = new AiFlow();
        BeanUtils.copyProperties(this, aiFlow);
        return aiFlow;
    }

    public static AiFlowFrontVO buildSelf(AiFlow aiFlow) {
        final AiFlowFrontVO aiFlowFrontVO = new AiFlowFrontVO();
        BeanUtils.copyProperties(aiFlow, aiFlowFrontVO);
        return aiFlowFrontVO;
    }
}
