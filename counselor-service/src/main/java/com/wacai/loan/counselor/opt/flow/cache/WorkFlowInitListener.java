/*
 * @Author: shunhua
 * @Date: 2025-08-01 15:31:52
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-01 15:31:53
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.cache;

import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@RequiredArgsConstructor
@Slf4j
public class WorkFlowInitListener implements ApplicationListener<ContextRefreshedEvent>{

    private final WorkFlowLoadCache workFlowLoadCache;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        workFlowLoadCache.initWorkFlow();
        log.info("*********workFlowLoadCache initWorkFlow **************");
    }

}
