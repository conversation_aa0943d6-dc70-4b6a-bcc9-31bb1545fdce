/*
 * @Author: shunhua
 * @Date: 2025-05-03 21:09:26
 * @LastEditors: shunhua
 * @LastEditTime: 2025-05-04 15:28:21
 * @Description: 
 */

package com.wacai.loan.counselor.opt.base.event;

import org.springframework.context.ApplicationEvent;
import com.wacai.loan.counselor.opt.base.dto.DialEventDTO;
import lombok.Getter;

public abstract class SipEvent extends ApplicationEvent {

    @Getter
    private final DialEventDTO eventDTO;

    public SipEvent(Object source, DialEventDTO eventDTO) {
        super(source);
        this.eventDTO = eventDTO;
    }

    /**
     * 振铃事件
     */
    public static class OriginateEvent extends SipEvent {
        public OriginateEvent(Object source, DialEventDTO eventDTO) {
            super(source, eventDTO);
        }
    }
    
    /**
     * 振铃事件
     */
    public static class RingEvent extends SipEvent {
        public RingEvent(Object source, DialEventDTO eventDTO) {
            super(source, eventDTO);
        }
    }

    /**
     * 应答事件
     */
    public static class AnswerEvent extends SipEvent {
        public AnswerEvent(Object source, DialEventDTO eventDTO) {
            super(source, eventDTO);
        }
    }

    /**
     * 拒绝事件
     */
    public static class RejectEvent extends SipEvent {
        public RejectEvent(Object source, DialEventDTO eventDTO) {
            super(source, eventDTO);
        }
    }

    /**
     * 挂断事件
     */
    public static class HangupEvent extends SipEvent {
        public HangupEvent(Object source, DialEventDTO eventDTO) {
            super(source, eventDTO);
        }
    }

    /**
     * 未知事件
     */
    public static class UnknownEvent extends SipEvent {
        public UnknownEvent(Object source, DialEventDTO eventDTO) {
            super(source, eventDTO);
        }
    }
}
