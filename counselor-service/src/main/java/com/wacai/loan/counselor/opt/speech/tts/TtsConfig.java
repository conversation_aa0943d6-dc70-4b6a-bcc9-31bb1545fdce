/*
 * @Author: shunhua
 * @Date: 2025-04-30 14:46:18
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-25 10:04:34
 * @Description: 
 */
package com.wacai.loan.counselor.opt.speech.tts;

import org.springframework.stereotype.Component;
import lombok.Data;

/**
 * TTS 配置
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Component
public class TtsConfig {
    
    // @Value("${speech.aliyun.app-key}")
    // private String appKey;
    
    // @Value("${speech.tts.default-voice:xiaoyun}")
    // private String defaultVoice;
    
    // @Value("${speech.tts.default-speech-rate:0}")
    // private int defaultSpeechRate;
    
    // @Value("${speech.tts.default-pitch-rate:0}")
    // private int defaultPitchRate;
    
    // @Value("${speech.tts.default-volume:50}")
    // private int defaultVolume;
    
    // @Value("${speech.tts.temp-path:/tmp/tts/temp}")
    // private String tempPath;
    
    // @Value("${speech.tts.output-path:/tmp/tts/output}")
    // private String outputPath;
    
    // @Override
    // public void afterPropertiesSet() throws Exception {
    //     // 创建目录
    //     createDirectories();
    // }
    
    /**
     * 创建目录
     */
    // private void createDirectories() {
    //     try {
    //         File tempDir = new File(tempPath);
    //         if (!tempDir.exists()) {
    //             tempDir.mkdirs();
    //         }
            
    //         File outputDir = new File(outputPath);
    //         if (!outputDir.exists()) {
    //             outputDir.mkdirs();
    //         }
    //     } catch (Exception e) {
    //         throw new RuntimeException("Create directories failed", e);
    //     }
    // }
}
