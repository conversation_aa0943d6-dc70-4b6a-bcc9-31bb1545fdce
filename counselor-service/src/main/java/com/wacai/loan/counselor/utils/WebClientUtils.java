/*
 * @Author: shun<PERSON>
 * @Date: 2024-12-20 19:27:27
 * @LastEditors: shunhua
 * @LastEditTime: 2025-04-11 11:45:12
 * @Description: 
 */
package com.wacai.loan.counselor.utils;

import com.alibaba.fastjson2.JSON;

import java.net.URI;
import java.time.Duration;
import java.util.function.Consumer;

import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.Getter;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/29 16:23
 */
@UtilityClass
@Slf4j
public class WebClientUtils {

    /**
     * some times we will use directly
     */
    @Getter
    private WebClient webClient;

    static {
        init();
    }

    private void init() {
        ConnectionProvider provider = ConnectionProvider.builder("custom-connection-pool")
                .maxConnections(500) // 最大连接数
                .pendingAcquireMaxCount(1000) // 等待获取连接的请求最大数
                .pendingAcquireTimeout(Duration.ofSeconds(30)) // 等待连接的最大时间
                .build();
        HttpClient httpClient = HttpClient.create(
                provider)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 20000) // 连接超时
                .responseTimeout(Duration.ofSeconds(30)) // 整体响应超时
                .doOnConnected(conn -> conn
                        .addHandlerLast(new ReadTimeoutHandler(30)) // 读取超时
                        .addHandlerLast(new WriteTimeoutHandler(30))); // 写入超时

        webClient = WebClient.builder().clientConnector(new ReactorClientHttpConnector(httpClient)).build();
    }

    public void post(String url, Object param, Consumer<? super String> consumer) {
        if (consumer == null) {
            consumer = s -> log.info("post result {} {}", url, s);
        }

        final WebClient.RequestBodySpec requestBodySpec = webClient.post()
            .uri(URI.create(url))
            .contentType(MediaType.APPLICATION_JSON);
        if (param != null) {
            if (param instanceof String) {
                requestBodySpec.bodyValue(param);
            } else {
                requestBodySpec.bodyValue(JSON.toJSONString(param));
            }
        }
        requestBodySpec.retrieve()
            .bodyToMono(String.class)
            .doOnError(throwable -> log.error("post alert has err", throwable))
            .subscribe(consumer);
    }

    public WebClient.ResponseSpec post(String url, Object param) {
        final WebClient.RequestBodySpec requestBodySpec = webClient.post()
            .uri(URI.create(url))
            .contentType(MediaType.APPLICATION_JSON);
        if (param != null) {
            requestBodySpec.bodyValue(JSON.toJSONString(param));
        }
        return requestBodySpec.retrieve();
    }

    public void get(String url) {
        webClient.get()
            .uri(URI.create(url))
            .retrieve()
            .bodyToMono(String.class)
            .doOnError(throwable -> log.error("webClient req alert has err", throwable))
            .subscribe(s -> log.info("req: result {} {}", url, s));
    }
}
