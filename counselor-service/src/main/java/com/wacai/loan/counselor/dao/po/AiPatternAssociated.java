package com.wacai.loan.counselor.dao.po;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * ai匹配模式附属表，单模式关联
 *
 * @TableName ai_pattern_associated
 */
@Table(name = "ai_pattern_associated")
@Data
@Entity
public class AiPatternAssociated implements Serializable {
    /**
     * 自增主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * ai_pattern编码
     */
    @Column(name = "ai_pattern_code")
    private String aiPatternCode;

    /**
     * 匹配模式，正则表达式
     */
    private String pattern;

    /**
     * 备注
     */
    private String comment;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    private Date updatedTime;

    private static final long serialVersionUID = 1L;
}