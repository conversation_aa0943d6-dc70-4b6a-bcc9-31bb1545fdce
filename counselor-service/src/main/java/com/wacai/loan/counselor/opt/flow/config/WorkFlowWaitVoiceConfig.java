/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-06 17:22:36
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-21 11:25:32
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.config;


import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

@Data
public class WorkFlowWaitVoiceConfig {

    private boolean enable;

    private boolean repeatSelf;

    private int interval = 10;

    private int times = 2;

    /**
     * next flow code
     */
    private String muteEndFlowCode;

    public static WorkFlowWaitVoiceConfig defaultObj() {
        final WorkFlowWaitVoiceConfig waitVoiceCfg = new WorkFlowWaitVoiceConfig();
        waitVoiceCfg.setEnable(true);
        waitVoiceCfg.setRepeatSelf(false);
        waitVoiceCfg.setInterval(10);
        return waitVoiceCfg;
    }


    public static WorkFlowWaitVoiceConfig builderWaitVoice(String context) {
        if (StringUtils.isEmpty(context)) {
            return null;
        }
        JSONObject json = JSON.parseObject(context);
        JSONObject waitVoiceCfgJson = json.getJSONObject("waitVoiceCfg");
        if (waitVoiceCfgJson == null || waitVoiceCfgJson.isEmpty()) {
            return WorkFlowWaitVoiceConfig.defaultObj();
        }

        return waitVoiceCfgJson.to(WorkFlowWaitVoiceConfig.class);

    }
}
