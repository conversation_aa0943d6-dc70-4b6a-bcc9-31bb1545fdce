/*
 * @Author: shunhua
 * 
 * @Date: 2025-09-04 14:57:34
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-09-05 10:23:40
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.handler.wss;

import java.net.URI;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketHttpHeaders;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;
import com.wacai.loan.counselor.opt.flow.service.CallProcessor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DynamicWebSocketClient implements WebSocketHandler {

    @Getter
    private WebSocketSession session;

    private final String callId;

    private final String mobile;


    private worf callProcessor;

    public DynamicWebSocketClient(String callId, String mobile, CallProcessor callProcessor) {
        this.callId = callId;
        this.mobile = mobile;
        this.callProcessor = callProcessor;
    }

    public ListenableFuture<?> connect(String serverUrl) throws Exception {
        StandardWebSocketClient client = new StandardWebSocketClient();
        WebSocketHttpHeaders headers = new WebSocketHttpHeaders();
        headers.add("mobile",mobile);
       return  client.doHandshake(this, headers, URI.create(serverUrl + "/" + callId));
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        this.session = session;
        log.info("WebSocket connection established for callId: {}, mobile: {}", callId, mobile);
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message)
            throws Exception {
      if(message instanceof TextMessage){
          String payload = ((TextMessage) message).getPayload();
          log.info("callId: {} receive message: {}", callId, payload);
          callProcessor.playVoice(callId, payload, null, null);
      } else if (message instanceof BinaryMessage){

      }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception)
            throws Exception {
        log.error("WebSocket transport error for callId: {}, mobile: {}, error: {}", callId, mobile,
                exception.getMessage(), exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus)
            throws Exception {
        log.info("WebSocket connection closed for callId: {}, mobile: {}, status: {}", callId,
                mobile, closeStatus);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }


    
}
