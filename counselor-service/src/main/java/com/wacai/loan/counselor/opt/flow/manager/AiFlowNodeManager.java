/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-06 18:55:43
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-01 15:21:29
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.manager;

import java.util.List;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.dao.po.AiFlowNode;
import com.wacai.loan.counselor.dao.repository.AiFlowNodeMapper;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AiFlowNodeManager {

  @Resource
  private AiFlowNodeMapper aiFlowNodeMapper;

  public List<AiFlowNode> listAll() {
    return aiFlowNodeMapper.selectAll();
  }

  public AiFlowNode getFlowNodeByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }
    AiFlowNode aiFlowNode = new AiFlowNode();
    aiFlowNode.setCode(code);
    return aiFlowNodeMapper.selectOne(aiFlowNode);
  }

  public void add(AiFlowNode aiFlowNode) {
    aiFlowNodeMapper.insert(aiFlowNode);
  }
}

