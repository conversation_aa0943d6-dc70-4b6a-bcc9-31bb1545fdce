/*
 * @Author: shunhua
 * @Date: 2025-08-13 19:40:46
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-18 11:07:45
 * @Description: 
 */
package com.wacai.loan.counselor.opt.base.alarm;

import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.wacai.loan.counselor.utils.OkHttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/14 16:49
 */
@Service
@Slf4j
public class AlarmService {

    @Value("${notice-component.alarm.url}")
    private   String alarmUrl;

    @Value("${spring.application.name}")
    private String app;

    @Value("#{'${notice-component.alarm.receivers}'.split(',')}")
    private List<String> receivers;

    static final List<LocalTime> timeRang = ImmutableList.of(LocalTime.parse("08:00"), LocalTime.parse("23:00"));


    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    private static final AtomicBoolean isExecuting = new AtomicBoolean(false);

    public void alert(String txt) {
        alert(NotifierContext.builder().content(txt).build());
    }

    public void throttledAlert(String txt) {
        if (isExecuting.compareAndSet(false, true)) {
            // 若之前未执行，则执行任务
            alert(NotifierContext.builder().content(txt).build());
            // 一分钟后将状态置为 false，以便下次可以再次执行
            scheduler.schedule(() -> isExecuting.set(false), 1, TimeUnit.MINUTES);
        }
    }

    public void alert(NotifierContext context) {
        final LocalTime now = LocalTime.now();
        if (now.isBefore(timeRang.get(0)) || now.isAfter(timeRang.get(1))) {
            log.error("out of time just log alert {}", context);
            return;
        }

        packageContext(context);

    OkHttpUtil.getInstance().post(alarmUrl, ImmutableMap.of("X-Login-User",app), 
            JSON.toJSONString(context), new TypeReference<String>() {
            });
    }

    private void packageContext(NotifierContext context) {
        context.setLevel("warn");
        Map<String, Object> map = Maps.newHashMap();
        map.put("label", UUID.randomUUID().toString());
        context.setLabel(map);
        context.setTitle(
                "production".equals(System.getenv("JAVA_APP_ENV")) ? "【生产环境】【counselor-service】应用"
                        : "【测试环境】【counselor-service】应用");
        context.setReceivers(receivers);
    }
}
