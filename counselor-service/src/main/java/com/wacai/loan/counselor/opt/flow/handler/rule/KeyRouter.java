/*
 * @Author: shunhua
 * @Date: 2025-05-07 10:22:41
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-18 19:16:30
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.handler.rule;

import java.util.List;
import com.wacai.loan.counselor.opt.ai.service.OpenAIFacadeService;
import com.wacai.loan.counselor.opt.flow.model.RouterResponse;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
public class KeyRouter  extends Router{

    private List<String> data;
    @Override
    public void init(OpenAIFacadeService openAIFacadeService) {
       
    }


    @Override
    public RouterResponse actionRouter(String callId, WorkflowNodeConfig workflowNodeConfig, String phone,
            String text) {
        return null;
    }

}
