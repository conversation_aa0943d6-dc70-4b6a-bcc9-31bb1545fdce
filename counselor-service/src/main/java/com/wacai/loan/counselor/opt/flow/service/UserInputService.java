/*
 * @Author: shunhua
 * @Date: 2025-05-12 11:09:43
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-01 14:52:44
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.service;

import java.util.List;
import com.wacai.loan.counselor.dao.po.UserInput;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;

public interface UserInputService {


     void  add(String message,String callId,String phone,WorkflowNodeConfig workflowNodeConfig);

    void update(String callId,String message,String aiResut);

    void updateUserInputOfVoiceAssistant(String callId,String text);

    List<UserInput> listUserInputByCallId(String callId);
}
