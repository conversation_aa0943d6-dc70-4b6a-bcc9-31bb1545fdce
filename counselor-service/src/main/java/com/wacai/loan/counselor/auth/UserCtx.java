package com.wacai.loan.counselor.auth;

import java.util.Optional;

import org.apache.commons.lang3.StringUtils;

/**
 *
 */
public class UserCtx {
    private static final ThreadLocal<UserInfo> ctx = new ThreadLocal<>();

    public static UserInfo get() {
        return ctx.get();
    }

    public static void set(UserInfo ui) {
        ctx.set(ui);
    }

    public static void remove() {
        ctx.remove();
    }

    public static String acqUserName() {
        return Optional.ofNullable(get()).map(UserInfo::getAccount).orElse(StringUtils.EMPTY);
    }
}
