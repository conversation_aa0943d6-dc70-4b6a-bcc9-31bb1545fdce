/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-07 10:14:39
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-18 19:17:22
 * 
 * @Description:
 */
package com.wacai.loan.counselor.opt.flow.handler.rule;

import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.wacai.loan.counselor.opt.ai.service.OpenAIFacadeService;
import com.wacai.loan.counselor.opt.flow.model.RouterResponse;
import com.wacai.loan.counselor.opt.flow.model.WorkflowNodeConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Data
public abstract class Router {

    String type;

    @AllArgsConstructor
    public enum Type {
        key(KeyRouter.class), pattern(PatternRouter.class), aiModel(
                AiModelRouter.class);

        @Getter
        Class<? extends Router> clz;
    }

    public static Router buildTransferRule(String extInfo,OpenAIFacadeService openAIFacadeService) {
        Router transferTypeRule = null;
        if (StringUtils.isNotBlank(extInfo)) {
            final JSONObject jsonObject = JSON.parseObject(extInfo);
            final JSONObject transferType = jsonObject.getJSONObject("transferType");
            if (Objects.nonNull(transferType)) {
                final String type = transferType.getString("type");
                transferTypeRule = transferType.to(Type.valueOf(type).getClz());
                transferTypeRule.init(openAIFacadeService);
            }
        }
        return transferTypeRule;
    }

    public abstract void init(OpenAIFacadeService openAIFacadeService);


    @Data
    public static class SemanticsItem {
        String code;
        String name;
    }

    /**
     * 
     * @param callId
     * @param flowTip
     * @param phone
     * @param text
     * @return
     */
    public abstract RouterResponse actionRouter(String callId,WorkflowNodeConfig workflowNodeConfig,
            String phone, String text);

}
