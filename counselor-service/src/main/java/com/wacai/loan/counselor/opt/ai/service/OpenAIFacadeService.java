/*
 * @Author: shunhua
 * @Date: 2025-05-15 19:15:52
 * @LastEditors: shunhua
 * @LastEditTime: 2025-05-15 19:17:39
 * @Description: 
 */
package com.wacai.loan.counselor.opt.ai.service;

import java.util.Optional;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.wacai.loan.counselor.opt.ai.constant.OpenAiModelEnums;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class OpenAIFacadeService {

    @Value("${openai.url}")
    private String openAiUrl;

    @Value("${openai.api.key}")
    private String openAiKey;

    /**
     * 模型语义识别
     *
     * @param callUUid 通话 callUUid
     * @param flowCode 流程Code
     * @param prompt 模型 prompt system
     * @param modelName 模型名称 如：Doubao-lite-32k-0428
     * @param content 语义输入
     */
    public String acquireAiModalResult(String callUUid, String flowCode, String SystemPrompt,
            String userPrompt, String modelName) {
        Optional<OpenAiModelEnums> optional = OpenAiModelEnums.of(modelName);
        if (!optional.isPresent()) {
            throw new IllegalArgumentException("modelName is not exist");
        }
        String url = String.format(openAiUrl, modelName);
        return OpenAiChatService.getOpenChat(callUUid, flowCode, SystemPrompt, userPrompt, url,
                openAiKey);
    }
}
