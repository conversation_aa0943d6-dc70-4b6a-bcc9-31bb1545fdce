/*
 * @Author: shunhua
 * @Date: 2025-07-15 14:32:19
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-25 17:47:47
 * @Description: 
 */
package com.wacai.loan.counselor.opt.flow.service;

import com.wacai.loan.counselor.opt.flow.core.WorkFlowNode;
import com.wacai.loan.counselor.opt.sip.sipsession.AiAgentCall;

public interface WorkFlowNodeService {
    /**
     * start first flow
     * @param callId
     * @param workFlowNode
     */
    public void startFirstFlow(String callId,WorkFlowNode workFlowNode);

    /**
     * asr callback
     * @param call
     * @param text
     */
     public void asrCallback(AiAgentCall call, String text);


     /**
      *action next flow node
      * @param call
      * @param workFlowNode
      * @param text
      */
     public  void actionNextFlow(AiAgentCall call, WorkFlowNode workFlowNode, String text);

}
