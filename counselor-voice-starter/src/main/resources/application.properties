spring.application.name=counselor-voice
project.basedir=../
LOG_HOME=${project.basedir}/.logs

spring.env.active=dev

voice.cluster.number=1

## db config
spring.secure.ds.default.database=linkup
DRUID_DECRYPT_KEY=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKYdVbcO5iAly/TQavSzVYfzGWaI47TpR9qGwZWf84oxoIQ6dSF7v82bTrxLcj/uuQQ31QnHYGp3XFokxUKSmzUCAwEAAQ==
spring.secure.ds.default.urlParams=useUnicode=true&characterEncoding=utf8&useSSL=true


# ignore properties
#yo.report.kafka.url=http://yo-report.yo.k2.test.wacai.info/report/kafka


# sipphone
yo.sipphone.stalemated-check.rate=600
yo.sipphone.silence-check.duration=4800

# Voice Store
yo.robot.voice-file-server.base-path=http://api.genesys.wacai.info/br/file/

#
yo.robot-call.data.query-url=http://yo-br-campaign.yo-prod.k2.test.wacai.info/robot/call-data

yo.robot-talk.mem-cache.enable=true

# websocket url
#yo.sipphone.call-data.stomp-url=http://yo-ivr.yo.k2.test.wacai.info/stomp



# enable linkup
linkup.enable=true
linkup.account.mock=false
# if linkup.account.mock = true, the next 4 lines does not take effect.
linkup.account.loader.zk=*************:2181
linkup.account.loader.path=/Sipphone/LinkupNodes
linkup.account.loader.count=10
linkup.account.loader.siphost=**************:6060
#linkup.account.loader.siphost=**************:6060
linkup.listener.urlTemplate=wss://linkup-fs.test.wacai.info:443
linkup.listener.agentReadyUri=link-agent.linkup.k2.test.wacai.info
# PJSIP
pjsip.sip-port=6060
pjsip.log-level=6
pjsip.ua.thread-count=1
pjsip.codecs=PCMA/8000/1
pjsip.recordings-dir=/Users/<USER>/Desktop/tmp/
voice.template.path=/Users/<USER>/Desktop/tmp/
ai.reply.url=http://counselor.x-amc.wke-office.test.wacai.info/voice/ai



dubbo.zk.servers=zktestserver1.wacai.info:22181,zktestserver2.wacai.info:22181,zktestserver3.wacai.info:22181
dubbo.zk.namespace=counselor
#qdrant
topic.qdrant.collection.name=counselor_voice_topic_data
knowledge.qdrant.collection.name=counselor_qa_data

# zk
spring.cloud.zookeeper.discovery.instance-pre-path=/wacai/counselor/sp-instance
spring.cloud.zookeeper.connect-string=${dubbo.zk.servers}
spring.cloud.zookeeper.discovery.register=true
#spring.cloud.zookeeper.discovery.instance-id=${spring.cloud.zookeeper.discovery.instance-pre-path}/${spring.application.name}:${spring.application.instance_id:${random.value}}
spring.cloud.zookeeper.discovery.root=/wacai/counselor/sp-instance

# openai
openai.token=d9673357f6a540dc92e66626d39ae31f

openai.base.url=http://chatgpt.tiny-test.wke-office.test.wacai.info
openai.api.version=2023-03-15-preview

voice.robot.test.num=701300

not.handler.times=3

ai-action.name-map={'15179183370':'\u7a0b\u5148\u751f','13588796085':'\u4f55\u5c0f\u59d0','13505816420':'\u67f3\u5148\u751f','13676530556':'\u8521\u5148\u751f','18226145289':'\u94b1\u5c0f\u59d0','13695820509':'\u91d1\u5148\u751f','13758281937':'\u6d4b\u5148\u751f','18557521830':'\u738b\u5148\u751f','13858981923':'\u7ae0\u5c0f\u59d0','17764546926':'\u90b1\u5c0f\u59d0','13591337015':'\u6d4b\u5148\u751f','18515502192':'\u6d4b\u5148\u751f','15935217157':'\u6d4b\u5148\u751f','17758001558':'\u5b81\u8fdc','13758171315':'\u821c\u534e','18668083292':'\u6708\u83f1','18958182502':'\u6000\u747e','13083966536':'\u5217\u5f17','15058795602':'\u5176\u741b'}

# aliyun ??
# aliyun ??
aliyun.voicePlayer.configs=[{\"voicePlayer\":\"sijing\",\"speechRate\":\"-250\"},{\"voicePlayer\":\"sicheng\",\"speechRate\":\"-170\"}]
# aliyun.voicePlayer=sijing,sicheng
aliyun.speech.noise.threshold=1
aliyun.realtime-asr.max_sentence_silence=2000

# aliyun.appkey=3CTfUnySrlrNJffu
# aliyun.accessKey=LTAI4GEqEDVLryGgpdv1UvwQ
# aliyun.accessSecret=******************************


speech.aliyun.default-config.app-key=3CTfUnySrlrNJffu
speech.aliyun.default-config.access-key=LTAI4GEqEDVLryGgpdv1UvwQ
speech.aliyun.default-config.access-secret=******************************
speech.aliyun.default-config.active=1
speech.aliyun.default-config.speech-noise-threshold=1
speech.aliyun.default-config.max-sentence-silence=800


voice.speech.path=/Users/<USER>/Desktop/tmp/

openai.url=http://chatgpt.tiny-test.wke-office.test.wacai.info/openai/deployments/%s/chat/completions
openai.api.key=2c26e7b252b7419a8aa9766f053c3cfa

ai-action.gateway-host=http://linkup-gateway.linkup-test.wke-office.test.wacai.info/

ai-action.direct-transfer-phones=13758171311

counselor.voice.type=MEDIATION-TEST
linkup.agent.url=http://link-conf.linkup-test.wke-office.test.wacai.info/

call-user-data-cache.campaign-host=http://**************:8080
ai-flow-node-service.default-queue-code=1108



link.agent.url=http://link-agent-ops.linkup-test.wke-office.test.wacai.info
link.state.url=http://link-state.linkup-test.wke-office.test.wacai.info


counselor.inbound.flow.057158100153={ "flowCode":"lucheng_satisfaction_inbound_20240716"}


web.interceptor.path-patterns=/front/**

chatgpt.url=http://chatgpt.tiny-test.wke-office.test.wacai.info/


ai-flow-node-voice-cache.cache-path=/Users/<USER>/Downloads/


notice-component.alarm.url=http://api.wke-cloud.test.wacai.info/alert/api/v1/outerevents
notice-component.alarm.receivers=shunhua



flow.wav-path=/Users/<USER>/Desktop/amx/temp/


linkup.tts.url=http://link-conf.linkup-test.wke-office.test.wacai.info/node/out-api/tts/generate