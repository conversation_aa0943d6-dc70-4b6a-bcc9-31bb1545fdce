<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:task="http://www.springframework.org/schema/task"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/task
        http://www.springframework.org/schema/task/spring-task.xsd">

    <bean id="simpleAsyncUncaughtExceptionHandler"
          class="org.springframework.aop.interceptor.SimpleAsyncUncaughtExceptionHandler" />

    <!--<bean class="com.wacai.loan.yo.sipphone.config.AccountsFactoryPostProcessor">-->
        <!--<constructor-arg value="${pjsip.account-allocation.zk.addr:}" />-->
        <!--<constructor-arg value="${pjsip.account-allocation.zk.path:/Sipphone/Nodes}" />-->
        <!--<constructor-arg value="${pjsip.account-allocation.count:0}" />-->
        <!--<constructor-arg value="${pjsip.sip-accounts:}" />-->
    <!--</bean>-->


</beans>
