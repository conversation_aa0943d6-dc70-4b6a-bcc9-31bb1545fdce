<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean class="org.pjsip.pjsua2.Endpoint" />
    <bean id="logbackWriter" class="com.wacai.loan.yo.pjsua2.LogbackWriter">
        <constructor-arg value="${LOG_HOME:.}/pjsua2.log" />
        <constructor-arg value="${pjsip.log.console-level:${pjsip.log.level:4}}" />
    </bean>
    <!-- !IMPORTANT! define global bean avoid Java GC cause JNI LIB error -->
    <bean id="pjsu2LogConfig" class="org.pjsip.pjsua2.LogConfig">
        <property name="level" value="${pjsip.log.level:4}" />
        <property name="consoleLevel" value="${pjsip.log.console-level:${pjsip.log.level:4}}" />
        <!--<property name="filename" value="${LOG_HOME:.}/pjsua2.log" />-->
        <property name="decor" value="${pjsip.log.decor:8256}" />
        <property name="writer" ref="logbackWriter" />
    </bean>
    <bean id="pjsua2MediaConfig" class="org.pjsip.pjsua2.MediaConfig">
        <property name="clockRate" value="${pjsip.media.clock-rate:${yo.realtime-asr.sample-rate:8000}}" />
      <!--  <property name="sndClockRate" value="${pjsip.media.clock-rate:${yo.realtime-asr.sample-rate:16000}}" />
        <property name="sndRecLatency" value="20" />-->
        <property name="maxMediaPorts" value="${pjsip.media.maxMediaPorts:128}"/>

    </bean>
    <bean id="pjsua2UaConfig" class="org.pjsip.pjsua2.UaConfig">
        <property name="userAgent" value="${pjsip.ua.user-agent:AIUa}"/>
        <property name="maxCalls" value="${pjsip.ua.max-calls:128}"/>
        <property name="threadCnt" value="${pjsip.ua.threadCnt:1}"/>
        <property name="mainThreadOnly" value="${pjsip.ua.main-thread-only:false}"/>
    </bean>
    <bean class="org.pjsip.pjsua2.EpConfig">
        <property name="logConfig" ref="pjsu2LogConfig" />
        <property name="medConfig" ref="pjsua2MediaConfig" />
        <property name="uaConfig" ref="pjsua2UaConfig" />
    </bean>
    <bean class="org.pjsip.pjsua2.TransportConfig">
        <property name="port" value="${pjsip.sip-port:6060}" />
    </bean>
</beans>
