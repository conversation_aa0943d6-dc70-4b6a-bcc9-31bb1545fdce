<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:websocket="http://www.springframework.org/schema/websocket"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/websocket
        http://www.springframework.org/schema/websocket/spring-websocket.xsd">

    <!-- Websocket Server -->
<!--    <websocket:message-broker application-destination-prefix="/ws">-->
<!--        <websocket:stomp-endpoint path="/stomp" allowed-origins="*">-->
<!--            <websocket:sockjs heartbeat-time="10000" />-->
<!--        </websocket:stomp-endpoint>-->
<!--        <websocket:simple-broker prefix="/topic" />-->
<!--    </websocket:message-broker>-->


    <!-- Websocket Client -->
    <!--<bean class="com.wacai.loan.yo.sipphone.websocket.StompSessionHandlerAdapterImpl">-->
        <!--<constructor-arg>-->
            <!--<bean class="org.springframework.web.socket.messaging.WebSocketStompClient">-->
                <!--<constructor-arg>-->
                    <!--<bean class="org.springframework.web.socket.sockjs.client.SockJsClient">-->
                        <!--<constructor-arg>-->
                            <!--<list>-->
                                <!--<bean class="org.springframework.web.socket.sockjs.client.WebSocketTransport">-->
                                    <!--<constructor-arg>-->
                                        <!--<bean class="org.springframework.web.socket.client.standard.StandardWebSocketClient" />-->
                                    <!--</constructor-arg>-->
                                <!--</bean>-->
                            <!--</list>-->
                        <!--</constructor-arg>-->
                    <!--</bean>-->
                <!--</constructor-arg>-->
                <!--<property name="messageConverter">-->
                    <!--<bean class="org.springframework.messaging.converter.MappingJackson2MessageConverter" />-->
                <!--</property>-->
                <!--<property name="taskScheduler">-->
                    <!--<bean class="org.springframework.scheduling.concurrent.ConcurrentTaskScheduler" />-->
                <!--</property>-->
            <!--</bean>-->
        <!--</constructor-arg>-->
        <!--<constructor-arg value="${yo.sipphone.call-data.stomp-url}" />-->
    <!--</bean>-->
</beans>
