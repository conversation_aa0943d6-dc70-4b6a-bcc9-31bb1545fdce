package com.wacai.loan.counselor.voice.starter.auth;

import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;

/**
 * @author: qichen
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LoginAuthenticationWebConfig implements WebMvcConfigurer {

    @Value("${web.interceptor.exclude-path-patterns:}")
    private String[] excludePathPatterns;

    @Value("${web.interceptor.path-patterns:}")
    private String[] pathPatterns;

    private final LoginCheckInterceptor loginCheckInterceptor;

    @Autowired
    public LoginAuthenticationWebConfig(LoginCheckInterceptor loginCheckInterceptor) {
        this.loginCheckInterceptor = loginCheckInterceptor;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        if (ArrayUtils.isEmpty(pathPatterns)) {
            return;
        }
        registry.addInterceptor(loginCheckInterceptor)
            .addPathPatterns(pathPatterns)
            .excludePathPatterns(excludePathPatterns);
    }

}
