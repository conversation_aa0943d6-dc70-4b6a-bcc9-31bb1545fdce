package com.wacai.loan.counselor.voice.starter.controller.model;

import lombok.Data;

/**
 * Created With IntelliJ IDEA.
 *
 * @author: jindanzi
 * @date: 2020-02-14 下午4:44
 * @description: ${DESCRIPTION}
 */
@Data
public class DialResultDTO {
    public static final int FAIL = 0;
    public static final int SUCCESS = 1;
    public static final int INVALID = 2;
    public static final int BIZ_FILTER = 3;

    /**
     * 手机号
     */
    private String mobile;
    /**
     * 业务方自定义数据（随路数据）
     */
    private String userData;

    /**
     * batchNo（批次号）
     */
    private String batchNo;

    /**
     * 拨打状态：（0：失败，1：成功，2：无效）
     */
    private Integer callStatus;

    /**
     * 通话时长
     */
    private Integer duration;

    /**
     * 通话开始时间
     */
    private String startTime;

    /**
     * 通话结束时间
     */
    private String endTime;

    /**
     * 拨打结果码
     */
    private Integer resultCode;

    /**
     * 拨打结果信息
     */
    private String resultMsg;

    /**
     * 菜单输入
     */
    private String inputMenu;

    /**
     * 坐席ID
     */
    private String agentId;

    /**
     * 通话ID
     */
    private String connId;

    /**
     * 拨打时间
     */
    private String callTime;

    /**
     * 拨打次数
     */
    private Integer attempt;

    /**
     * campaignKey
     */
    private String campaignKey;

    /**
     * 前缀
     */
    private String prefix;

    /**
     * 拨打轮次
     */
    private Integer round;
}
