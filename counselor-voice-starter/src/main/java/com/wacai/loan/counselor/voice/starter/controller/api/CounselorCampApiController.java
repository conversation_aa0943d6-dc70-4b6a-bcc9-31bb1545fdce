/*
 * @Author: shunhua
 * 
 * @Date: 2025-06-05 15:59:30
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-01 14:21:08
 * 
 * @Description:
 */
package com.wacai.loan.counselor.voice.starter.controller.api;

import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.wacai.loan.counselor.common.model.WebApiResponse;
import com.wacai.loan.counselor.dao.po.AiFlow;
import com.wacai.loan.counselor.opt.flow.manager.AiFlowManager;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/v1/campaign")
@Slf4j
public class CounselorCampApiController {

    @Resource
    private AiFlowManager aiFlowManager;

    @GetMapping("/isAiOfCampCode")
    public WebApiResponse<Boolean> isAiOfCampCode(@RequestParam String campCode) {
        AiFlow aiFlows = aiFlowManager.findAiFlowByCampCode(campCode);
        return WebApiResponse.success(Objects.nonNull(aiFlows)?true:false);
    }
}
