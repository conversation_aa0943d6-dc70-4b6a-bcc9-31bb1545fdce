package com.wacai.loan.counselor.voice.starter.aop;

import com.wacai.loan.counselor.common.model.WebApiResponse;

import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/4 16:07
 */
@ControllerAdvice
@Slf4j
public class ExceptionHandlerControllerAdvice {

    @ResponseBody
    @ExceptionHandler
    public WebApiResponse<Void> handlerException(Exception e) {
        return ExceptionHandlerUtility.handlerException(e);
    }
}
