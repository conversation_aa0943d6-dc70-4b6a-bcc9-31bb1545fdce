/*
 * @Author: shunhua
 * 
 * @Date: 2025-08-14 14:11:42
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-14 14:13:57
 * 
 * @Description:
 */
package com.wacai.loan.counselor.voice.starter.controller;

import java.io.File;
import java.util.UUID;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wacai.loan.counselor.common.model.WebApiResponse;
import com.wacai.loan.counselor.opt.flow.service.RemoteVoiceService;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/tts")
@Slf4j
public class LinkupTTSController {

    @Resource
    private RemoteVoiceService remoteVoiceService;

    @GetMapping("/generate")
    public WebApiResponse<String> generate() throws Exception {
        File file =
                remoteVoiceService.generalTTSFile(UUID.randomUUID().toString(), "测试生成语音的功能，应该还可以吧");
        return WebApiResponse.success(file.getAbsolutePath());
    }
}
