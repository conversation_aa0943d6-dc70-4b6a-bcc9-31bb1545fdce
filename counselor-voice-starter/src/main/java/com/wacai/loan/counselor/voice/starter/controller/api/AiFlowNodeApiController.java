/*
 * @Author: shunhua
 * 
 * @Date: 2025-05-21 15:11:10
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-18 11:13:43
 * 
 * @Description:
 */
package com.wacai.loan.counselor.voice.starter.controller.api;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.wacai.loan.counselor.common.model.WebApiResponse;
import com.wacai.loan.counselor.dao.po.AiFlow;
import com.wacai.loan.counselor.dao.po.AiFlowNode;
import com.wacai.loan.counselor.dao.po.AiFlowToNode;
import com.wacai.loan.counselor.opt.flow.manager.AiFlowManager;
import com.wacai.loan.counselor.opt.flow.manager.AiFlowNodeManager;
import com.wacai.loan.counselor.opt.flow.manager.AiFlowToNodeManager;
import com.wacai.loan.counselor.voice.starter.controller.api.dataobject.AiFlowDetailResponse;
import com.wacai.loan.counselor.voice.starter.controller.api.dataobject.AiFlowNodeTagRequest;
import com.wacai.loan.counselor.voice.starter.controller.api.dataobject.AiflowNodeResponse;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api")
@Slf4j
public class AiFlowNodeApiController {

    @Resource
    private AiFlowManager aiFlowManager;

    @Resource
    private AiFlowNodeManager aiFlowNodeManager;

    @Resource
    private AiFlowToNodeManager aiFlowToNodeManager;

    @PostMapping("/v1/tagFlowNode")
    public WebApiResponse<Boolean> tagAiFlowNode(@Valid @RequestBody AiFlowNodeTagRequest request,
            BindingResult bindingResult) {
        try {
            if (bindingResult.hasErrors()) {
                String errorMsg = bindingResult.getFieldErrors().stream()
                        .map(error -> error.getField() + ": " + error.getDefaultMessage())
                        .findFirst().orElse("参数校验失败");
                return WebApiResponse.error(errorMsg);
            }
            AiFlow aiFlow = aiFlowManager.getAiFlowByCode(request.getFlowCode());
            if (Objects.isNull(aiFlow)) {
                return WebApiResponse.error("flowCode不存在");
            }
            AiFlowNode aiFlowNode = aiFlowNodeManager.getFlowNodeByCode(request.getFlowNodeCode());
            if (Objects.isNull(aiFlowNode)) {
                return WebApiResponse.error("flowNodeCode不存在");
            }
            aiFlowNode.setTag(request.getFlowNodeTag());
            aiFlowNodeManager.add(aiFlowNode);
            log.info("tagAiFlowNode param : {}  succes ", request.toString());
            return WebApiResponse.success(true);
        } catch (Exception e) {
            log.error("tagAiFlowNode param: {}  err", request.toString(), e);
            return WebApiResponse.error("tagAiFlowNode err");
        }
    }

    @GetMapping("/v1/listAiFlowNode")
    public WebApiResponse<AiFlowDetailResponse> listAiFlowDetail(
            @RequestParam(required = false) String flowCode,
            @RequestParam(required = false) String campCode) {

        try {
            if (StringUtils.isAllEmpty(flowCode, campCode)) {
                return WebApiResponse.error("flowCode和campCode不能同时为空");
            }
            AiFlow aiFlow = null;
            if (StringUtils.isNotBlank(campCode)) {
                 aiFlow = aiFlowManager.findAiFlowByCampCode(campCode);
                if (Objects.isNull(aiFlow)) {
                    return WebApiResponse.error("campCode 未配置流程或对于多个流程");
                }
            } else if (StringUtils.isNotBlank(flowCode)) {
                aiFlow = aiFlowManager.getAiFlowByCode(flowCode);
                if (Objects.isNull(aiFlow)) {
                    return WebApiResponse.error("flowCode不存在");
                }
            } else {
                return WebApiResponse.error("缺少参数");
            }
            if (Objects.isNull(aiFlow)) {
                return WebApiResponse.error("流程未配置或参数错误");
            }
            List<AiFlowToNode> aiFlowToNodes =
                    aiFlowToNodeManager.listByAiFlowCode(aiFlow.getCode());
            if (CollectionUtils.isEmpty(aiFlowToNodes)) {
                return WebApiResponse.error("请求参数未配置子流程节点");
            }
            List<AiflowNodeResponse> responses = aiFlowToNodes.stream().map(x -> {
                AiFlowNode aiFlowNode = aiFlowNodeManager.getFlowNodeByCode(x.getAiFlowNodeCode());
                if (Objects.isNull(aiFlowNode)) {
                    return null;
                }
                AiflowNodeResponse aiflowNodeResponse = new AiflowNodeResponse();
                BeanUtils.copyProperties(aiFlowNode, aiflowNodeResponse);
                return aiflowNodeResponse;
            }).filter(Objects::nonNull).collect(Collectors.toList());

            return WebApiResponse.success(new AiFlowDetailResponse(aiFlow.getCode(),
                    aiFlow.getName(), aiFlow.getCampaignCode(), responses));
        } catch (Exception e) {
            log.error("list aiflowDetail error ,param : {},{},err: {}", flowCode, campCode,
                    e.getMessage());
            return WebApiResponse.error("list aiflowDetail error");
        }
    }
}
