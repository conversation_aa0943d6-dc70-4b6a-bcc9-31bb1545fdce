/*
 * @Author: shunhua
 * 
 * @Date: 2025-07-21 19:31:54
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-31 19:51:25
 * 
 * @Description:
 */
package com.wacai.loan.counselor.voice.starter.controller;

import java.util.List;
import javax.annotation.Resource;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.alibaba.fastjson2.TypeReference;
import com.wacai.loan.counselor.common.model.WebApiResponse;
import com.wacai.loan.counselor.opt.flow.cache.WorkFlowLoadCache;
import com.wacai.loan.counselor.rebalance.AgentInstanceManager;
import com.wacai.loan.counselor.utils.InetAddressUtil;
import com.wacai.loan.counselor.utils.OkHttpUtil;
import com.wacai.loan.counselor.utils.Utils;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/workFlowNode")
@Slf4j
public class WorkFlowNodeController {

    @Resource
    private AgentInstanceManager agentInstanceManager;

    @Resource
    private WorkFlowLoadCache workFlowLoadCache;


    @GetMapping("/cluster/invalidCache")
    public WebApiResponse<String> invalidAiFlowNodeCacheCluster() {
        StringBuilder builder = new StringBuilder();
        List<String> instances = agentInstanceManager.getzkInstanceChildren();
        instances.stream().forEach(x -> {
            WebApiResponse<String> response = OkHttpUtil.getInstance().get(
                    Utils.packageUrl(x).concat("/workFlowNode/invalidCache"), Maps.newHashMap(),
                    null, new TypeReference<WebApiResponse<String>>() {});
            if (!response.isSuccess()) {
                log.error("invalidAiFlowNodeCacheCluster error: {}, instance: {}",
                        response.getError(), x);
                builder.append("实例: ").append(x).append("，错误信息：").append(response.getError())
                        .append("\n");
            } else {
                log.info("invalidAiFlowNodeCacheCluster success, instance: {}", x);
            }
        });
        if (instances.isEmpty()) {
            log.info("invalidAiFlowNodeCacheCluster no instance found, instance: {}",
                    InetAddressUtil.getIpAddress());
            return WebApiResponse.error("no instance found");
        }
        if (builder.length() > 0) {
            log.error("invalidAiFlowNodeCacheCluster error: {}", builder.toString());
            return WebApiResponse.error(builder.toString());
        }
        return WebApiResponse.success("success");
    }

    @GetMapping("/invalidCache")
    public WebApiResponse<String> invalidCacheCluster() {
        try {
            log.info("invalidCacheCluster instance:【{}】 start...", InetAddressUtil.getIpAddress());
            workFlowLoadCache.initWorkFlow();
            log.info("invalidCacheCluster instance:【{}】 end...", InetAddressUtil.getIpAddress());
            return WebApiResponse.success("success");
        } catch (Exception e) {
            log.error("invalidCacheCluster error: ", e);
            return WebApiResponse.error(e.getMessage());
        }
    }
}
