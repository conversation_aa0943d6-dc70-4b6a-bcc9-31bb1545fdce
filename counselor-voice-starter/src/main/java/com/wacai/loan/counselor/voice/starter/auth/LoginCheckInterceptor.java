/*
 * @Author: shunhua
 * 
 * @Date: 2025-08-13 19:40:46
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-18 11:11:14
 * 
 * @Description:
 */
package com.wacai.loan.counselor.voice.starter.auth;

import java.net.URI;
import java.time.Duration;
import java.util.Optional;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import com.alibaba.fastjson2.JSONObject;
import com.wacai.loan.counselor.auth.UserCtx;
import com.wacai.loan.counselor.auth.UserInfo;
import com.wacai.loan.counselor.utils.WebClientUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * qichen
 */
@Component
@Slf4j
public class LoginCheckInterceptor implements HandlerInterceptor {
    @Value("${api.linkup-auth.domain:http://linkup-auth:8080}")
    private String authDomain;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response,
            Object handler) throws Exception {
        String session = request.getHeader("X-Login-Session");
        if (StringUtils.isBlank(session)) {
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "X-Login-Session为空");
            return false;
        }
        try {
            getAndSetUserInfo(session);
        } catch (Exception e) {
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, e.getMessage());
            return false;
        }

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
            Object handler, Exception ex) throws Exception {
        UserCtx.remove();
    }

    public void getAndSetUserInfo(String session) {
        final String baseUrl = authDomain + "/api/v1/user";

        final Optional<String> stringOptional = WebClientUtils.getWebClient().get()
                .uri(URI.create(baseUrl + "?sessionid=" + session)).retrieve()
                .bodyToMono(String.class).blockOptional(Duration.ofSeconds(10));

        if (!stringOptional.isPresent()) {
            throw new RuntimeException("获取用户信息失败");
        }

        final JSONObject jsonObject = JSONObject.parseObject(stringOptional.get());

        if (jsonObject.getInteger("code") != 0) {
            throw new RuntimeException("获取用户信息失败");
        }
        UserInfo userInfo = jsonObject.getObject("data", UserInfo.class);
        if (!userInfo.isActive()) {
            throw new RuntimeException("用户未激活");
        }

        UserCtx.set(userInfo);
    }

}
