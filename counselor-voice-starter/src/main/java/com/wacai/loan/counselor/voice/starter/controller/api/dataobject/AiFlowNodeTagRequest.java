/*
 * @Author: shunhua
 * @Date: 2025-05-21 15:13:32
 * @LastEditors: shunhua
 * @LastEditTime: 2025-05-26 16:09:45
 * @Description: 
 */
package com.wacai.loan.counselor.voice.starter.controller.api.dataobject;

import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class AiFlowNodeTagRequest {

    @NotBlank(message = "flowCode不能为空")
    private String flowCode;

    @NotBlank(message = "flowNodeCode不能为空")
    private String flowNodeCode;

    @NotBlank(message = "flowNodeTag不能为空")
    private String flowNodeTag;
}
