/*
 * @Author: shunhua
 * @Date: 2025-05-21 14:29:03
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-01 14:52:13
 * @Description: 
 */
package com.wacai.loan.counselor.voice.starter.controller.api.dataobject;

import java.util.Date;
import com.wacai.loan.counselor.opt.base.jsonformat.JsonDataFormat;
import lombok.Data;

@Data
public class UserInputDTO {

      /**
     * 用户输入
     */
    private String input;

    /**
     * uuid
     */

    private String callUuid;

    /**
     * ai提示
     */
    private String tip;

    /**
     * 用户电话
     */
    private String phone;

    /**
     * 备注
     */
    private String comment;

    /**
     * 创建时间
     */
    @JsonDataFormat
    private Date createdTime;

    /**
     * 流程 code
     */

    private String flowCode;


    private String flowNodeCode;

    private String flowNodeName;

    private String flowNodeTags;

    private Boolean transfer;

    private String semantics;

    private String nodeContext;
}
