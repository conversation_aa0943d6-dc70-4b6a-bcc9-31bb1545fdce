/*
 * @Author: shunhua
 * @Date: 2025-05-30 10:10:09
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-01 15:06:09
 * @Description: 
 */
package com.wacai.loan.counselor.voice.starter;

import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.alibaba.fastjson2.JSON;
import com.wacai.loan.counselor.opt.sip.dataobject.agent.AgentDTO;
import com.wacai.loan.counselor.rebalance.LinkupAccountService;
import com.wacai.loan.counselor.rebalance.zookeeper.ZkLeaderService;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-20 16:56
 */
@Service
@Slf4j
public class CounselorLoader {

    @Resource
    private ZkLeaderService zkLeaderService;

    @Resource
    private LinkupAccountService linkupAccountService;

    public void loader() {
        List<AgentDTO> agentDTOS = linkupAccountService.listRobotAgent();
        log.info("account = {}", JSON.toJSONString(agentDTOS));
        if (CollectionUtils.isEmpty(agentDTOS)) {
            throw new IllegalArgumentException("agent Account is Empty");
        }
        zkLeaderService.selector();
    }

    public void stop() {
        zkLeaderService.close();
    }
}
