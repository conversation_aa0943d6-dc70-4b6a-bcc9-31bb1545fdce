/*
 * @Author: shunhua
 * @Date: 2025-05-21 15:26:13
 * @LastEditors: shunhua
 * @LastEditTime: 2025-05-21 15:45:50
 * @Description: 
 */
package com.wacai.loan.counselor.voice.starter.controller.api.dataobject;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AiFlowDetailResponse {
   
     private String flowCode;

     private String flowName;

     private String campCode;

     private List<AiflowNodeResponse> flowNodeList;
}
