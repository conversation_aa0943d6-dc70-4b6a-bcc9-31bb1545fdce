/*
 * @Author: shunhua
 * 
 * @Date: 2024-03-12 11:09:23
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-01 15:44:35
 * 
 * @Description:
 */
package com.wacai.loan.counselor.voice.starter.controller.api;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.wacai.loan.counselor.common.model.WebApiResponse;
import com.wacai.loan.counselor.dao.po.AiFlow;
import com.wacai.loan.counselor.dao.po.AiFlowNode;
import com.wacai.loan.counselor.dao.po.UserInput;
import com.wacai.loan.counselor.opt.flow.manager.AiFlowManager;
import com.wacai.loan.counselor.opt.flow.manager.AiFlowNodeManager;
import com.wacai.loan.counselor.opt.flow.service.UserInputService;
import com.wacai.loan.counselor.voice.starter.controller.api.dataobject.UserInputDTO;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/21 14:14
 */
@RestController
@RequestMapping("/user-input")
@Slf4j
public class UserInputRecordController {

    @Resource
    private UserInputService userInputService;

    @Resource
    private AiFlowManager aiFlowManager;

    @Resource
    private AiFlowNodeManager aiFlowNodeManager;


    @GetMapping("/listUserIdByCallId")
    public WebApiResponse<List<UserInputDTO>> listUserInputByCallId(@RequestParam String callId,
            @RequestParam String campCode) {
        try {
            List<UserInput> userInputs = userInputService.listUserInputByCallId(callId);
            if (CollectionUtils.isEmpty(userInputs)) {
                return WebApiResponse.success(Lists.newArrayList());
            }
            AiFlow aiFlow = aiFlowManager.findAiFlowByCampCode(campCode);
            List<UserInputDTO> userInputDTOs =
                    userInputs.stream().sorted(Comparator.comparing(UserInput::getId)).map(x -> {
                        AiFlowNode aiFlowNode =
                                aiFlowNodeManager.getFlowNodeByCode(x.getFlowCode());
                        UserInputDTO userInputDTO = new UserInputDTO();
                        BeanUtils.copyProperties(x, userInputDTO);
                        if (Objects.nonNull(aiFlow)) {
                            userInputDTO.setFlowCode(aiFlow.getCode());
                        }
                        userInputDTO.setFlowNodeCode(x.getFlowCode());
                        userInputDTO.setFlowNodeName(aiFlowNode.getName());
                        userInputDTO.setFlowNodeTags(aiFlowNode.getTag());
                        return userInputDTO;
                    }).collect(Collectors.toList());
            return WebApiResponse.success(userInputDTOs);
        } catch (Exception e) {
            log.error("callId: {},campCode: {} listUserInputByCallId err", callId, campCode, e);
            return WebApiResponse.error("listUserInputByCallId err");
        }
    }
}
