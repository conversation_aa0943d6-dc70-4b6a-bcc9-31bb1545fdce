/*
 * @Author: shunhua
 * 
 * @Date: 2025-04-02 17:09:57
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-07-21 19:30:51
 * 
 * @Description:
 */
package com.wacai.loan.counselor.voice.starter.controller;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wacai.loan.counselor.common.model.WebApiResponse;
import com.wacai.loan.counselor.opt.sip.dataobject.agent.AgentDTO;
import com.wacai.loan.counselor.opt.sip.sipsession.AiAgentRegistryService;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-23 21:59
 */
@RestController
@RequestMapping("/voice")
@Slf4j
public class AgentAccountController {

    @Resource
    private AiAgentRegistryService aiAgentRegistryService;

    @PostMapping("/allotAccount")
    public WebApiResponse<String> allotAccount(@RequestBody List<AgentDTO> accounts)
            throws Exception {
        log.info("allotAccount :{}",
                accounts.stream().map(x -> x.getSipAccount()).collect(Collectors.joining(",")));
        aiAgentRegistryService.registryAccount(accounts);
        return WebApiResponse.success("success");
    }

}
