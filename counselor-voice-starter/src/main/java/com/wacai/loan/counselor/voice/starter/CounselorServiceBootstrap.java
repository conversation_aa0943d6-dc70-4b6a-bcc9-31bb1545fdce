/*
 * @Author: shunhua
 * @Date: 2024-09-26 11:43:45
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-25 18:58:00
 * @Description: 
 */
package com.wacai.loan.counselor.voice.starter;

import javax.annotation.Resource;
import org.springframework.boot.Banner;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.lang.NonNull;
import com.wacai.loan.counselor.opt.base.threadpool.ExecutorServiceHandlerRegistry;
import com.wacai.loan.counselor.utils.OkHttpUtil;
import com.wacai.loan.yo.pjsua2.Pjsua2ShareLibLoader;
import lombok.extern.slf4j.Slf4j;

/**
 * @Created by katana 2022/8/10 15:50
 */
@Slf4j
@SpringBootApplication
@EnableConfigurationProperties
@EnableDiscoveryClient
@ImportResource("classpath:spring/*.xml")
public class CounselorServiceBootstrap implements CommandLineRunner, ApplicationListener<ContextClosedEvent> {

    static {
        Pjsua2ShareLibLoader.load();
    }

    @Resource
    private CounselorLoader counselorLoader;

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(CounselorServiceBootstrap.class);
        app.setBannerMode(Banner.Mode.CONSOLE);
        app.run(args);
    }

    @Override
    public void run(String... args) throws Exception {
        counselorLoader.loader();
        OkHttpUtil.getInstance(); //提前加载
    }

    @Override
    public void onApplicationEvent(@NonNull ContextClosedEvent event) {
        counselorLoader.stop();
        ExecutorServiceHandlerRegistry.shuntDown();
    }
}
