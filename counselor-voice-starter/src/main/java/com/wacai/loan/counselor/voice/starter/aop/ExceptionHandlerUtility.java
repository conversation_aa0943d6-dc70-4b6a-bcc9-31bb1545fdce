package com.wacai.loan.counselor.voice.starter.aop;

import com.wacai.loan.counselor.common.model.WebApiResponse;

import javax.validation.ConstraintViolationException;

import org.springframework.web.bind.MethodArgumentNotValidException;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/27 16:10
 */
@UtilityClass
@Slf4j
public class ExceptionHandlerUtility {

    // 全局异常拦截（拦截项目中的所有异常）
    public WebApiResponse<Void> handlerException(Exception e) {

        log.error("happened error:", e);

        if (e instanceof MethodArgumentNotValidException) {
            return WebApiResponse.error(
                ((MethodArgumentNotValidException) e).getBindingResult().getFieldError().getDefaultMessage());
        }

        if (e instanceof ConstraintViolationException) {
            return WebApiResponse.error(
                ((ConstraintViolationException) e).getConstraintViolations().stream().findFirst().get().getMessage());
        }

        return WebApiResponse.error(e.getMessage());
    }
}
