/*
 * @Author: shunhua
 * 
 * @Date: 2025-08-14 11:02:34
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-18 11:14:12
 * 
 * @Description:
 */
package com.wacai.loan.counselor.utils;

import java.io.File;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import org.junit.Test;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ExectuorTest {

  private static final ExecutorService executorService =Executors.newCachedThreadPool();

 

    private static final Map<String, CompletableFuture<File>> downloadingTasks =
            Maps.newConcurrentMap();

    @Test
    public void test() throws InterruptedException {
        ExecutorService executorService1 = Executors.newSingleThreadExecutor();
        executorService1.submit(()->{
            String hash = "3333";
            String callId = "linkup333334";
            log.info("444444444444444");
            CompletableFuture<File> future =
                    downloadingTasks.compute(hash, (key, existingFuture) -> {
                        if (Objects.nonNull(existingFuture)) {
                            log.info("callId: {} Reusing existing future for hash: {}, isDone: {}",
                                    callId, hash, existingFuture.isDone());
                            if (existingFuture.isDone()) {
                                log.warn(
                                        "callId: {} Existing future is done, creating new one for hash: {}",
                                        callId, hash);
                            } else {
                                return existingFuture;
                            }
                        }


                        CompletableFuture<File> newFuture = CompletableFuture.supplyAsync(() -> {
                            try {
                                // log.info("callId: {} Starting downloadVoiceFile for hash: {}",
                                // callId, hash);
                                File downloaded = downloadVoiceFile(callId);
                                if (downloaded != null && downloaded.exists()
                                        && downloaded.isFile()) {
                                            log.info("callId----->down");
                                    return downloaded;
                                }
                                throw new RuntimeException("下载失败或文件不存在");
                            } finally {
                                // log.info("callId: {} Removing hash from downloadingTasks: {}",
                                // callId, hash);
                                downloadingTasks.remove(hash); // 下载完成或失败后清理
                            }
                        }, executorService);
                        return newFuture;
                    });

            try

            {
                future.get(30, TimeUnit.SECONDS); // 等待下载结果，超时60秒
                log.info("3333333333333");
            } catch (TimeoutException e) {
                log.error("callId: {} acquireVoice timed out for hash: {}, param: {}", e);

            }catch(InterruptedException ex){
                log.error("callId: {} acquireVoice interrupted for hash: {}, param: {}", ex);
                future.cancel(true);
            }
             catch (Exception ex) {
                log.error("  acquireVoice failed for hash:", ex);

            }
        });
        TimeUnit.SECONDS.sleep(3);
        executorService1.shutdownNow();
        executorService1=null;
        log.info("executorService1 shutdown");
        TimeUnit.SECONDS.sleep(30);
    }

    private File downloadVoiceFile(String callId) {
        try {
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            e.printStackTrace();  
        }
         if (Thread.currentThread().isInterrupted()) {
            log.warn("检测到中断，退出下载");
            return null;
        }
        log.info("downloadVoiceFile======>");
       return new File("/Users/<USER>/Desktop/amx/zyxm/zyxm_repay_34.wav");
    
    }
}
