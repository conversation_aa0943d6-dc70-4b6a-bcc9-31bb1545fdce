/*
 * @Author: shunhua
 * @Date: 2025-06-05 10:17:33
 * @LastEditors: shunhua
 * @LastEditTime: 2025-07-22 10:18:52
 * @Description: 
 */
package com.wacai.loan.counselor;

import java.util.List;
import java.util.concurrent.TimeUnit;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.junit.Before;
import org.junit.Test;

public class ZKUtils {

    private CuratorFramework curatorFramework;
   
    @Before
    public void before(){
         CuratorFrameworkFactory.Builder builder = CuratorFrameworkFactory.builder().connectString("zktestserver1.wacai.info:22181,zktestserver2.wacai.info:22181,zktestserver3.wacai.info:22181")
            .retryPolicy(new ExponentialBackoffRetry(10000, 3));
        curatorFramework = builder.build();
        curatorFramework.start();
        try {
            curatorFramework.blockUntilConnected(10000 * 3, TimeUnit.MILLISECONDS);
          //  log.info("Counselor-voice init:zookeeper registry center success");
        } catch (final Exception ex) {
          //  log.error(" Counselor-voice zookeeper init exception : ", ex);
        }
    }
    @Test
    public void zkTest() throws Exception{
            List<String> list=curatorFramework.getChildren().forPath("/wacai/counselor/robotConfig");
            System.out.println(list);

    }
}
