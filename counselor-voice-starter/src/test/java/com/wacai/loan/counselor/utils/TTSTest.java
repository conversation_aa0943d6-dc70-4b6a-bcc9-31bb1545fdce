/*
 * @Author: shunhua
 * @Date: 2025-08-14 14:49:49
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-18 11:14:16
 * @Description: 
 */
package com.wacai.loan.counselor.utils;

import java.net.URI;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import org.junit.Test;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableMap;

public class TTSTest {


    @Test
    public void test(){
         try {
            String ttsUrl="http://link-conf.linkup-test.wke-office.test.wacai.info/node/out-api/tts/generate";
            String callId="2111333";
            JSONObject jsonObject = new JSONObject(ImmutableMap.of("text", "天下风雨出我22233333哈哈哈哈哈哈额呵呵"));
            JSONObject param =
                    new JSONObject(ImmutableMap.of("jsonData", JSON.toJSONString(jsonObject), "uuid", callId));
            WebClient.RequestHeadersSpec<?> requestHeadersSpec = WebClientUtils.getWebClient()
                    .post().uri(URI.create(ttsUrl)).contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(JSON.toJSONString(param))
                   .accept(MediaType.APPLICATION_OCTET_STREAM);
                   System.out.println(JSON.toJSONString(param));
         //   String targetName = FFmpegUtils.getTargetName(callId);
            final String fileT = "/Users/<USER>/Desktop" + "/" + callId + "_t.wav";
            final Path pathT = Paths.get(fileT);

            FFmpegUtils.delMayExistFile(pathT);

            requestHeadersSpec.retrieve().bodyToFlux(DataBuffer.class)
                            .doOnNext(dataBuffer -> FFmpegUtils.writeToFile(pathT, dataBuffer))
                            .then().block(Duration.ofSeconds(5));
           System.out.println(pathT.getFileName());
        } catch (Exception e) {
            
        }
    }
}
