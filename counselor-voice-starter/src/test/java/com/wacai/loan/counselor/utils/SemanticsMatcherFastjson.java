/*
 * @Author: shunhua
 * @Date: 2025-06-17 10:43:45
 * @LastEditors: shunhua
 * @LastEditTime: 2025-08-25 10:48:39
 * @Description: 
 */
package com.wacai.loan.counselor.utils;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

public class SemanticsMatcherFastjson {

    static class Rule {
        public List<String> suitWords;
        public String semantics;
    }

    public static String matchSemantics(String input, List<Rule> rules) {
        if (rules == null)
            return null;
        for (Rule rule : rules) {
            if (rule.suitWords == null)
                continue;
            for (String regex : rule.suitWords) {
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(input);
                if (matcher.find()) {
                    System.out.println("匹配到的正则表达式: " + regex);
                    return rule.semantics;
                }
            }
        }
        return null;
    }

    public static void main(String[] args) {
        String json = "[\n" + //
                        "    {\n" + //
                        "    \"suitWords\": [\n" + //
                        "        \"(?i)(您好.*?(电话|用户)?.*?(暂时|当前|无法|停机|欠费|关机).*?接通)\",\n" + //
                        "        \"(?i)^.*(正在通话中|暂时无法接听|正忙|无法接听|通话中|正在通话).*\",\n" + //
                        "        \"(?i)^.*(助理(小王|小志|小李)?|小(艾|爱|秘|助)|秘书|(AI|智能|电话|语音)?助手|语音服务|电话助理|通讯管家|守护者|机主|录音).*\",\n" + //
                        "        \"(?i)^.*((语音)?留言|请(按一重录|留言)|滴声后留言|您所拨打的电话暂时无法接通).*\",\n" + //
                        "        \"(?i)(请按\\\\s*(一|二|三|四|五|1|2|3|4|5)(号)?[,、]?(.*?请按\\\\s*(一|二|三|四|五|6|7|8|9|1|2|3|4|5)))\",\n" + //
                        "        \"(?i)^.*(语音服务|系统自动回复|语音提示|AI语音).*\",\n" + //
                        "        \"(?i).*(拨打|电话|暂时|无法|接通|停机|欠费|关机|语音|助手|服务|通话|留言|录音|提示|提示音|温馨提示).*\"\n" + //
                        "    ],\n" + //
                        "    \"semantics\": \"15\"\n" + //
                        "},\n" + //
                        "    {\n" + //
                        "        \"suitWords\":\n" + //
                        "        [\n" + //
                        "            \"(?i)^.*(不是(我|啊|的|他|她|本人)?|不认识(你|他|她)?|不知道|谁(啊|呀|呢)?|搞错|找错|打错|没(有|是)).*([啊呀呢唉嗯哈哦]*)[，,。.！!？?~…～、]*$\"\n" + //
                        "        ],\n" + //
                        "        \"semantics\": \"22\"\n" + //
                        "    },\n" + //
                        "    {\n" + //
                        "        \"suitWords\":\n" + //
                        "        [\n" + //
                        "            \"(?i).*(你(是谁|哪位|从哪来的|哪找来的|怎么知道的|哪里来的)|你找(谁|他|她).*|你说你是谁|你找他有(什么|啥)事|你有什么事|你找他干嘛).*\",\n" + //
                        "            \"(?i).*(你猜|可能吧|算是吧|也许|怎么了|好吧|有点事|也对|是又怎样|说不清).*\",\n" + //
                        "            \"(?i).*(你说吧|你讲|有啥事|又怎么了|又来了|别废话|别绕弯子|讲重点|说重点|直说|你快点说|你别啰嗦).*\",\n" + //
                        "            \"(?i).*(听不清|喂喂喂|信号(不好|差)|你再说一遍|断断续续|没听清|断了|说慢点|重复一下|什么事).*\",\n" + //
                        "            \"(?i).*(你直接说|有事直说|说正事|你说啥|你讲啥|什么事|你说什么|你讲什么).*\",\n" + //
                        "            \"(?i)^\\\\s*([喂嗯啊哎哈哦唉]{1,2})\\\\s*[。？！!?.…~～、,，]*\\\\s*$\",\n" + //
                        "            \"(?i)^\\\\s*([喂嗯啊哎哈哦唉]{1,2})[，,]?\\\\s*你说\\\\s*[。？！!?.…~～、,，]*$\"\n" + //
                        "        ],\n" + //
                        "        \"semantics\": \"21\"\n" + //
                        "    },\n" + //
                        "    {\n" + //
                        "        \"suitWords\":\n" + //
                        "        [\n" + //
                        "            \"^(联系不到|无法联系|找不到).*本人$\"\n" + //
                        "        ],\n" + //
                        "        \"semantics\": \"8\"\n" + //
                        "    },\n" + //
                        "    {\n" + //
                        "        \"suitWords\":\n" + //
                        "        [\n" + //
                        "            \"(?i).*\\\\b(是我|我是(啊|呀|呢)?|我就是(啊|呀|啦|呢)?|在的|我在)\\\\b.*\",\n" + //
                        "            \"(?i)^.*(嗯+|啊+|喂+|唉+)?[,，\\\\s]*(对|对啊|对啦|对呢|对你说|对的)(啊|啦|呢)?[，。,！!……~～、,，]*$\",\n" + //
                        "            \"(?i)^.*(嗯+|啊+|唉+)?[,，\\\\s]*(对)[,，\\\\s]*(你说|你讲|你说吧|你讲吧)[，。,！!……~～、,，]*$\",\n" + //
                        "            \"(?i)^.*(嗯+|啊+|唉+)?[，,\\\\s]*(对)[，,\\\\s]*(你说|你讲|你说吧|你讲吧)[，。,！!……~～、,，]*[啊啦呢]*$\",\n" + //
                        "            \"(?i).*\\\\b(嗯+[,，\\\\s]*)?是([啊啦呢]*)\\\\b.*\",\n" + //
                        "            \"(?i).*你说(吧|就是|好啦|好吧).*\",\n" + //
                        "            \"(?i).*你说啥就是啥.*\",\n" + //
                        "            \"(?i).*\\\\b我.*(是啊|是的|我是(啊|啦)?|知道了|听着呢|清楚了|晓得了|我听见了)\\\\b.*\"\n" + //
                        "        ],\n" + //
                        "        \"semantics\": \"18\"\n" + //
                        "    }\n" + //
                        "]";

        List<Rule> rules = JSON.parseObject(json, new TypeReference<List<Rule>>() {});

        // 测试输入，这里你可以替换成任意你想测试的字符串
        String input = "电话已转。";

        String semantics = matchSemantics(input, rules);
        if (semantics != null) {
            System.out.println("匹配到的语义编号是：" + semantics);
        } else {
            System.out.println("无匹配的语义编号");
        }
    }
}
