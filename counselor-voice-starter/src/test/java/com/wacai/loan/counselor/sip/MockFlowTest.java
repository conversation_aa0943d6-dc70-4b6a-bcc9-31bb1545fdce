/*
 * @Author: shunhua
 * 
 * @Date: 2025-06-04 19:37:28
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-06-05 10:45:06
 * 
 * @Description:
 */
package com.wacai.loan.counselor.sip;

import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import com.alibaba.fastjson2.JSON;
import com.wacai.loan.counselor.opt.base.dto.DialEventDTO;
import com.wacai.loan.counselor.opt.sip.service.SipEventService;
import com.wacai.loan.counselor.voice.starter.CounselorServiceBootstrap;
import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CounselorServiceBootstrap.class,
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class MockFlowTest {

    @Resource
    private SipEventService sipEventService;

    @Test
    public void mockSip() throws InterruptedException {
        TimeUnit.SECONDS.sleep(80);
        String sipBody =
                "{\"type\":\"EVENT\",\"eventName\":\"ORIGINATE\",\"callType\":\"PREDICTION\",\"uuid\":\"linkup304935358\",\"targetNum\":\"13735539834\",\"dialTime\":\"1749035379431\",\"targetNumProvince\":\"浙江\",\"targetNumCity\":\"杭州\",\"hideTargetNum\":\"137****9834\",\"showNum\":\"057158100151\",\"callStatus\":\"DIAL_ANSWE\",\"comment\":\"\",\"show\":\"\",\"userData\":\"%7B%22kaseId%22%3A611599%2C%22bizType%22%3A%22AUTOMATIC%22%2C%22flowCode%22%3A%22ivr_par_test%22%2C%22remoteVoiceParam%22%3A%7B%22voiceFrom%22%3A%22aiqi%22%2C%22volume%22%3A%2251%22%2C%22intonation%22%3A%221%22%2C%22jsonData%22%3A%7B%22courtName%22%3A%22%E9%87%8D%E5%BA%86%E5%B8%82%E9%BB%94%E6%B1%9F%E5%8C%BA%E4%BA%BA%E6%B0%91%E6%B3%95%E9%99%A2%22%2C%22gender%22%3A%22%E5%85%88%E7%94%9F%22%2C%22loanAmt%22%3A%2210792.75%22%2C%22loanOverdueDays%22%3A299%2C%22loanPeriods%22%3A19%2C%22loanPlatformName%22%3A%22%E5%BF%AB%E8%B4%B7%22%2C%22loanRepayAmt%22%3A12261.75%2C%22loanTime%22%3A%222025%E5%B9%B41%E6%9C%888%E6%97%A5%22%2C%22orgName%22%3A%22%E9%87%8D%E5%BA%86%E7%BB%85%E5%90%88%E4%BC%81%E4%B8%9A%E7%AE%A1%E7%90%86%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%22%2C%22userName%22%3A%22%E5%BE%90%E4%BD%B3%E4%BC%9F%22%7D%2C%22vocalSpeed%22%3A%221%22%7D%2C%22batchId%22%3A531%2C%22collectionId%22%3A611599%7D\",\"hangupReason\":\"\",\"campCode\":\"f007b1cff3e34f01be07d825c00444f2\"}";
        DialEventDTO eventDTO = JSON.parseObject(sipBody, DialEventDTO.class);
        sipEventService.handler(eventDTO);
    }

}
