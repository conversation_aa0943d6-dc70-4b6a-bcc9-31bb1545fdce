/*
 * @Author: shunhua
 * 
 * @Date: 2025-08-01 11:39:29
 * 
 * @LastEditors: shunhua
 * 
 * @LastEditTime: 2025-08-13 11:23:50
 * 
 * @Description:
 */
package com.wacai.loan.counselor.flow;

import java.io.File;
import javax.annotation.Resource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import com.wacai.loan.counselor.opt.flow.cache.WorkFlowBOCache;
import com.wacai.loan.counselor.opt.flow.cache.WorkFlowLoadCache;
import com.wacai.loan.counselor.opt.flow.model.AiFlowBO;
import com.wacai.loan.counselor.opt.flow.service.RemoteVoiceService;
import com.wacai.loan.counselor.voice.starter.CounselorServiceBootstrap;
import lombok.extern.slf4j.Slf4j;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = CounselorServiceBootstrap.class)
@Slf4j
public class WorkFlowTest {

    @Resource
    private WorkFlowLoadCache workFlowLoadCache;

    @Resource
    private WorkFlowBOCache workFlowBOCache;

    @Resource
    private RemoteVoiceService remoteVoiceService;

    @Test
    public void flowTest() {
        System.out.println("start------------");
        AiFlowBO aiFlowBO = workFlowBOCache.getAiFlowByCode("AI_PPCY");
        System.out.println("-----------start-----");
         aiFlowBO = workFlowBOCache.getAiFlowByCode("AI_PPCY");
         System.out.println("-----------start11111-----");
          aiFlowBO = workFlowBOCache.getAiFlowByCode("AI_PPCY");
        System.out.println(aiFlowBO);
    }

    @Test
    public void remoteFile(){
       File file=  remoteVoiceService.generalTTSFile("linkup3333", "我们都是大家好啊，你好吗");

       System.out.println(file.getAbsolutePath());
    }
}
