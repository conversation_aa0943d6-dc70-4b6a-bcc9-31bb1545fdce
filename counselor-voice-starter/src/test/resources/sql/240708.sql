select *
from user_input
where flow_code in
      ('lucheng_satisfaction_20240617_reside_info', 'lucheng_satisfaction_20240617_record_phone_no_worry',
       'lucheng_satisfaction_20240617_record_phone', 'lucheng_satisfaction_20240617_question_again')
order by id desc;


select count(*)
from user_input
where flow_code like 'lucheng_satisfaction_20240617_%s'
order by id desc;


CREATE TABLE user_input_t AS
select *
from user_input
where flow_code like 'lucheng_satisfaction_20240617_%s';

alter table user_input
    add ai_semantics varchar(64) null comment 'ai识别的语义';

#0708

INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('平定-分期乐第一阶段', 'pd_fql_1', 'pd_fql_1', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('pd_fql_1', 'pd_fql_1',
        'pd_fql_1', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {


var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");



    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=ZA3ZWRGDBJ21&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "pd_fql_1";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/458",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);



INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('平定-分期乐第二阶段', 'pd_fql_2', 'pd_fql_2', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('pd_fql_2', 'pd_fql_2',
        'pd_fql_2', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {


var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");



    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=ZA3ZWRGDBJ21&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "pd_fql_2";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/457",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);


INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('平定-分期乐第三阶段', 'pd_fql_3', 'pd_fql_3', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('pd_fql_3', 'pd_fql_3',
        'pd_fql_3', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {


var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");



    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=ZA3ZWRGDBJ21&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "pd_fql_3";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/456",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);

INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('平定-分期乐-调解减免', 'pd_fql_tjjm', 'pd_fql_tjjm', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('pd_fql_tjjm', 'pd_fql_tjjm',
        'pd_fql_tjjm', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {


var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");



    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=ZA3ZWRGDBJ21&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "pd_fql_tjjm";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/452",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);



update ai_flow_node
set remote_voice = '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/468",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}'
where code = 'pd_fql_1';

update ai_flow_node
set remote_voice = '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/469",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}'
where code = 'pd_fql_2';

update ai_flow_node
set remote_voice = '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/470",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}'
where code = 'pd_fql_3';

update ai_flow_node
set remote_voice = '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/471",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}'
where code = 'pd_fql_tjjm';


#0709

INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('青浦-360第一阶段', 'qp_360_1', 'qp_360_1', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('qp_360_1', 'qp_360_1',
        'qp_360_1', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {


var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");



    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=3GUKRGBXJ416&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "qp_360_1";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/472",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);



INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('青浦-360第二阶段', 'qp_360_2', 'qp_360_2', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('qp_360_2', 'qp_360_2',
        'qp_360_2', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {


var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");



    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=3GUKRGBXJ416&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "qp_360_2";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/473",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);



INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('青浦-360第三阶段', 'qp_360_3', 'qp_360_3', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('qp_360_3', 'qp_360_3',
        'qp_360_3', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {


var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");



    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=3GUKRGBXJ416&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "qp_360_3";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/474",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);

## 0723

update ai_flow
set context=''
where code = 'lucheng_satisfaction_20240617';


## 0729
INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `context`)
VALUES ('衢州文旅问卷', 'quzhou_travel_20240729', 'quzhou_travel_20240729_kcb', '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": false,
    "interval": 5,
    "times": 1
  }
}');

update ai_flow
set context='
    {
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": false,
    "interval": 15,
    "times": 2
  }
}'
where code = 'quzhou_travel_20240729';


INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `end`, `trigger_input`,
                            `receive_simple_dtmf`,
                            `next_node_code_adapters`, `voice_path`, semantics_transform, script)
VALUES ('quzhou_travel_20240729_kcb', 'quzhou_travel_20240729_kcb',
        '您好!我们正在开展衢州市文旅出行调研送活动，现诚挚邀请您参与本次调研问卷，希望您能够给出客观的评价，请问你愿意参与吗?', 0, 0, 0, '
[
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_reside_info_no"
    ],
    "semantics": "no"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_reside_info_yes"
    ],
    "semantics": "yes"
  }
]

', '/quzhou_travel_20240729/qz_kcb.wav', '{
  "yes": "quzhou_travel_20240729_dizhi",
  "no": "quzhou_travel_20240729_sorryzaijian",
  "self": "quzhou_travel_20240729_kcb",
    "other_end":"quzhou_travel_20240729_sorryzaijian",
"next":"quzhou_travel_20240729_sorryzaijian"
}', '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var applicationContext = scriptContext.applicationContext;

  if (applicationContext.getBean("aiPatternServiceImpl").isSuit("lucheng_satisfaction_20240617_reside_info_other", input)) {
    var repeatTimes = call.getCallContext().get("repeatTimes");
    if (repeatTimes == null) {
      repeatTimes = 0;
    }

    //initialized status
    if (repeatTimes == 0) {
      {
        call.getCallContext().put("repeatTimes", ++repeatTimes);
        return "self";
      }
    } else {
      //has played
      //reset to zero
      call.getCallContext().put("repeatTimes", 0);
      return "other_end";
    }
  }

  return "next";
}
');



INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `end`, `trigger_input`,
                            `receive_simple_dtmf`,
                            `next_node_code_adapters`, `voice_path`, semantics_transform, script)
VALUES ('quzhou_travel_20240729_dizhi', 'quzhou_travel_20240729_dizhi',
        '好的，接下来的问题请您根据实际情况回答即可,您目前常驻在哪里呢？您可以告诉我哪个省哪个市。', 0, 0, 0, null, '/quzhou_travel_20240729/qz_dizhi.wav', '{
  "self": "quzhou_travel_20240729_dizhi",
    "other_end":"quzhou_travel_20240729_sorryzaijian",
"next":"quzhou_travel_20240729_mudi"
}', '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var applicationContext = scriptContext.applicationContext;

  if (applicationContext.getBean("aiPatternServiceImpl").isSuit("lucheng_satisfaction_20240617_reside_info_other", input)) {
    var repeatTimes = call.getCallContext().get("repeatTimes");
    if (repeatTimes == null) {
      repeatTimes = 0;
    }

    //initialized status
    if (repeatTimes == 0) {
      {
        call.getCallContext().put("repeatTimes", ++repeatTimes);
        return "self";
      }
    } else {
      //has played
      //reset to zero
      call.getCallContext().put("repeatTimes", 0);
      return "other_end";
    }
  }

  return "next";
}
');

#next
INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `end`, `trigger_input`,
                            `receive_simple_dtmf`,
                            `next_node_code_adapters`, `voice_path`, semantics_transform, script)
VALUES ('quzhou_travel_20240729_mudi', 'quzhou_travel_20240729_mudi',
        '您此次出行最主要的目的是什么呢？是来观光休闲的、探亲访友的、还是务工、出差呢?', 0, 0, 0, null, '/quzhou_travel_20240729/qz_mudi.wav', '{
  "self": "quzhou_travel_20240729_mudi",
    "other_end":"quzhou_travel_20240729_sorryzaijian",
"next":"quzhou_travel_20240729_guoye"
}', '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var applicationContext = scriptContext.applicationContext;

  if (applicationContext.getBean("aiPatternServiceImpl").isSuit("lucheng_satisfaction_20240617_reside_info_other", input)) {
    var repeatTimes = call.getCallContext().get("repeatTimes");
    if (repeatTimes == null) {
      repeatTimes = 0;
    }

    //initialized status
    if (repeatTimes == 0) {
      {
        call.getCallContext().put("repeatTimes", ++repeatTimes);
        return "self";
      }
    } else {
      //has played
      //reset to zero
      call.getCallContext().put("repeatTimes", 0);
      return "other_end";
    }
  }

  return "next";
}
');

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `end`, `trigger_input`,
                            `receive_simple_dtmf`,
                            `next_node_code_adapters`, `voice_path`, semantics_transform, script)
VALUES ('quzhou_travel_20240729_guoye', 'quzhou_travel_20240729_guoye',
        '您此次出行是否在外过夜?', 0, 0, 0, null, '/quzhou_travel_20240729/qz_guoye.wav', '{
  "self": "quzhou_travel_20240729_guoye",
    "other_end":"quzhou_travel_20240729_sorryzaijian",
"next":"quzhou_travel_20240729_changtujiaotongfei"
}', '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var applicationContext = scriptContext.applicationContext;

  if (applicationContext.getBean("aiPatternServiceImpl").isSuit("lucheng_satisfaction_20240617_reside_info_other", input)) {
    var repeatTimes = call.getCallContext().get("repeatTimes");
    if (repeatTimes == null) {
      repeatTimes = 0;
    }

    //initialized status
    if (repeatTimes == 0) {
      {
        call.getCallContext().put("repeatTimes", ++repeatTimes);
        return "self";
      }
    } else {
      //has played
      //reset to zero
      call.getCallContext().put("repeatTimes", 0);
      return "other_end";
    }
  }

  return "next";
}
');

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `end`, `trigger_input`,
                            `receive_simple_dtmf`,
                            `next_node_code_adapters`, `voice_path`, semantics_transform, script)
VALUES ('quzhou_travel_20240729_changtujiaotongfei', 'quzhou_travel_20240729_changtujiaotongfei',
        '您此次出行，长途交通费大概花费多少呢?您可以告诉我个大概的金额，比如300以内、500-800、1000左右等等', 0, 0, 0, null,
        '/quzhou_travel_20240729/qz_changtujiaotongfei.wav', '{
  "self": "quzhou_travel_20240729_changtujiaotongfei",
    "other_end":"quzhou_travel_20240729_sorryzaijian",
"next":"quzhou_travel_20240729_shineijiaotongfei"
}', '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var applicationContext = scriptContext.applicationContext;

  if (applicationContext.getBean("aiPatternServiceImpl").isSuit("lucheng_satisfaction_20240617_reside_info_other", input)) {
    var repeatTimes = call.getCallContext().get("repeatTimes");
    if (repeatTimes == null) {
      repeatTimes = 0;
    }

    //initialized status
    if (repeatTimes == 0) {
      {
        call.getCallContext().put("repeatTimes", ++repeatTimes);
        return "self";
      }
    } else {
      //has played
      //reset to zero
      call.getCallContext().put("repeatTimes", 0);
      return "other_end";
    }
  }

  return "next";
}
');

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `end`, `trigger_input`,
                            `receive_simple_dtmf`,
                            `next_node_code_adapters`, `voice_path`, semantics_transform, script)
VALUES ('quzhou_travel_20240729_shineijiaotongfei', 'quzhou_travel_20240729_shineijiaotongfei',
        '您此次出行的市内交通费大约花费多少呢?包含公交地铁、打车等。', 0, 0, 0, null, '/quzhou_travel_20240729/qz_shineijiaotongfei.wav', '{
  "self": "quzhou_travel_20240729_shineijiaotongfei",
    "other_end":"quzhou_travel_20240729_sorryzaijian",
"next":"quzhou_travel_20240729_zhusufeiyong"
}', '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var applicationContext = scriptContext.applicationContext;

  if (applicationContext.getBean("aiPatternServiceImpl").isSuit("lucheng_satisfaction_20240617_reside_info_other", input)) {
    var repeatTimes = call.getCallContext().get("repeatTimes");
    if (repeatTimes == null) {
      repeatTimes = 0;
    }

    //initialized status
    if (repeatTimes == 0) {
      {
        call.getCallContext().put("repeatTimes", ++repeatTimes);
        return "self";
      }
    } else {
      //has played
      //reset to zero
      call.getCallContext().put("repeatTimes", 0);
      return "other_end";
    }
  }

  return "next";
}
');

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `end`, `trigger_input`,
                            `receive_simple_dtmf`,
                            `next_node_code_adapters`, `voice_path`, semantics_transform, script)
VALUES ('quzhou_travel_20240729_zhusufeiyong', 'quzhou_travel_20240729_zhusufeiyong',
        '您此次出行的住宿费用大约花费多少?', 0, 0, 0, null, '/quzhou_travel_20240729/qz_zhusufeiyong.wav', '{
  "self": "quzhou_travel_20240729_zhusufeiyong",
    "other_end":"quzhou_travel_20240729_sorryzaijian",
"next":"quzhou_travel_20240729_canyinfei"
}', '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var applicationContext = scriptContext.applicationContext;

  if (applicationContext.getBean("aiPatternServiceImpl").isSuit("lucheng_satisfaction_20240617_reside_info_other", input)) {
    var repeatTimes = call.getCallContext().get("repeatTimes");
    if (repeatTimes == null) {
      repeatTimes = 0;
    }

    //initialized status
    if (repeatTimes == 0) {
      {
        call.getCallContext().put("repeatTimes", ++repeatTimes);
        return "self";
      }
    } else {
      //has played
      //reset to zero
      call.getCallContext().put("repeatTimes", 0);
      return "other_end";
    }
  }

  return "next";
}
');

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `end`, `trigger_input`,
                            `receive_simple_dtmf`,
                            `next_node_code_adapters`, `voice_path`, semantics_transform, script)
VALUES ('quzhou_travel_20240729_canyinfei', 'quzhou_travel_20240729_canyinfei',
        '您此次出行的餐饮消费大约是多少?', 0, 0, 0, null, '/quzhou_travel_20240729/qz_canyinfei.wav', '{
  "self": "quzhou_travel_20240729_canyinfei",
    "other_end":"quzhou_travel_20240729_sorryzaijian",
"next":"quzhou_travel_20240729_yulefei"
}', '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var applicationContext = scriptContext.applicationContext;

  if (applicationContext.getBean("aiPatternServiceImpl").isSuit("lucheng_satisfaction_20240617_reside_info_other", input)) {
    var repeatTimes = call.getCallContext().get("repeatTimes");
    if (repeatTimes == null) {
      repeatTimes = 0;
    }

    //initialized status
    if (repeatTimes == 0) {
      {
        call.getCallContext().put("repeatTimes", ++repeatTimes);
        return "self";
      }
    } else {
      //has played
      //reset to zero
      call.getCallContext().put("repeatTimes", 0);
      return "other_end";
    }
  }

  return "next";
}
');

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `end`, `trigger_input`,
                            `receive_simple_dtmf`,
                            `next_node_code_adapters`, `voice_path`, semantics_transform, script)
VALUES ('quzhou_travel_20240729_yulefei', 'quzhou_travel_20240729_yulefei',
        '您此次出行的娱乐消费包括门票、导游费、观看表演、娱乐场所等大约花费是多少?', 0, 0, 0, null, '/quzhou_travel_20240729/qz_yulefei.wav', '{
  "self": "quzhou_travel_20240729_yulefei",
    "other_end":"quzhou_travel_20240729_sorryzaijian",
"next":"quzhou_travel_20240729_gouwufei"
}', '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var applicationContext = scriptContext.applicationContext;

  if (applicationContext.getBean("aiPatternServiceImpl").isSuit("lucheng_satisfaction_20240617_reside_info_other", input)) {
    var repeatTimes = call.getCallContext().get("repeatTimes");
    if (repeatTimes == null) {
      repeatTimes = 0;
    }

    //initialized status
    if (repeatTimes == 0) {
      {
        call.getCallContext().put("repeatTimes", ++repeatTimes);
        return "self";
      }
    } else {
      //has played
      //reset to zero
      call.getCallContext().put("repeatTimes", 0);
      return "other_end";
    }
  }

  return "next";
}
');

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `end`, `trigger_input`,
                            `receive_simple_dtmf`,
                            `next_node_code_adapters`, `voice_path`, semantics_transform, script)
VALUES ('quzhou_travel_20240729_gouwufei', 'quzhou_travel_20240729_gouwufei',
        '您此次出行的购物消费预计大约是多少?', 0, 0, 0, null, '/quzhou_travel_20240729/qz_gouwufei.wav', '{
  "self": "quzhou_travel_20240729_gouwufei",
    "other_end":"quzhou_travel_20240729_sorryzaijian",
"next":"quzhou_travel_20240729_qitafei"
}', '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var applicationContext = scriptContext.applicationContext;

  if (applicationContext.getBean("aiPatternServiceImpl").isSuit("lucheng_satisfaction_20240617_reside_info_other", input)) {
    var repeatTimes = call.getCallContext().get("repeatTimes");
    if (repeatTimes == null) {
      repeatTimes = 0;
    }

    //initialized status
    if (repeatTimes == 0) {
      {
        call.getCallContext().put("repeatTimes", ++repeatTimes);
        return "self";
      }
    } else {
      //has played
      //reset to zero
      call.getCallContext().put("repeatTimes", 0);
      return "other_end";
    }
  }

  return "next";
}
');

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `end`, `trigger_input`,
                            `receive_simple_dtmf`,
                            `next_node_code_adapters`, `voice_path`, semantics_transform, script)
VALUES ('quzhou_travel_20240729_qitafei', 'quzhou_travel_20240729_qitafei',
        '您此次出行还有其他消费吗?大约花费多少?', 0, 0, 0, null, '/quzhou_travel_20240729/qz_qitafei.wav', '{
  "self": "quzhou_travel_20240729_qitafei",
    "other_end":"quzhou_travel_20240729_sorryzaijian",
"next":"quzhou_travel_20240729_zaijian"
}', '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var applicationContext = scriptContext.applicationContext;

  if (applicationContext.getBean("aiPatternServiceImpl").isSuit("lucheng_satisfaction_20240617_reside_info_other", input)) {
    var repeatTimes = call.getCallContext().get("repeatTimes");
    if (repeatTimes == null) {
      repeatTimes = 0;
    }

    //initialized status
    if (repeatTimes == 0) {
      {
        call.getCallContext().put("repeatTimes", ++repeatTimes);
        return "self";
      }
    } else {
      //has played
      //reset to zero
      call.getCallContext().put("repeatTimes", 0);
      return "other_end";
    }
  }

//next node clean repeatTimes
    call.getCallContext().put("repeatTimes", 0);

  return "next";
}
');

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `end`,
                            `next_node_code_adapters`, `voice_path`, semantics_transform, script)
VALUES ('quzhou_travel_20240729_zaijian', 'quzhou_travel_20240729_zaijian',
        '好的，感谢您的配合，祝您生活愉快，再见!', 1, null, '/quzhou_travel_20240729/qz_zaijian.wav', null, null);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `end`,
                            `next_node_code_adapters`, `voice_path`, semantics_transform, script)
VALUES ('quzhou_travel_20240729_sorryzaijian', 'quzhou_travel_20240729_sorryzaijian',
        '好的，那这边先不打扰您了，祝您生活愉快，再见!', 1, null, '/quzhou_travel_20240729/qz_sorryzaijian.wav', null, null);


## update

insert into ai_pattern (code, patterns, include_codes) value ('quzhou_travel_20240729_kcb_yes', '[".*愿意.*",".*同意.*"]',
                                                              '["quzhou_travel_20240729_kcb_yes_SYS_GENERATED_","lucheng_satisfaction_20240617_reside_info_yes","lucheng_satisfaction_20240617_reside_info_yes_SYS_GENERATED_"]');

insert into ai_pattern (code, patterns, include_codes) value ('quzhou_travel_20240729_kcb_no', '[".*不愿意.*",".*不同意.*"]',
                                                              '["quzhou_travel_20240729_kcb_no_SYS_GENERATED_","lucheng_satisfaction_20240617_reside_info_no","lucheng_satisfaction_20240617_reside_info_no_SYS_GENERATED_"]');

update ai_flow_node
set next_node_code_adapters =
    '
    [
      {
        "aiPatternCodes": [
          "quzhou_travel_20240729_kcb_no"
        ],
        "semantics": "no"
      },
      {
        "aiPatternCodes": [
          "quzhou_travel_20240729_kcb_yes"
        ],
        "semantics": "yes"
      }
    ]
    '
where code = 'quzhou_travel_20240729_kcb';

update ai_flow_node
set script='
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var applicationContext = scriptContext.applicationContext;

  if (applicationContext.getBean("aiPatternServiceImpl").isSuit("lucheng_satisfaction_20240617_common_other", input)) {
    var repeatTimes = call.getCallContext().get("repeatTimes");
    if (repeatTimes == null) {
      repeatTimes = 0;
    }

    //initialized status
    if (repeatTimes == 0) {
      {
        call.getCallContext().put("repeatTimes", ++repeatTimes);
        return "self";
      }
    } else {
      //has played
      //reset to zero
      call.getCallContext().put("repeatTimes", 0);
      return "other_end";
    }
  }

//next node clean repeatTimes
    call.getCallContext().put("repeatTimes", 0);

  return "next";
}
'
where code in ('quzhou_travel_20240729_qitafei', 'quzhou_travel_20240729_gouwufei', 'quzhou_travel_20240729_yulefei',
               'quzhou_travel_20240729_canyinfei', 'quzhou_travel_20240729_zhusufeiyong',
               'quzhou_travel_20240729_shineijiaotongfei', 'quzhou_travel_20240729_changtujiaotongfei',
               'quzhou_travel_20240729_guoye', 'quzhou_travel_20240729_mudi', 'quzhou_travel_20240729_dizhi',
               'quzhou_travel_20240729_kcb');


##0730

insert into ai_pattern (code, patterns, include_codes) value ('quzhou_travel_20240729_kcb_yes', '[".*愿意.*",".*同意.*"]',
                                                              '["quzhou_travel_20240729_kcb_yes_SYS_GENERATED_","lucheng_satisfaction_20240617_reside_info_yes","lucheng_satisfaction_20240617_reside_info_yes_SYS_GENERATED_"]');

insert into ai_pattern (code, patterns, include_codes) value ('quzhou_travel_20240729_kcb_no', '[".*不愿意.*",".*不同意.*"]',
                                                              '["quzhou_travel_20240729_kcb_no_SYS_GENERATED_","lucheng_satisfaction_20240617_reside_info_no","lucheng_satisfaction_20240617_reside_info_no_SYS_GENERATED_"]');



update ai_pattern
set include_codes='quzhou_travel_20240729_kcb_yes_SYS_GENERATED_',
    patterns='[".*愿意.*",".*同意.*", ".*是.*", ".*对.*", ".*嗯.*", ".*有.*", ".*行.*", ".*好.*", ".*可以.*", ".*奥.*", ".*嗷啦.*", ".*哦尅.*", ".*是也.*", ".*没错.*", ".*是对.*", ".*是的.*", ".*嗯你说.*", ".*当然.*", ".*有的.*", ".*嗯嗯.*", ".*嗯呗.*", ".*嗯的.*", ".*是的啊.*", ".*能啊是住着.*", ".*对的住着.*", ".*就住着.*", ".*就住这.*", ".*是住这里.*", ".*住这里的.*", ".*我住这的.*", ".*好的.*", ".*确实是.*", ".*是也.*", ".*对.*", ".*对对.*", ".*对对对.*", ".*有的.*", ".*对呀.*", ".*恩没错.*", ".*嗯是.*", ".*啊对.*", ".*嗯嗯对.*", ".*对头.*", ".*正确.*", ".*对的对的.*", ".*对就是.*", ".*就是.*", ".*对是.*", ".*呃对.*"]'
where code = 'quzhou_travel_20240729_kcb_yes';

update ai_pattern
set include_codes='quzhou_travel_20240729_kcb_no_SYS_GENERATED_',
    patterns='[".*不愿意.*",".*不同意.*", ".*没.*", ".*不.*", ".*不是.*", ".*没有.*", ".*打错了.*", ".*不用.*", ".*不需要.*", ".*不必.*", ".*不必了.*", ".*用不到.*", ".*用不着.*", ".*暂时没.*", ".*木有.*", ".*现在还没.*", ".*近期没.*", ".*没这个规划.*", ".*暂时好像没有.*", ".*没有.*", ".*这个没有.*", ".*没有需要.*", ".*没有谢谢.*", ".*不好意思.*", ".*暂时不用.*", ".*不对.*", ".*不住着.*", ".*不住这.*", ".*我不住这.*", ".*我不住这里.*", ".*没有住啊.*", ".*不住这里.*", ".*没住.*", ".*没住啊.*", ".*不对.*", ".*不是.*", ".*不对的.*", ".*不对的啊.*", ".*没必要.*", ".*不搞这个.*", ".*不用不用.*", ".*不愿意.*", ".*不感兴趣.*", ".*我不听.*", ".*不听了.*", ".*不用安排.*", ".*不需要.*", ".*不用.*", ".*不考虑.*", ".*没考虑.*", ".*不需要.*", ".*没需求.*", ".*不想说.*", ".*不想了解.*", ".*没有需要.*", ".*不用介绍.*", ".*不想要.*", ".*挂了.*", ".*先这样.*", ".*不要.*", ".*不想.*", ".*没这个打算.*", ".*改天聊.*", ".*用不上.*", ".*别打.*", ".*不了.*", ".*不搞.*", ".*不做.*", ".*不弄.*", ".*暂时不用.*", ".*不要不要.*", ".*这个我不要.*", ".*不用谢谢.*", ".*谢谢不用.*", ".*哦不用.*", ".*不需要了.*", ".*没需要了.*", ".*不用了啊.*", ".*现在没有.*", ".*暂时没有.*", ".*没有没有.*", ".*没有没有没有.*", ".*没得没得.*", ".*否.*" ]'
where code = 'quzhou_travel_20240729_kcb_no';


##t
update ai_flow_node
set script='
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var applicationContext = scriptContext.applicationContext;

  if (applicationContext.getBean("aiPatternServiceImpl").isSuit("lucheng_satisfaction_20240617_common_other", input)) {
    var repeatTimes = call.getCallContext().get("repeatTimes");
    if (repeatTimes == null) {
      repeatTimes = 0;
    }

    //initialized status
    if (repeatTimes == 0) {
      {
        call.getCallContext().put("repeatTimes", ++repeatTimes);
        return "self";
      }
    } else {
      //has played
      //reset to zero
      call.getCallContext().put("repeatTimes", 0);
      return "other_end";
    }
  }

//next node clean repeatTimes
    call.getCallContext().put("repeatTimes", 0);

  return "next";
}
'
where code in ('quzhou_travel_20240729_qitafei', 'quzhou_travel_20240729_gouwufei', 'quzhou_travel_20240729_yulefei',
               'quzhou_travel_20240729_canyinfei', 'quzhou_travel_20240729_zhusufeiyong',
               'quzhou_travel_20240729_shineijiaotongfei', 'quzhou_travel_20240729_changtujiaotongfei',
               'quzhou_travel_20240729_guoye', 'quzhou_travel_20240729_mudi', 'quzhou_travel_20240729_dizhi',
               'quzhou_travel_20240729_kcb');


update ai_pattern
set include_codes='quzhou_travel_20240729_kcb_yes_SYS_GENERATED_',
    patterns='[".*愿意.*",".*同意.*", ".*是.*", ".*对.*", ".*嗯.*", ".*有.*", ".*行.*", ".*好.*", ".*可以.*", ".*奥.*", ".*嗷啦.*", ".*哦尅.*", ".*是也.*", ".*没错.*", ".*是对.*", ".*是的.*", ".*嗯你说.*", ".*当然.*", ".*有的.*", ".*嗯嗯.*", ".*嗯呗.*", ".*嗯的.*", ".*是的啊.*", ".*能啊是住着.*", ".*对的住着.*", ".*就住着.*", ".*就住这.*", ".*是住这里.*", ".*住这里的.*", ".*我住这的.*", ".*好的.*", ".*确实是.*", ".*是也.*", ".*对.*", ".*对对.*", ".*对对对.*", ".*有的.*", ".*对呀.*", ".*恩没错.*", ".*嗯是.*", ".*啊对.*", ".*嗯嗯对.*", ".*对头.*", ".*正确.*", ".*对的对的.*", ".*对就是.*", ".*就是.*", ".*对是.*", ".*呃对.*"]'
where code = 'quzhou_travel_20240729_kcb_yes';

update ai_pattern
set include_codes='quzhou_travel_20240729_kcb_no_SYS_GENERATED_',
    patterns='[".*不愿意.*",".*不同意.*", ".*没.*", ".*不.*", ".*不是.*", ".*没有.*", ".*打错了.*", ".*不用.*", ".*不需要.*", ".*不必.*", ".*不必了.*", ".*用不到.*", ".*用不着.*", ".*暂时没.*", ".*木有.*", ".*现在还没.*", ".*近期没.*", ".*没这个规划.*", ".*暂时好像没有.*", ".*没有.*", ".*这个没有.*", ".*没有需要.*", ".*没有谢谢.*", ".*不好意思.*", ".*暂时不用.*", ".*不对.*", ".*不住着.*", ".*不住这.*", ".*我不住这.*", ".*我不住这里.*", ".*没有住啊.*", ".*不住这里.*", ".*没住.*", ".*没住啊.*", ".*不对.*", ".*不是.*", ".*不对的.*", ".*不对的啊.*", ".*没必要.*", ".*不搞这个.*", ".*不用不用.*", ".*不愿意.*", ".*不感兴趣.*", ".*我不听.*", ".*不听了.*", ".*不用安排.*", ".*不需要.*", ".*不用.*", ".*不考虑.*", ".*没考虑.*", ".*不需要.*", ".*没需求.*", ".*不想说.*", ".*不想了解.*", ".*没有需要.*", ".*不用介绍.*", ".*不想要.*", ".*挂了.*", ".*先这样.*", ".*不要.*", ".*不想.*", ".*没这个打算.*", ".*改天聊.*", ".*用不上.*", ".*别打.*", ".*不了.*", ".*不搞.*", ".*不做.*", ".*不弄.*", ".*暂时不用.*", ".*不要不要.*", ".*这个我不要.*", ".*不用谢谢.*", ".*谢谢不用.*", ".*哦不用.*", ".*不需要了.*", ".*没需要了.*", ".*不用了啊.*", ".*现在没有.*", ".*暂时没有.*", ".*没有没有.*", ".*没有没有没有.*", ".*没得没得.*", ".*否.*" ]'
where code = 'quzhou_travel_20240729_kcb_no';

##0802

update ai_flow
set wait_voice='/data/program/counselor/ai/record/counselor-ai/quzhou_travel_20240729/nh_qwnhyztm.wav',
    context='{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": false,
    "interval": 2,
    "times": 1
  }
}'
where code = 'quzhou_travel_20240729';

update ai_flow_node
set remote_voice='
{
  "url": "https://linkup.wacai.info/config-server/script-compose/wav/66ac8ee69b354e4a973e57d9%E6%82%A8%E5%A5%BD!%E6%88%91%E4%BB%AC%E6%AD%A3%E5%9C%A8%E5%BC%80%E5%B1%95%E8%A1%A2%E5%B7%9E%E5%B8%82%E6%96%87%E6%97%85%E5%87%BA%E8%A1%8C%E8%B0%83%E7%A0%94%E6%B4%BB%E5%8A%A8.wav",
  "method": "GET"
}
'
where code = 'quzhou_travel_20240729_kcb';


update ai_flow_node
set remote_voice='
{
  "url": "https://linkup.wacai.info/config-server/script-compose/wav/66ac8e17399be675fe291945%E5%97%AF%EF%BC%8C%E5%A5%BD%E7%9A%84%EF%BC%8C%E9%82%A3%E6%82%A8%E6%AD%A4%E6%AC%A1%E5%87%BA%E8%A1%8C%E7%9A%84%E9%A4%90%E9%A5%AE%E6%B6%88%E8%B4%B9%E5%A4%A7%E7%BA%A6%E6%98%AF%E5%A4%9A%E5%B0%91.wav",
  "method": "GET"
}
'
where code = 'quzhou_travel_20240729_canyinfei';
update ai_flow_node
set remote_voice='
{
  "url": "https://linkup.wacai.info/config-server/script-compose/wav/66ac8e0c9b354e4a973e57d5%E5%97%AF%EF%BC%8C%E5%A5%BD%E7%9A%84%EF%BC%8C%E9%82%A3%E6%82%A8%E6%AD%A4%E6%AC%A1%E5%87%BA%E8%A1%8C%EF%BC%8C%E9%95%BF%E9%80%94%E4%BA%A4%E9%80%9A%E8%B4%B9%E5%A4%A7%E6%A6%82%E8%8A%B1%E8%B4%B9%E5%A4%9A%E5%B0%91%E5%91%A2.wav",
  "method": "GET"
}
'
where code = 'quzhou_travel_20240729_changtujiaotongfei';
update ai_flow_node
set remote_voice='
{
  "url": "https://linkup.wacai.info/config-server/script-compose/wav/66ac8de99b354e4a973e57d4%E5%A5%BD%E7%9A%84%EF%BC%8C%E6%8E%A5%E4%B8%8B%E6%9D%A5%E7%9A%84%E9%97%AE%E9%A2%98%E8%AF%B7%E6%82%A8%E6%A0%B9%E6%8D%AE%E5%AE%9E%E9%99%85%E6%83%85%E5%86%B5%E5%9B%9E%E7%AD%94%E5%8D%B3%E5%8F%AF.wav",
  "method": "GET"
}
'
where code = 'quzhou_travel_20240729_dizhi';
update ai_flow_node
set remote_voice='
{
  "url": "https://linkup.wacai.info/config-server/script-compose/wav/66ac8e23399be675fe291946%E5%97%AF%EF%BC%8C%E5%A5%BD%E7%9A%84%EF%BC%8C%E9%82%A3%E6%82%A8%E6%AD%A4%E6%AC%A1%E5%87%BA%E8%A1%8C%E7%9A%84%E8%B4%AD%E7%89%A9%E6%B6%88%E8%B4%B9%E9%A2%84%E8%AE%A1%E5%A4%A7%E7%BA%A6%E6%98%AF%E5%A4%9A%E5%B0%91.wav",
  "method": "GET"
}
'
where code = 'quzhou_travel_20240729_gouwufei';
update ai_flow_node
set remote_voice='
{
  "url": "https://linkup.wacai.info/config-server/script-compose/wav/66ac8ea69b354e4a973e57d7%E5%97%AF%EF%BC%8C%E5%A5%BD%E7%9A%84%EF%BC%8C%E9%82%A3%E6%82%A8%E6%AD%A4%E6%AC%A1%E5%87%BA%E8%A1%8C%E6%98%AF%E5%90%A6%E5%9C%A8%E5%A4%96%E8%BF%87%E5%A4%9C.wav",
  "method": "GET"
}
'
where code = 'quzhou_travel_20240729_guoye';
update ai_flow_node
set remote_voice='
{
  "url": "https://linkup.wacai.info/config-server/script-compose/wav/66ac8eaf399be675fe29194a%E5%97%AF%EF%BC%8C%E5%A5%BD%E7%9A%84%EF%BC%8C%E9%82%A3%E6%82%A8%E6%AD%A4%E6%AC%A1%E5%87%BA%E8%A1%8C%E6%9C%80%E4%B8%BB%E8%A6%81%E7%9A%84%E7%9B%AE%E7%9A%84%E6%98%AF%E4%BB%80%E4%B9%88%E5%91%A2.wav",
  "method": "GET"
}
'
where code = 'quzhou_travel_20240729_mudi';
update ai_flow_node
set remote_voice='
{
  "url": "https://linkup.wacai.info/config-server/script-compose/wav/66ac8e9c399be675fe291949%E5%97%AF%EF%BC%8C%E5%A5%BD%E7%9A%84%EF%BC%8C%E9%82%A3%E6%82%A8%E6%AD%A4%E6%AC%A1%E5%87%BA%E8%A1%8C%E8%BF%98%E6%9C%89%E5%85%B6%E4%BB%96%E6%B6%88%E8%B4%B9%E5%90%97%EF%BC%9F%E5%A4%A7%E7%BA%A6%E8%8A%B1%E8%B4%B9%E5%A4%9A%E5%B0%91.wav",
  "method": "GET"
}
'
where code = 'quzhou_travel_20240729_qitafei';
update ai_flow_node
set remote_voice='
{
  "url": "https://linkup.wacai.info/config-server/script-compose/wav/66ac8e2f399be675fe291947%E5%97%AF%EF%BC%8C%E5%A5%BD%E7%9A%84%EF%BC%8C%E9%82%A3%E6%82%A8%E6%AD%A4%E6%AC%A1%E5%87%BA%E8%A1%8C%E7%9A%84%E5%B8%82%E5%86%85%E4%BA%A4%E9%80%9A%E8%B4%B9%E5%A4%A7%E7%BA%A6%E8%8A%B1%E8%B4%B9%E5%A4%9A%E5%B0%91%E5%91%A2%EF%BC%9F%E5%8C%85%E5%90%AB%E5%85%AC%E4%BA%A4%E5%9C%B0%E9%93%81%E3%80%81%E6%89%93%E8%BD%A6%E7%AD%89.wav",
  "method": "GET"
}
'
where code = 'quzhou_travel_20240729_shineijiaotongfei';
update ai_flow_node
set remote_voice='
{
  "url": "https://linkup.wacai.info/config-server/script-compose/wav/66ac8df6399be675fe291944%E5%A5%BD%E7%9A%84%EF%BC%8C%E9%82%A3%E8%BF%99%E8%BE%B9%E5%85%88%E4%B8%8D%E6%89%93%E6%89%B0%E6%82%A8%E4%BA%86%EF%BC%8C%E7%A5%9D%E6%82%A8%E7%94%9F%E6%B4%BB%E6%84%89%E5%BF%AB%EF%BC%8C%E5%86%8D%E8%A7%81.wav",
  "method": "GET"
}
'
where code = 'quzhou_travel_20240729_sorryzaijian';
update ai_flow_node
set remote_voice='
{
  "url": "https://linkup.wacai.info/config-server/script-compose/wav/66ac8e3a9b354e4a973e57d6%E5%97%AF%EF%BC%8C%E5%A5%BD%E7%9A%84%EF%BC%8C%E9%82%A3%E6%82%A8%E6%AD%A4%E6%AC%A1%E5%87%BA%E8%A1%8C%E7%9A%84%E5%A8%B1%E4%B9%90%E6%B6%88%E8%B4%B9%E5%8C%85%E6%8B%AC%E9%97%A8%E7%A5%A8.wav",
  "method": "GET"
}
'
where code = 'quzhou_travel_20240729_yulefei';
update ai_flow_node
set remote_voice='
{
  "url": "https://linkup.wacai.info/config-server/script-compose/wav/66ac46959b354e4a973e57a3%E5%A5%BD%E7%9A%84%EF%BC%8C%E6%84%9F%E8%B0%A2%E6%82%A8%E7%9A%84%E9%85%8D%E5%90%88%EF%BC%8C%E7%A5%9D%E6%82%A8%E7%94%9F%E6%B4%BB%E6%84%89%E5%BF%AB%EF%BC%8C%E5%86%8D%E8%A7%81.wav",
  "method": "GET"
}
'
where code = 'quzhou_travel_20240729_zaijian';
update ai_flow_node
set remote_voice='
{
  "url": "https://linkup.wacai.info/config-server/script-compose/wav/66ac8e93399be675fe291948%E5%97%AF%EF%BC%8C%E5%A5%BD%E7%9A%84%EF%BC%8C%E9%82%A3%E6%82%A8%E6%AD%A4%E6%AC%A1%E5%87%BA%E8%A1%8C%E7%9A%84%E4%BD%8F%E5%AE%BF%E8%B4%B9%E7%94%A8%E5%A4%A7%E7%BA%A6%E8%8A%B1%E8%B4%B9%E5%A4%9A%E5%B0%91.wav",
  "method": "GET"
}
'
where code = 'quzhou_travel_20240729_zhusufeiyong';

##0805 平定-召集令第一阶段
INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('pd_zjl_1', 'pd_zjl_1', 'pd_zjl_1', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('pd_zjl_1', 'pd_zjl_1',
        'pd_zjl_1', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {

var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=ZA3ZWRGDBJ21&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "pd_zjl_1";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/500",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);

INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('pd_zjl_2', 'pd_zjl_2', 'pd_zjl_2', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('pd_zjl_2', 'pd_zjl_2',
        'pd_zjl_2', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {

var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=ZA3ZWRGDBJ21&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "pd_zjl_2";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/501",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);

INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('pd_zjl_3', 'pd_zjl_3', 'pd_zjl_3', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('pd_zjl_3', 'pd_zjl_3',
        'pd_zjl_3', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {

var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=ZA3ZWRGDBJ21&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "pd_zjl_3";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/502",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);

INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('pd_zjl_tjjm', 'pd_zjl_tjjm', 'pd_zjl_tjjm', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('pd_zjl_tjjm', 'pd_zjl_tjjm',
        'pd_zjl_tjjm', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {

var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=ZA3ZWRGDBJ21&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "pd_zjl_tjjm";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/503",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);



INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('qj_fql_1', 'qj_fql_1', 'qj_fql_1', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('qj_fql_1', 'qj_fql_1',
        'qj_fql_1', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {

var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=NTDDQEFOOZ14&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "qj_fql_1";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/504",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);


INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('qj_fql_2', 'qj_fql_2', 'qj_fql_2', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('qj_fql_2', 'qj_fql_2',
        'qj_fql_2', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {

var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=NTDDQEFOOZ14&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "qj_fql_2";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/505",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);


INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('qj_fql_3', 'qj_fql_3', 'qj_fql_3', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('qj_fql_3', 'qj_fql_3',
        'qj_fql_3', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {

var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=NTDDQEFOOZ14&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "qj_fql_3";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/506",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);


INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('qj_fql_tjjm', 'qj_fql_tjjm', 'qj_fql_tjjm', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('qj_fql_tjjm', 'qj_fql_tjjm',
        'qj_fql_tjjm', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {

var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=NTDDQEFOOZ14&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "qj_fql_tjjm";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/507",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);


# 0903
INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('qj_default_1', 'qj_default_1', 'qj_default_1', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('qj_default_1', 'qj_default_1',
        'qj_default_1', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {

var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=NTDDQEFOOZ14&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "qj_default_1";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/515",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);


INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('qj_default_2', 'qj_default_2', 'qj_default_2', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('qj_default_2', 'qj_default_2',
        'qj_default_2', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {

var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=NTDDQEFOOZ14&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "qj_default_2";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/516",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);


INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('qj_default_3', 'qj_default_3', 'qj_default_3', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('qj_default_3', 'qj_default_3',
        'qj_default_3', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {

var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=NTDDQEFOOZ14&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "qj_default_3";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/517",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);


INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('qj_default_4', 'qj_default_4', 'qj_default_4', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('qj_default_4', 'qj_default_4',
        'qj_default_4', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {

var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=NTDDQEFOOZ14&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "qj_default_4";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/518",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);

###0912

INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('pd_default_1', 'pd_default_1', 'pd_default_1', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('pd_default_1', 'pd_default_1',
        'pd_default_1', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {

var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=ZA3ZWRGDBJ21&callerNumber=" + mobile

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "pd_default_1";
    }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/520",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);

## 0929
select *
from ai_flow;

INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('jc_ivr_1', 'jc_ivr_1', 'jc_ivr_1', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('jc_ivr_1', 'jc_ivr_1',
        'jc_ivr_1', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {

  var hasSendSms = call.getCallContext().get("hasSendSms");
  if (hasSendSms == null) {
    hasSendSms = false;
  }

  if (!hasSendSms) {
    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url = "http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=NTDDQEFOOZ14&callerNumber=" + mobile
    var userData = AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if (kid != null) {
      url += "&kid=" + kid;
    }

    WebClientUtils.get(url);
    call.getCallContext().put("hasSendSms", true);
  }

  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else if (input == "2") {
    return "fssd_mediation_notice_ivr_2";
  } else {
    return "jc_ivr_1";
  }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/521",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);


INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('jc_ivr_2', 'jc_ivr_2', 'jc_ivr_2', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('jc_ivr_2', 'jc_ivr_2',
        'jc_ivr_2', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {

  var hasSendSms = call.getCallContext().get("hasSendSms");
  if (hasSendSms == null) {
    hasSendSms = false;
  }

  if (!hasSendSms) {
    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url = "http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=NTDDQEFOOZ14&callerNumber=" + mobile
    var userData = AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if (kid != null) {
      url += "&kid=" + kid;
    }

    WebClientUtils.get(url);
    call.getCallContext().put("hasSendSms", true);
  }

  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else if (input == "2") {
    return "fssd_mediation_notice_ivr_2";
  } else {
    return "jc_ivr_2";
  }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/522",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);



INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('jc_ivr_3', 'jc_ivr_3', 'jc_ivr_3', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('jc_ivr_3', 'jc_ivr_3',
        'jc_ivr_3', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {

  var hasSendSms = call.getCallContext().get("hasSendSms");
  if (hasSendSms == null) {
    hasSendSms = false;
  }

  if (!hasSendSms) {
    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url = "http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=NTDDQEFOOZ14&callerNumber=" + mobile
    var userData = AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if (kid != null) {
      url += "&kid=" + kid;
    }

    WebClientUtils.get(url);
    call.getCallContext().put("hasSendSms", true);
  }

  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else if (input == "2") {
    return "fssd_mediation_notice_ivr_2";
  } else {
    return "jc_ivr_3";
  }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/523",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);


##1011
INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('jc_ppd_ivr_1', 'jc_ppd_ivr_1', 'jc_ppd_ivr_1', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('jc_ppd_ivr_1', 'jc_ppd_ivr_1',
        'jc_ppd_ivr_1', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {

  var hasSendSms = call.getCallContext().get("hasSendSms");
  if (hasSendSms == null) {
    hasSendSms = false;
  }

  if (!hasSendSms) {
    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url = "http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=NTDDQEFOOZ14&callerNumber=" + mobile
    var userData = AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if (kid != null) {
      url += "&kid=" + kid;
    }

    WebClientUtils.get(url);
    call.getCallContext().put("hasSendSms", true);
  }

  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else if (input == "2") {
    return "fssd_mediation_notice_ivr_2";
  } else {
    return "jc_ppd_ivr_1";
  }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/527",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);


INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('jc_ppd_ivr_2', 'jc_ppd_ivr_2', 'jc_ppd_ivr_2', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('jc_ppd_ivr_2', 'jc_ppd_ivr_2',
        'jc_ppd_ivr_2', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {

  var hasSendSms = call.getCallContext().get("hasSendSms");
  if (hasSendSms == null) {
    hasSendSms = false;
  }

  if (!hasSendSms) {
    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url = "http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=NTDDQEFOOZ14&callerNumber=" + mobile
    var userData = AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if (kid != null) {
      url += "&kid=" + kid;
    }

    WebClientUtils.get(url);
    call.getCallContext().put("hasSendSms", true);
  }

  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else if (input == "2") {
    return "fssd_mediation_notice_ivr_2";
  } else {
    return "jc_ppd_ivr_2";
  }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/528",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);


##1016

INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('jyjczb_ivr_1', 'jyjczb_ivr_1', 'jyjczb_ivr_1', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('jyjczb_ivr_1', 'jyjczb_ivr_1',
        'jyjczb_ivr_1', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {

  var hasSendSms = call.getCallContext().get("hasSendSms");
  if (hasSendSms == null) {
    hasSendSms = false;
  }

  if (!hasSendSms) {
    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url = "http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=LQXTC5KX7E28&callerNumber=" + mobile
    var userData = AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if (kid != null) {
      url += "&kid=" + kid;
    }

    WebClientUtils.get(url);
    call.getCallContext().put("hasSendSms", true);
  }

  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else if (input == "2") {
    return "fssd_mediation_notice_ivr_2";
  } else {
    return "jyjczb_ivr_1";
  }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/521",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);


INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('jyjczb_ivr_2', 'jyjczb_ivr_2', 'jyjczb_ivr_2', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('jyjczb_ivr_2', 'jyjczb_ivr_2',
        'jyjczb_ivr_2', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {

  var hasSendSms = call.getCallContext().get("hasSendSms");
  if (hasSendSms == null) {
    hasSendSms = false;
  }

  if (!hasSendSms) {
    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url = "http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=LQXTC5KX7E28&callerNumber=" + mobile
    var userData = AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if (kid != null) {
      url += "&kid=" + kid;
    }

    WebClientUtils.get(url);
    call.getCallContext().put("hasSendSms", true);
  }

  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else if (input == "2") {
    return "fssd_mediation_notice_ivr_2";
  } else {
    return "jyjczb_ivr_2";
  }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/522",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);


INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('jyjczb_ivr_3', 'jyjczb_ivr_3', 'jyjczb_ivr_3', NULL, now(), now(), '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}', NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('jyjczb_ivr_3', 'jyjczb_ivr_3',
        'jyjczb_ivr_3', NULL, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {

  var hasSendSms = call.getCallContext().get("hasSendSms");
  if (hasSendSms == null) {
    hasSendSms = false;
  }

  if (!hasSendSms) {
    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url = "http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=LQXTC5KX7E28&callerNumber=" + mobile
    var userData = AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if (kid != null) {
      url += "&kid=" + kid;
    }

    WebClientUtils.get(url);
    call.getCallContext().put("hasSendSms", true);
  }

  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else if (input == "2") {
    return "fssd_mediation_notice_ivr_2";
  } else {
    return "jyjczb_ivr_3";
  }

}', 0, 0, 1, NULL, NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/523",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}', NULL);

update ai_flow_node
set voice_path='biz/pharos/amc_auto'
where code = 'amc_auto';

#1025

INSERT INTO ai_flow (name, code, first_flow_node_code, comment, created_time, updated_time, call_event_call_back_url,
                     not_receive_voice_input, record_path, wait_voice, context, voice_end_call_back_url)
VALUES ('inlet_guidance_nov', 'inlet_guidance_nov', 'inlet_guidance_nov', null, null, null,
        'http://gofer-web-api.linkup.k2.wacai.info/call/status', 1,
        '/data/program/counselor/ai/record/counselor-ai/biz/pharos/cus/',
        '/data/program/counselor/ai/record/counselor-ai/biz/pharos/voice/amc_test_wait.wav', '{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": false,
    "interval": 50,
    "times": 0
  }
}', 'http://gofer-web-api.linkup.k2.wacai.info/call/tts/end');


INSERT INTO ai_flow (name, code, first_flow_node_code, comment, created_time, updated_time, call_event_call_back_url,
                     not_receive_voice_input, record_path, wait_voice, context, voice_end_call_back_url)
VALUES ('register_guidance_nov', 'register_guidance_nov', 'register_guidance_nov', null, null, null,
        'http://gofer-web-api.linkup.k2.wacai.info/call/status', 1,
        '/data/program/counselor/ai/record/counselor-ai/biz/pharos/cus/',
        '/data/program/counselor/ai/record/counselor-ai/biz/pharos/voice/amc_test_wait.wav', '{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": false,
    "interval": 50,
    "times": 0
  }
}', 'http://gofer-web-api.linkup.k2.wacai.info/call/tts/end');

INSERT INTO ai_flow (name, code, first_flow_node_code, comment, created_time, updated_time, call_event_call_back_url,
                     not_receive_voice_input, record_path, wait_voice, context, voice_end_call_back_url)
VALUES ('sms_guidance_nov', 'sms_guidance_nov', 'sms_guidance_nov', null, null, null,
        'http://gofer-web-api.linkup.k2.wacai.info/call/status', 1,
        '/data/program/counselor/ai/record/counselor-ai/biz/pharos/cus/',
        '/data/program/counselor/ai/record/counselor-ai/biz/pharos/voice/amc_test_wait.wav', '{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": false,
    "interval": 50,
    "times": 0
  }
}', 'http://gofer-web-api.linkup.k2.wacai.info/call/tts/end');



INSERT INTO ai_flow_node (name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path, semantics_map, remote_voice, context, semantics_transform)
VALUES ('inlet_guidance_nov', 'inlet_guidance_nov', 'inlet_guidance_nov', null, null, 0, 0, 0, null, null, null, null,
        null, null, null, 'biz/pharos/inlet_guidance_nov', null, null, null, null);
INSERT INTO ai_flow_node (name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path, semantics_map, remote_voice, context, semantics_transform)
VALUES ('register_guidance_nov', 'register_guidance_nov', 'register_guidance_nov', null, null, 0, 0, 0, null, null,
        null, null, null, null, null, 'biz/pharos/register_guidance_nov', null, null, null, null);
INSERT INTO ai_flow_node (name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path, semantics_map, remote_voice, context, semantics_transform)
VALUES ('sms_guidance_nov', 'sms_guidance_nov', 'sms_guidance_nov', null, null, 0, 0, 0, null, null, null, null, null,
        null, null, 'biz/pharos/sms_guidance_nov', null, null, null, null);
