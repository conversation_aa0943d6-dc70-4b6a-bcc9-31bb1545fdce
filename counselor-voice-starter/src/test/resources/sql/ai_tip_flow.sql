CREATE TABLE `ai_flow_node` (
    `id`                  int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `name`                varchar(128)         DEFAULT NULL COMMENT '名称',
    `code`                varchar(64) NOT NULL COMMENT '编码',
    `tip`                 varchar(1024)        DEFAULT NULL COMMENT 'ai提示',

    `sms_ruler_id`        varchar(128)         DEFAULT NULL COMMENT '短信模板 id',

    `script`              text               DEFAULT NULL COMMENT '用于动态返回节点的脚本',

    `end`                 tinyint   NOT NULL DEFAULT 0 COMMENT '是否是终止节点',
    `transfer`            tinyint   NOT NULL DEFAULT 0 COMMENT '是否是转接节点',
    `trigger_input`       tinyint   NOT NULL DEFAULT 0 COMMENT '是否触发用户输入',
    `receive_simple_dtmf` tinyint   NOT NULL DEFAULT 0 COMMENT '是否触发用户输入',

    `ext_info`            text               DEFAULT NULL COMMENT '额外拓展属性，json 格式',

    `comment`             varchar(255)       DEFAULT NULL COMMENT '备注',
    `created_time`        timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`        timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code`(`code`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8 COMMENT ='ai流程节点';


alter table ai_flow_node
    add simple_next_flow_code   varchar(64) null comment '简单的下一个节点',
    add next_node_code_adapters text        null comment '下一个节点适配器，json格式';



alter table ai_flow_node
    add voice_path varchar(255) null comment '语音文件地址';




