INSERT INTO ai_pattern (name, code, patterns, include_codes, comment, created_time, updated_time, need_check)
VALUES (null, 'lucheng_satisfaction_20240617_reside_info_yes', null, null, null, '2024-07-17 16:18:19',
        '2024-07-17 16:18:56', 0);
INSERT INTO ai_pattern (name, code, patterns, include_codes, comment, created_time, updated_time, need_check)
VALUES (null, 'lucheng_satisfaction_20240617_question_again_no', null, null, null, '2024-07-18 16:40:16',
        '2024-07-18 16:40:16', 0);
INSERT INTO ai_pattern (name, code, patterns, include_codes, comment, created_time, updated_time, need_check)
VALUES (null, 'lucheng_satisfaction_20240617_question_again_other', null, null, null, '2024-07-18 16:40:16',
        '2024-07-18 16:40:16', 0);
INSERT INTO ai_pattern (name, code, patterns, include_codes, comment, created_time, updated_time, need_check)
VALUES (null, 'lucheng_satisfaction_20240617_question_again_yes', null, null, null, '2024-07-18 16:40:16',
        '2024-07-18 16:40:16', 0);
INSERT INTO ai_pattern (name, code, patterns, include_codes, comment, created_time, updated_time, need_check)
VALUES (null, 'lucheng_satisfaction_20240617_record_phone_no', null, null, null, '2024-07-18 16:40:16',
        '2024-07-18 16:40:16', 0);
INSERT INTO ai_pattern (name, code, patterns, include_codes, comment, created_time, updated_time, need_check)
VALUES (null, 'lucheng_satisfaction_20240617_record_phone_no_worry_no', null, null, null, '2024-07-18 16:40:16',
        '2024-07-18 16:40:16', 0);
INSERT INTO ai_pattern (name, code, patterns, include_codes, comment, created_time, updated_time, need_check)
VALUES (null, 'lucheng_satisfaction_20240617_record_phone_no_worry_other', null, null, null, '2024-07-18 16:40:16',
        '2024-07-18 16:40:16', 0);
INSERT INTO ai_pattern (name, code, patterns, include_codes, comment, created_time, updated_time, need_check)
VALUES (null, 'lucheng_satisfaction_20240617_record_phone_no_worry_yes', null, null, null, '2024-07-18 16:40:16',
        '2024-07-18 16:40:16', 0);
INSERT INTO ai_pattern (name, code, patterns, include_codes, comment, created_time, updated_time, need_check)
VALUES (null, 'lucheng_satisfaction_20240617_record_phone_other', null, null, null, '2024-07-18 16:40:16',
        '2024-07-18 16:40:16', 0);
INSERT INTO ai_pattern (name, code, patterns, include_codes, comment, created_time, updated_time, need_check)
VALUES (null, 'lucheng_satisfaction_20240617_record_phone_yes', null, null, null, '2024-07-18 16:40:16',
        '2024-07-18 16:40:16', 0);
INSERT INTO ai_pattern (name, code, patterns, include_codes, comment, created_time, updated_time, need_check)
VALUES (null, 'lucheng_satisfaction_20240617_reside_info_no', null, null, null, '2024-07-18 16:40:16',
        '2024-07-18 16:40:16', 0);
INSERT INTO ai_pattern (name, code, patterns, include_codes, comment, created_time, updated_time, need_check)
VALUES (null, 'lucheng_satisfaction_20240617_reside_info_other', null, null, null, '2024-07-18 16:40:16',
        '2024-07-18 16:40:16', 0);


update ai_flow_node
set next_node_code_adapters='
[
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_reside_info_no_SYS_GENERATED_","lucheng_satisfaction_20240617_reside_info_no"
    ],
    "semantics": "no"
  },
  {
    "aiPatternCodes": [
       "lucheng_satisfaction_20240617_reside_info_yes_SYS_GENERATED_","lucheng_satisfaction_20240617_reside_info_yes"
    ],
    "semantics": "yes"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_reside_info_other_SYS_GENERATED_","lucheng_satisfaction_20240617_reside_info_other"
    ],
    "semantics": "other"
  }
]
',
    semantics_transform='{
  "yes": "lucheng_satisfaction_20240617_record_phone",
  "no": "lucheng_satisfaction_20240617_no_disturb",
  "other": "lucheng_satisfaction_20240617_question_again"
}'
where code = 'lucheng_satisfaction_20240617_reside_info';


update ai_flow_node
set next_node_code_adapters='

[
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_record_phone_no_worry_no_SYS_GENERATED_","lucheng_satisfaction_20240617_record_phone_no_worry_no"
    ],
    "semantics": "no"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_record_phone_no_worry_yes_SYS_GENERATED_","lucheng_satisfaction_20240617_record_phone_no_worry_yes"
    ],
    "semantics": "yes"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_record_phone_no_worry_other_SYS_GENERATED_","lucheng_satisfaction_20240617_record_phone_no_worry_other"
    ],
    "semantics": "other"
  }
]

',
    semantics_transform='{
  "yes": "lucheng_satisfaction_20240617_tks",
  "no": "lucheng_satisfaction_20240617_other_end",
  "other": "lucheng_satisfaction_20240617_other_end"
}'
where code = 'lucheng_satisfaction_20240617_record_phone_no_worry';


#

update ai_flow_node
set next_node_code_adapters='
[
  {
    "aiPatternCodes": [
     "lucheng_satisfaction_20240617_record_phone_no_SYS_GENERATED_","lucheng_satisfaction_20240617_record_phone_no"
    ],
    "semantics": "no"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_record_phone_yes_SYS_GENERATED_","lucheng_satisfaction_20240617_record_phone_yes"
    ],
    "semantics": "yes"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_record_phone_other_SYS_GENERATED_","lucheng_satisfaction_20240617_record_phone_other"
    ],
    "semantics": "other"
  }
]

',
    semantics_transform='{
  "yes": "lucheng_satisfaction_20240617_tks",
  "no": "lucheng_satisfaction_20240617_record_phone_no_worry",
  "other": "lucheng_satisfaction_20240617_record_phone_no_worry"
}'
where code = 'lucheng_satisfaction_20240617_record_phone';


update ai_flow_node
set next_node_code_adapters='
[
   {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_question_again_no_SYS_GENERATED_","lucheng_satisfaction_20240617_question_again_no"
    ],
    "semantics": "no"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_question_again_yes_SYS_GENERATED_","lucheng_satisfaction_20240617_question_again_yes"
    ],
    "semantics": "yes"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_question_again_other_SYS_GENERATED_","lucheng_satisfaction_20240617_question_again_other"
    ],
    "semantics": "other"
  }
]

',
    semantics_transform='{
  "yes": "lucheng_satisfaction_20240617_record_phone",
  "no": "lucheng_satisfaction_20240617_no_disturb",
  "other": "lucheng_satisfaction_20240617_other_end"
}'
where code = 'lucheng_satisfaction_20240617_question_again';


## addition data

insert into ai_pattern (code, patterns) value ('lucheng_satisfaction_20240617_common_other', '[
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
      ".*没听清楚.*",
    ".*没听懂.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ]');


update ai_pattern
set patterns='[
      ".*现在打的.*",
      ".*打的.*",
      ".*我的电话.*",
      ".*电话.*",
      ".*就打.*",
      ".*这个.*",
      ".*你看.*",
      ".*你大的.*",
      ".*你打.*",
      ".*你打的.*",
      ".*这个.*",
      ".*就这个.*",
      ".*号码.*",
      ".*打过来.*",
      ".*[0-9]{3,}.*"
    ]'
where code = 'lucheng_satisfaction_20240617_record_phone_yes';


## 071901


update ai_pattern
set include_codes='
["lucheng_satisfaction_20240617_common_other"]
'
where code like '%_other' and code != 'lucheng_satisfaction_20240617_common_other';



update ai_flow_node
set next_node_code_adapters='
[
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_reside_info_no_SYS_GENERATED_","lucheng_satisfaction_20240617_reside_info_no"
    ],
    "semantics": "no"
  },
  {
    "aiPatternCodes": [
       "lucheng_satisfaction_20240617_reside_info_yes_SYS_GENERATED_","lucheng_satisfaction_20240617_reside_info_yes"
    ],
    "semantics": "yes"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_reside_info_other_SYS_GENERATED_","lucheng_satisfaction_20240617_reside_info_other"
    ],
    "semantics": "other"
  }
]
',
    semantics_transform='{
  "yes": "lucheng_satisfaction_inbound_20240716_record_phone",
  "no": "lucheng_satisfaction_inbound_20240716_no_disturb",
  "other": "lucheng_satisfaction_inbound_20240716_question_again"
}'
where code = 'lucheng_satisfaction_inbound_20240716_reside_info';


update ai_flow_node
set next_node_code_adapters='

[
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_record_phone_no_worry_no_SYS_GENERATED_","lucheng_satisfaction_20240617_record_phone_no_worry_no"
    ],
    "semantics": "no"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_record_phone_no_worry_yes_SYS_GENERATED_","lucheng_satisfaction_20240617_record_phone_no_worry_yes"
    ],
    "semantics": "yes"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_record_phone_no_worry_other_SYS_GENERATED_","lucheng_satisfaction_20240617_record_phone_no_worry_other"
    ],
    "semantics": "other"
  }
]

',
    semantics_transform='{
  "yes": "lucheng_satisfaction_inbound_20240716_tks",
  "no": "lucheng_satisfaction_inbound_20240716_other_end",
  "other": "lucheng_satisfaction_inbound_20240716_other_end"
}'
where code = 'lucheng_satisfaction_inbound_20240716_record_phone_no_worry';


#

update ai_flow_node
set next_node_code_adapters='

[
  {
    "aiPatternCodes": [
     "lucheng_satisfaction_20240617_record_phone_no_SYS_GENERATED_","lucheng_satisfaction_20240617_record_phone_no"
    ],
    "semantics": "no"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_record_phone_yes_SYS_GENERATED_","lucheng_satisfaction_20240617_record_phone_yes"
    ],
    "semantics": "yes"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_record_phone_other_SYS_GENERATED_","lucheng_satisfaction_20240617_record_phone_other"
    ],
    "semantics": "other"
  }
]

',
    semantics_transform='{
  "yes": "lucheng_satisfaction_inbound_20240716_tks",
  "no": "lucheng_satisfaction_inbound_20240716_record_phone_no_worry",
  "other": "lucheng_satisfaction_inbound_20240716_record_phone_no_worry"
}'
where code = 'lucheng_satisfaction_inbound_20240716_record_phone';


update ai_flow_node
set next_node_code_adapters='

[
   {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_question_again_no_SYS_GENERATED_","lucheng_satisfaction_20240617_question_again_no"
    ],
    "semantics": "no"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_question_again_yes_SYS_GENERATED_","lucheng_satisfaction_20240617_question_again_yes"
    ],
    "semantics": "yes"
  },
  {
    "aiPatternCodes": [
      "lucheng_satisfaction_20240617_question_again_other_SYS_GENERATED_","lucheng_satisfaction_20240617_question_again_other"
    ],
    "semantics": "other"
  }
]

',
    semantics_transform='{
  "yes": "lucheng_satisfaction_inbound_20240716_record_phone",
  "no": "lucheng_satisfaction_inbound_20240716_no_disturb",
  "other": "lucheng_satisfaction_inbound_20240716_other_end"
}'
where code = 'lucheng_satisfaction_inbound_20240716_question_again';