##
select *
from ai_flow;

## feature_240403_node_ctx_cfg
alter table ai_flow_node
    add context text null comment '上下文';

alter table ai_flow
    add context text null comment '上下文';

## 240507
CREATE TABLE `ai_pattern` (
    `id`            int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `name`          varchar(128)              DEFAULT NULL COMMENT '名称',
    `code`          varchar(64)      NOT NULL COMMENT '编码',
    patterns        text             null comment '匹配模式，正则表达式。json 数组格式',
    `include_codes` text             null comment '自包含的编码。json 数组格式',
    `comment`       varchar(255)              DEFAULT NULL COMMENT '备注',
    `created_time`  timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`  timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code`(`code`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8 COMMENT ='ai匹配模式';

alter table ai_flow_node
    add column semantics_transform varchar(2000) null comment '语义转换';


select *
from ai_pattern;

##1010

alter table ai_flow
    add type          varchar(32)          null comment '类型：normal，dynamic',
    add released      tinyint(1) default 1 not null comment '已发布',
    add campaign_code varchar(32) comment '活动code',
    add tenant_code   varchar(32)          null comment '业务线编码',
    add created_by    varchar(32)          null comment '创建人',
    add ui_data       varchar(2000)        null comment 'ui数据',
    modify wait_voice varchar(1000) null comment '等待提示语音路径'
;

alter table ai_flow_node
    add node_action varchar(1000) null comment '节点的动作配置';