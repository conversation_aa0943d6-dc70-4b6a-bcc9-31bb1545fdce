INSERT INTO ai_flow (name, code, first_flow_node_code)
VALUES ('鹿城问卷20240415', 'lucheng_satisfaction_20240415', 'lucheng_satisfaction_20240415_reside_info');
INSERT INTO ai_flow (name, code, first_flow_node_code)
VALUES ('鹿城离开流程20240415', 'lucheng_leaving_20240415', 'lucheng_leaving_20240415_reside_info');


INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('先不打扰', 'lucheng_satisfaction_20240415_no_disturb', '好的，那这边先不打扰您了，祝您生活愉快，再见', null, '240415lucheng/fa/a_bdrnl',
        1);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('感谢您的配合', 'lucheng_satisfaction_20240415_tks',
        '好的，如您在鹿城区遇到困难可以联系到所属区域的网格员；稍后会以短信的形式下发网格员联系方式至您手机，请您注意查收。感谢您的配合，祝您生活愉快', null,
        '240415lucheng/fa/a_hd_rnzlcqydknkylxdssqydwgy', 1);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('再确认一下', 'lucheng_satisfaction_20240415_question_again', '抱歉，刚才没有听清。再跟您确认一下，请问您现在是住在鹿城区南汇街道对吗？', 'function acqSuitTipFlowCode(input, mobile,call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var strArrType = Java.type("java.lang.String[]")

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "lucheng_satisfaction_20240415_no_disturb";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.YES)) {
    return "lucheng_satisfaction_20240415_record_phone";
  } else {
    return "lucheng_satisfaction_20240415_other_end";
  }

}', '240415lucheng/fa/a_bq_gcmytq', 0);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('记录手机号码', 'lucheng_satisfaction_20240415_record_phone', '好的，那我可以记录一下您的手机号码吗？以便日后更好的为您提供服务', 'function acqSuitTipFlowCode(input, mobile,call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "lucheng_satisfaction_20240415_record_phone_no_worry";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.AU_YES)) {
    return "lucheng_satisfaction_20240415_tks";
  } else {
    return "lucheng_satisfaction_20240415_record_phone_no_worry";
  }

}', '240415lucheng/fa/a_jlhm', 0);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('记录手机号码不用担心', 'lucheng_satisfaction_20240415_record_phone_no_worry',
        '您不用担心，我们记录您的号码仅用于后续的社区网格服务，不会泄露您的个人信息，请问您同意记录您的号码吗？', 'function acqSuitTipFlowCode(input, mobile,call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "lucheng_satisfaction_20240415_other_end";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.AU_YES)) {
    return "lucheng_satisfaction_20240415_tks";
  } else {
    return "lucheng_satisfaction_20240415_other_end";
  }

}', '240415lucheng/fa/a_nbydx', 0);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('感谢您的配合other', 'lucheng_satisfaction_20240415_other_end',
        '如您在鹿城区遇到困难可以联系到所属区域的网格员；稍后会以短信的形式下发网格员联系方式至您手机，请您注意查收。感谢您的配合，祝您生活愉快', null,
        '240415lucheng/fa/a_rnzlcqydknkylxdssqydwgy', 1);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('居住询问', 'lucheng_satisfaction_20240415_reside_info', '您好，我是鹿城区南汇街道的网格工作人员，请问您最近住在南汇街道吗？', 'function acqSuitTipFlowCode(input, mobile,call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var strArrType = Java.type("java.lang.String[]")

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "lucheng_satisfaction_20240415_no_disturb";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.YES)) {
    return "lucheng_satisfaction_20240415_record_phone";
  } else {
    return "lucheng_satisfaction_20240415_question_again";
  }

}', '240415lucheng/fa/a_kcb', 0);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('居住询问', 'lucheng_leaving_20240415_reside_info', '您好，我是温州鹿城区网格工作人员，请问您现在还住在鹿城区南汇街道吗?', '
function acqSuitTipFlowCode(input, mobile,call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var strArrType = Java.type("java.lang.String[]")

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "lucheng_leaving_20240415_no_disturb";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.YES)) {
    return "lucheng_leaving_20240415_tks";
  } else {
    return "lucheng_leaving_20240415_question_again";
  }
}', '240415lucheng/fb/b_kcb', 0);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('感谢您的配合', 'lucheng_leaving_20240415_tks',
        '好的，如您在鹿城区遇到困难可以联系到所属区域的网格员；稍后会以短信的形式下发网格员联系方式至您手机，请您注意查收。感谢您的配合，祝您生活愉快', null,
        '240415lucheng/fb/b_hd_rnzlcqydkn', 1);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('再确认一下', 'lucheng_leaving_20240415_question_again', '抱歉，刚才没有听清。再跟您确认一下，请问您现在是住在鹿城区南汇街道对吗？', '
function acqSuitTipFlowCode(input, mobile,call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var strArrType = Java.type("java.lang.String[]")

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "lucheng_leaving_20240415_no_disturb";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.YES)) {
    return "lucheng_leaving_20240415_tks";
  } else {
    return "lucheng_leaving_20240415_other_end";
  }
}', '240415lucheng/fb/b_bq_gcmytq', 0);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('先不打扰', 'lucheng_leaving_20240415_no_disturb', '好的，那这边先不打扰您了。祝您生活愉快，再见', null, '240415lucheng/fb/b_bdrnl', 1);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('感谢您的配合other', 'lucheng_leaving_20240415_other_end',
        '如您在鹿城区遇到困难可以联系到所属区域的网格员；稍后会以短信的形式下发网格员联系方式至您手机，请您注意查收。感谢您的配合，祝您生活愉快', null,
        '240415lucheng/fb/b_rnzlcqydknkylxdssqydwgy', 1);


##
## ruian flow

INSERT INTO ai_flow (name, code, first_flow_node_code)
VALUES ('瑞安离开流程', 'ruian_leaving', 'ruian_leaving_reside_info');

INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('居住询问', 'ruian_leaving_reside_info', '您好，我是瑞安市网格工作人员，请问您现在还住在瑞安市吗?
', '
function acqSuitTipFlowCode(input, mobile,call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var strArrType = Java.type("java.lang.String[]")

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "ruian_leaving_no_disturb";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.YES)) {
    return "ruian_leaving_tks";
  } else {
    return "ruian_leaving_question_again";
  }
}', 'ruian_leaving/ralkBkcb', 0);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('感谢您的配合', 'ruian_leaving_tks',
        '好的，感谢您的配合，瑞安公安提醒您，日常生活请警惕各类诈骗，如刷单返利、冒充电商物流客服、冒充公检法、虚假征信、冒充领导、婚恋、交友等名义，让我们携手反诈，保护自己和身边的人，祝您生活愉快，再见。', null,
        'ruian_leaving/ralkB_hd_gxndph', 1);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('再确认一下', 'ruian_leaving_question_again', '抱歉，刚才没有听清。再跟您确认一下，请问您现在是住在瑞安市对吗？', '
function acqSuitTipFlowCode(input, mobile,call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var strArrType = Java.type("java.lang.String[]")

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "ruian_leaving_no_disturb";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.YES)) {
    return "ruian_leaving_tks";
  } else {
    return "ruian_leaving_other_end";
  }
}', 'ruian_leaving/ralkBbq_gcmtq', 0);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('先不打扰', 'ruian_leaving_no_disturb', '好的，那这边先不打扰您了。祝您生活愉快，再见', null, 'ruian_leaving/ralkB_hd_nxbdrn', 1);
INSERT INTO ai_flow_node (name, code, tip, script, voice_path, end)
VALUES ('感谢您的配合other', 'ruian_leaving_other_end',
        '好的，那先不打扰您了，稍后会有工作人员与您再联系，瑞安公安提醒您，日常生活请警惕各类诈骗，如刷单返利、冒充电商物流客服、冒充公检法、虚假征信、冒充领导、婚恋、交友等名义，让我们携手反诈，保护自己和身边的人，祝您生活愉快，再见。',
        null, 'ruian_leaving/ralkB_nxbdr_shhygzrylx_fztx', 1);


## 4,17 match world modify
update ai_flow_node
set next_node_code_adapters ='
[
  {
    "suitWords": [
      ".*还住在.*",
      ".*(?<!不)住在.*",
    ],
    "flowNodeCode": "lucheng_satisfaction_20240415_record_phone"
  }
]
'
where code in ('lucheng_satisfaction_20240415_reside_info', 'lucheng_satisfaction_20240415_question_again');

update ai_flow_node
set next_node_code_adapters ='
[
  {
    "suitWords": [
      ".*还住在.*",
      ".*(?<!不)住在.*",
    ],
    "flowNodeCode": "lucheng_leaving_20240415_tks"
  }
]
'
where code in ('lucheng_leaving_20240415_reside_info', 'lucheng_leaving_20240415_question_again');

update ai_flow_node
set next_node_code_adapters ='
[
  {
    "suitWords": [
      ".*还住在.*",
      ".*(?<!不)住在.*",
    ],
    "flowNodeCode": "ruian_leaving_tks"
  }
]
'
where code in ('ruian_leaving_reside_info', 'ruian_leaving_question_again');

select *
from ai_flow;

## 0419
update ai_flow_node
set next_node_code_adapters ='
[
  {
    "suitWords": [
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
    ".*没听懂.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ],
    "flowNodeCode": "lucheng_satisfaction_20240415_question_again"
  }
]
'
where code in ('lucheng_satisfaction_20240415_reside_info');

update ai_flow_node
set next_node_code_adapters ='
[
  {
    "suitWords": [
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
    ".*没听懂.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ],
    "flowNodeCode": "lucheng_satisfaction_20240415_other_end"
  }
]
'
where code in ('lucheng_satisfaction_20240415_question_again');


update ai_flow_node
set next_node_code_adapters ='
[
  {
    "suitWords": [
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
    ".*没听懂.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ],
    "flowNodeCode": "lucheng_satisfaction_20240415_record_phone_no_worry"
  }
]
'
where code in ('lucheng_satisfaction_20240415_record_phone');

update ai_flow_node
set next_node_code_adapters ='
[
  {
    "suitWords": [
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
    ".*没听懂.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ],
    "flowNodeCode": "lucheng_satisfaction_20240415_other_end"
  }
]
'
where code in ('lucheng_satisfaction_20240415_record_phone_no_worry');


update ai_flow_node
set next_node_code_adapters ='
[
  {
    "suitWords": [
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
    ".*没听懂.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ],
    "flowNodeCode": "lucheng_leaving_20240415_tks"
  }
]
'
where code in ('lucheng_leaving_20240415_question_again');

update ai_flow_node
set next_node_code_adapters ='
[
  {
    "suitWords": [
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
    ".*没听懂.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ],
    "flowNodeCode": "lucheng_leaving_20240415_other_end"
  }
]
'
where code in ('lucheng_leaving_20240415_question_again');

update ai_flow_node
set next_node_code_adapters ='
[
  {
    "suitWords": [
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
    ".*没听懂.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ],
    "flowNodeCode": "ruian_leaving_question_again"
  },

{
    "suitWords": [
      ".*早就搬了.*",".*前段时间搬的.*",".*上个月搬的.*",".*刚搬走.*",".*已经不住了.*",".*已经离开了瑞安.*",".*去别的地方了.*",".*到其他城市了.*",".*不在瑞安了.*",".*离开瑞安了.*",".*换城市了.*",".*搬家了.*",".*不住这了.*",".*已经不是瑞安人了.*",".*现在不是瑞安市民了.*",".*已经不在这居住了.*",".*已迁出瑞安.*",".*户口已迁出.*",".*人已经不在瑞安.*",".*目前在外地.*",".*人在外地.*",".*居住地变了.*",".*居住地址变了.*",".*住址变了,不是了.*",".*不住了.*",".*已经搬了.*",".*去别的地方了.*",".*已经离开了.*",".*现在在外地.*",".*户口迁出了.*",".*不在本地了.*",".*不是瑞安人了.*",".*非瑞安居民.*",".*换城市了.*",".*换地址了.*",".*住址变了.*",".*人在外地.*",".*居住地变了.*",".*不住原来的地方了.*",".*原址已变.*",".*地址有变.*",".*没在老地方了.*",".*已经不是这个地址了.*",".*已迁户口.*",".*不是本地人了.*",".*不在这里了.*",".*不居住在此了.*",".*没有没有.*",".*不不.*",".*没呢.*",".*不住在.*",".*没住在.*",".*在外地.*"
    ],
    "flowNodeCode": "ruian_leaving_no_disturb"
  },
{
    "suitWords": [
      ".*还住着.*",".*一直住.*",".*住呢.*",".*没搬.*",".*在的.*",".*在这.*",".*住在这.*",".*还在瑞安.*",".*没离开.*",".*还没走.*",".*住着呢.*",".*是的还住这.*",".*对还住着.*",".*是啊还在.*",".*嗯还未搬.*",".*是的一直住在这.*",".*对一直在瑞安.*",".*还居住在此.*",".*仍然住在瑞安.*",".*没有离开瑞安.*",".*仍在瑞安居住.*",".*是的还是瑞安市民.*",".*对仍是瑞安居民.*",".*还是瑞安人.*",".*一直是瑞安人.*",".*嗯没有搬走.*",".*还是住在原来的地方,是的.*",".*没错.*",".*还是瑞安人.*",".*还住在这.*",".*一直住着.*",".*还没搬.*",".*仍在瑞安.*",".*没有离开.*",".*还是这里.*",".*住在老地方.*",".*没换地方.*",".*还是原来的地址.*",".*一直是这个地址.*",".*没迁户口.*",".*户口还在这.*",".*还是本地人.*",".*本地居民.*",".*瑞安市民.*",".*瑞安居民.*",".*住在原处.*",".*原址未变.*",".*对对.*",".*是是.*",".*确实.*",".*当然.*"
    ],
    "flowNodeCode": "ruian_leaving_tks"
  }
]
'
where code in ('ruian_leaving_reside_info');

update ai_flow_node
set next_node_code_adapters ='
[
  {
    "suitWords": [
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
    ".*没听懂.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ],
    "flowNodeCode": "ruian_leaving_other_end"
  },

{
    "suitWords": [
      ".*早就搬了.*",".*前段时间搬的.*",".*上个月搬的.*",".*刚搬走.*",".*已经不住了.*",".*已经离开了瑞安.*",".*去别的地方了.*",".*到其他城市了.*",".*不在瑞安了.*",".*离开瑞安了.*",".*换城市了.*",".*搬家了.*",".*不住这了.*",".*已经不是瑞安人了.*",".*现在不是瑞安市民了.*",".*已经不在这居住了.*",".*已迁出瑞安.*",".*户口已迁出.*",".*人已经不在瑞安.*",".*目前在外地.*",".*人在外地.*",".*居住地变了.*",".*居住地址变了.*",".*住址变了,不是了.*",".*不住了.*",".*已经搬了.*",".*去别的地方了.*",".*已经离开了.*",".*现在在外地.*",".*户口迁出了.*",".*不在本地了.*",".*不是瑞安人了.*",".*非瑞安居民.*",".*换城市了.*",".*换地址了.*",".*住址变了.*",".*人在外地.*",".*居住地变了.*",".*不住原来的地方了.*",".*原址已变.*",".*地址有变.*",".*没在老地方了.*",".*已经不是这个地址了.*",".*已迁户口.*",".*不是本地人了.*",".*不在这里了.*",".*不居住在此了.*",".*没有没有.*",".*不不.*",".*没呢.*",".*不住在.*",".*没住在.*",".*在外地.*"
    ],
    "flowNodeCode": "ruian_leaving_no_disturb"
  },
{
    "suitWords": [
      ".*还住着.*",".*一直住.*",".*住呢.*",".*没搬.*",".*在的.*",".*在这.*",".*住在这.*",".*还在瑞安.*",".*没离开.*",".*还没走.*",".*住着呢.*",".*是的还住这.*",".*对还住着.*",".*是啊还在.*",".*嗯还未搬.*",".*是的一直住在这.*",".*对一直在瑞安.*",".*还居住在此.*",".*仍然住在瑞安.*",".*没有离开瑞安.*",".*仍在瑞安居住.*",".*是的还是瑞安市民.*",".*对仍是瑞安居民.*",".*还是瑞安人.*",".*一直是瑞安人.*",".*嗯没有搬走.*",".*还是住在原来的地方,是的.*",".*没错.*",".*还是瑞安人.*",".*还住在这.*",".*一直住着.*",".*还没搬.*",".*仍在瑞安.*",".*没有离开.*",".*还是这里.*",".*住在老地方.*",".*没换地方.*",".*还是原来的地址.*",".*一直是这个地址.*",".*没迁户口.*",".*户口还在这.*",".*还是本地人.*",".*本地居民.*",".*瑞安市民.*",".*瑞安居民.*",".*住在原处.*",".*原址未变.*",".*对对.*",".*是是.*",".*确实.*",".*当然.*"
    ],
    "flowNodeCode": "ruian_leaving_tks"
  }
]
'
where code in ('ruian_leaving_question_again');

### update script


## other words


## kid 0428

update ai_flow_node
set script='
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "qp_interactive_ivr_phone";
  } else if (input == "2") {

    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");

    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=3GUKRGBXJ416&callerNumber=" + mobile;

    var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "qp_interactive_ivr_view";
  } else {
    return "qp_interactive_ivr_main";
  }

}
'
where code = 'qp_interactive_ivr_main';


update ai_flow_node
set script='
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "qp_interactive_ivr_dxm_phone";
  } else if (input == "2") {
    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=3GUKRGBXJ416&callerNumber=" + mobile;

    var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "qp_interactive_ivr_dxm_view";
  } else {
    return "qp_interactive_menu_ivr_dxm";
  }

}
'
where code = 'qp_interactive_menu_ivr_dxm';

update ai_flow_node
set script='
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "qp_interactive_ivr_ygbx_phone";
  } else if (input == "2") {
    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");

    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=3GUKRGBXJ416&callerNumber=" + mobile;

    var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "qp_interactive_ivr_ygbx_view";
  } else {
    return "qp_interactive_menu_ivr_ygbx";
  }

}
'
where code = 'qp_interactive_menu_ivr_ygbx';


update ai_flow_node
set script='
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "qp_overmonth_menu_ivr_ygbx_1";
  } else if (input == "2") {
    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");

    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=3GUKRGBXJ416&callerNumber=" + mobile;

    var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "qp_overmonth_menu_ivr_ygbx_2";
  } else {
    return "qp_overmonth_menu_ivr_ygbx";
  }

}
'
where code = 'qp_overmonth_menu_ivr_ygbx';

update ai_flow_node
set script='
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else if (input == "2") {
    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");

    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=2SVB2GAKSF17&callerNumber=" + mobile;

    var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  } else {
    return "fssd_mediation_notice_ivr";
  }

}
'
where code = 'fssd_mediation_notice_ivr';

update ai_flow_node
set script='
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else if (input == "2") {
    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");

    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=3GUKRGBXJ416&callerNumber=" + mobile;
    var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  } else {
    return "qp_jdd_ivr";
  }

}
'
where code = 'qp_jdd_ivr';


## 0509 佛山顺德-调解通知（无短信）
INSERT INTO ai_flow_node (name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path, semantics_map, remote_voice)
VALUES ('fssd_no_sms_ivr', 'fssd_no_sms_ivr', '佛山顺德-调解通知（无短信）', null, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else {
    return "fssd_no_sms_ivr";
  }

}', 0, 0, 1, null, null, now(), now(), null, null, null, null, null, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/439",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}');

insert into ai_flow_to_node (ai_flow_code, ai_flow_node_code)
values ('fssd_no_sms_ivr', 'fssd_no_sms_ivr');

INSERT INTO ai_flow (name, code, first_flow_node_code, context)
VALUES ('佛山顺德-调解通知（无短信）', 'fssd_no_sms_ivr', 'fssd_no_sms_ivr', '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}');
# 0523 通用-减免商议ivr等3个
INSERT INTO ai_flow_node (name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path, semantics_map, remote_voice)
VALUES ('general_discussDeduct_ivr', 'general_discussDeduct_ivr', '通用-减免商议ivr', null, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else {
    return "general_discussDeduct_ivr";
  }

}', 0, 0, 1, null, null, now(), now(), null, null, null, null, null, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/442",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}');

insert into ai_flow_to_node (ai_flow_code, ai_flow_node_code)
values ('general_discussDeduct_ivr', 'general_discussDeduct_ivr');

INSERT INTO ai_flow (name, code, first_flow_node_code, context)
VALUES ('通用-减免商议ivr', 'general_discussDeduct_ivr', 'general_discussDeduct_ivr', '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}');


INSERT INTO ai_flow_node (name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path, semantics_map, remote_voice)
VALUES ('general_mediationDate_approaching_ivr', 'general_mediationDate_approaching_ivr', '通用-调解期限将满ivr', null, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else {
    return "general_mediationDate_approaching_ivr";
  }

}', 0, 0, 1, null, null, now(), now(), null, null, null, null, null, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/443",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}');

insert into ai_flow_to_node (ai_flow_code, ai_flow_node_code)
values ('general_mediationDate_approaching_ivr', 'general_mediationDate_approaching_ivr');

INSERT INTO ai_flow (name, code, first_flow_node_code, context)
VALUES ('通用-调解期限将满ivr', 'general_mediationDate_approaching_ivr', 'general_mediationDate_approaching_ivr', '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}');


INSERT INTO ai_flow_node (name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path, semantics_map, remote_voice)
VALUES ('PPD_mediationNotice_ivr', 'PPD_mediationNotice_ivr', '拍拍贷-调解通知ivr', null, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else {
    return "PPD_mediationNotice_ivr";
  }

}', 0, 0, 1, null, null, now(), now(), null, null, null, null, null, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/441",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}');

insert into ai_flow_to_node (ai_flow_code, ai_flow_node_code)
values ('PPD_mediationNotice_ivr', 'PPD_mediationNotice_ivr');

INSERT INTO ai_flow (name, code, first_flow_node_code, context)
VALUES ('拍拍贷-调解通知ivr', 'PPD_mediationNotice_ivr', 'PPD_mediationNotice_ivr', '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}');


###0531 lucheng_binjiang
update ai_flow_node
set next_node_code_adapters = '
[
  {
    "suitWords": [
".*你们是什么单位.*",
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
    ".*没听懂.*",
    ".*没听清楚.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ],
    "flowNodeCode": "lucheng_binjiang_satisfaction_20240529_question_again"
  },{
"suitWords": [
".*搬走.*",
    ],
    "flowNodeCode": "lucheng_binjiang_satisfaction_20240529_no_disturb"
  }
]
'
where code = 'lucheng_binjiang_satisfaction_20240529_reside_info';

update ai_flow_node
set next_node_code_adapters = '

[
  {
    "suitWords": [
".*你们是什么单位.*",
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
    ".*没听懂.*",
     ".*没听清楚.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ],
    "flowNodeCode": "lucheng_binjiang_satisfaction_20240529_other_end"
  }
,{
"suitWords": [
".*搬走.*",
    ],
    "flowNodeCode": "lucheng_binjiang_satisfaction_20240529_no_disturb"
  }
]
'
where code = 'lucheng_binjiang_satisfaction_20240529_question_again';

### PPD_discussDeduct_ivr 0604

INSERT INTO ai_flow_node (name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path, semantics_map, remote_voice)
VALUES ('PPD_discussDeduct_ivr', 'PPD_discussDeduct_ivr', '拍拍贷平台', null, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else {
    return "PPD_discussDeduct_ivr";
  }

}', 0, 0, 1, null, null, now(), now(), null, null, null, null, null, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/450",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}');

insert into ai_flow_to_node (ai_flow_code, ai_flow_node_code)
values ('PPD_discussDeduct_ivr', 'PPD_discussDeduct_ivr');

INSERT INTO ai_flow (name, code, first_flow_node_code, context)
VALUES ('拍拍贷平台', 'PPD_discussDeduct_ivr', 'PPD_discussDeduct_ivr', '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}');


### ly_mediationNotice_ivr 0611

delete
from ai_flow_node
where code = 'ly_mediationNotice_ivr';

INSERT INTO ai_flow_node (name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path, semantics_map, remote_voice)
VALUES ('ly_mediationNotice_ivr', 'ly_mediationNotice_ivr', '临颍县', null, '
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else  if (input == "2") {


var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");



    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url="http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=BF627FJ9W822&callerNumber=" + mobile;

var userData=AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if(kid != null){
      url += "&kid="+kid;
    }

    WebClientUtils.get(url);

    return "fssd_mediation_notice_ivr_2";
  }else{
    return "ly_mediationNotice_ivr";
    }

}', 0, 0, 1, null, null, now(), now(), null, null, null, null, null, '{
  "url": "http://link-conf.linkup.k2.wacai.info/node/out-api/script-items/451",
  "method": "POST",
  "param": {"voiceFrom":"aiqi","volume":"55","vocalSpeed":"50","intonation":"0"}
}');

insert into ai_flow_to_node (ai_flow_code, ai_flow_node_code)
values ('ly_mediationNotice_ivr', 'ly_mediationNotice_ivr');

INSERT INTO ai_flow (name, code, first_flow_node_code, context)
VALUES ('临颍县', 'ly_mediationNotice_ivr', 'ly_mediationNotice_ivr', '
{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": true,
    "interval": 3,
    "times": 2
  }
}');

###240617

INSERT INTO `ai_flow` (`name`, `code`, `first_flow_node_code`, `comment`, `created_time`, `updated_time`, `context`,
                       `call_event_call_back_url`, `not_receive_voice_input`, `record_path`, `wait_voice`,
                       `voice_end_call_back_url`)
VALUES ('鹿城区滨江问卷20240529', 'lucheng_binjiang_satisfaction_20240529',
        'lucheng_binjiang_satisfaction_20240529_reside_info', NULL, now(), now(), NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `ai_flow_node` (`name`, `code`, `tip`, `sms_ruler_id`, `script`, `end`, `trigger_input`,
                            `receive_simple_dtmf`, `ext_info`, `comment`, `created_time`, `updated_time`,
                            `simple_next_flow_code`, `next_node_code_adapters`, `transfer_queue_code`, `voice_path`,
                            `semantics_map`, `remote_voice`, `context`)
VALUES ('先不打扰', 'lucheng_binjiang_satisfaction_20240529_no_disturb', '好的，那这边先不打扰您了，祝您生活愉快，再见', NULL, NULL, 1, 0, 0,
        NULL, NULL, now(), now(), NULL, NULL, NULL, '240415lucheng/binjiang/a_bdrnl', NULL, NULL, NULL),
('感谢您的配合', 'lucheng_binjiang_satisfaction_20240529_tks',
 '好的，如您在鹿城区遇到困难可以联系到所属区域的网格员；稍后会以短信的形式下发网格员联系方式至您手机，请您注意查收。感谢您的配合，祝您生活愉快', NULL, NULL, 1, 0, 0, NULL, NULL, now(),
 now(), NULL, NULL, NULL, '240415lucheng/binjiang/a_hd_rnzlcqydknkylxdssqydwgy', NULL, NULL, NULL),
('再确认一下', 'lucheng_binjiang_satisfaction_20240529_question_again', '抱歉，刚才没有听清。再跟您确认一下，请问您现在是住在鹿城区滨江街道对吗？', NULL, 'function acqSuitTipFlowCode(input, mobile,call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var strArrType = Java.type("java.lang.String[]")

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "lucheng_binjiang_satisfaction_20240529_no_disturb";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.YES)) {
    return "lucheng_binjiang_satisfaction_20240529_record_phone";
  } else {
    return "lucheng_binjiang_satisfaction_20240529_other_end";
  }

}', 0, 0, 0, NULL, NULL, now(), now(), NULL, '
[
  {
    "suitWords": [
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
    ".*没听懂.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ],
    "flowNodeCode": "lucheng_binjiang_satisfaction_20240529_other_end"
  }
]
', NULL, '240415lucheng/binjiang/a_bq_gcmytq', NULL, NULL, NULL),
('记录手机号码', 'lucheng_binjiang_satisfaction_20240529_record_phone', '好的，那我可以记录一下您的手机号码吗？以便日后更好的为您提供服务', NULL, 'function acqSuitTipFlowCode(input, mobile,call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "lucheng_binjiang_satisfaction_20240529_record_phone_no_worry";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.AU_YES)) {
    return "lucheng_binjiang_satisfaction_20240529_tks";
  } else {
    return "lucheng_binjiang_satisfaction_20240529_record_phone_no_worry";
  }

}', 0, 0, 0, NULL, NULL, now(), now(), NULL, '
[
  {
    "suitWords": [
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
    ".*没听懂.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ],
    "flowNodeCode": "lucheng_binjiang_satisfaction_20240529_record_phone_no_worry"
  }
]
', NULL, '240415lucheng/binjiang/a_jlhm', NULL, NULL, NULL),
('记录手机号码不用担心', 'lucheng_binjiang_satisfaction_20240529_record_phone_no_worry',
 '您不用担心，我们记录您的号码仅用于后续的社区网格服务，不会泄露您的个人信息，请问您同意记录您的号码吗？', NULL, 'function acqSuitTipFlowCode(input, mobile,call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "lucheng_binjiang_satisfaction_20240529_other_end";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.AU_YES)) {
    return "lucheng_binjiang_satisfaction_20240529_tks";
  } else {
    return "lucheng_binjiang_satisfaction_20240529_other_end";
  }

}', 0, 0, 0, NULL, NULL, now(), now(), NULL, '
[
  {
    "suitWords": [
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
    ".*没听懂.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ],
    "flowNodeCode": "lucheng_binjiang_satisfaction_20240529_other_end"
  }
]
', NULL, '240415lucheng/binjiang/a_nbydx', NULL, NULL, NULL),
('感谢您的配合other', 'lucheng_binjiang_satisfaction_20240529_other_end',
 '如您在鹿城区遇到困难可以联系到所属区域的网格员；稍后会以短信的形式下发网格员联系方式至您手机，请您注意查收。感谢您的配合，祝您生活愉快', NULL, NULL, 1, 0, 0, NULL, NULL,
 '2024-04-16 14:01:58.0', '2024-04-16 14:01:58.0', NULL, NULL, NULL, '240415lucheng/binjiang/a_rnzlcqydknkylxdssqydwgy',
 NULL, NULL, NULL),
('居住询问', 'lucheng_binjiang_satisfaction_20240529_reside_info', '您好，我是鹿城区滨江街道的网格工作人员，请问您最近住在滨江街道吗？', NULL, 'function acqSuitTipFlowCode(input, mobile,call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var strArrType = Java.type("java.lang.String[]")

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "lucheng_binjiang_satisfaction_20240529_no_disturb";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.YES)) {
    return "lucheng_binjiang_satisfaction_20240529_record_phone";
  } else {
    return "lucheng_binjiang_satisfaction_20240529_question_again";
  }

}', 0, 0, 0, NULL, NULL, now(), now(), NULL, '
[
  {
    "suitWords": [
      ".*有什么事你说.*",
        ".*不清楚.*",
      ".*不明白.*",
    ".*没听懂.*",
    ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
        ".*不好意思.*",
        ".*没时间.*",".*你是哪个.*",".*有什么事.*",".*这是什么.*",".*喂，你好。.*",".*那你是谁呀.*",".*你是谁.*",".*你们是谁.*",".*你是说.*",".*是我听错了.*",".*是这样吗.*",".*有事吗.*"
    ],
    "flowNodeCode": "lucheng_binjiang_satisfaction_20240529_question_again"
  }
]
', NULL, '240415lucheng/binjiang/a_kcb', NULL, NULL, NULL);

### 620

update ai_flow_node
set tip='好的，那我可以记录一下您的手机号吗？以后社区有什么活动或者消息，网格员也可以联系到您!', voice_path='20240617lc_kcb/bjsjh.wav'
where code = 'lucheng_satisfaction_20240617_record_phone';

update ai_flow_node
set tip='您好，我是鹿城区#{#streetName}的工作人员，为加强居民与网格员的互动和服务，我们在做网格宣传工作，一会儿会把您所在区域的网格员信息同步给您，您最近住在#{#streetName}吗?'
where code = 'lucheng_satisfaction_20240617_reside_info';


### 0702
update ai_flow_node
set next_node_code_adapters='
[
  {
    "suitWords": [
      ".*有什么事你说.*",
      ".*不清楚.*",
      ".*不明白.*",
      ".*没听清楚.*",
      ".*没听懂.*",
      ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
      ".*不好意思.*",
      ".*没时间.*",
      ".*你是哪个.*",
      ".*有什么事.*",
      ".*这是什么.*",
      ".*喂，你好。.*",
      ".*那你是谁呀.*",
      ".*你是谁.*",
      ".*你们是谁.*",
      ".*你是说.*",
      ".*是我听错了.*",
      ".*是这样吗.*",
      ".*有事吗.*"
    ],
    "flowNodeCode": "lucheng_satisfaction_20240617_other_end"
  },
  {
    "suitWords": [
      ".*现在打的.*",
      ".*打的.*",
      ".*我的电话.*",
      ".*电话.*",
      ".*就打.*",
      ".*这个.*",
      ".*你看.*",
      ".*你大的.*",
      ".*你打.*",
      ".*你打的.*",
      ".*这个.*",
      ".*就这个.*",
      ".*号码.*",
      ".*打过来.*",
      ".*[0-9]{3,}.*"
    ],
    "flowNodeCode": "lucheng_satisfaction_20240617_tks"
  }
]
'
where code = 'lucheng_satisfaction_20240617_record_phone_no_worry';


update ai_flow_node
set next_node_code_adapters='
[
  {
    "suitWords": [
      ".*有什么事你说.*",
      ".*不清楚.*",
      ".*不明白.*",
      ".*没听清楚.*",
      ".*没听懂.*",
      ".*(?<!不)是不是.*",
      ".*(?<!没)有没有.*",
      ".*不知道.*",
      ".*不好意思.*",
      ".*没时间.*",
      ".*你是哪个.*",
      ".*有什么事.*",
      ".*这是什么.*",
      ".*喂，你好。.*",
      ".*那你是谁呀.*",
      ".*你是谁.*",
      ".*你们是谁.*",
      ".*你是说.*",
      ".*是我听错了.*",
      ".*是这样吗.*",
      ".*有事吗.*"
    ],
    "flowNodeCode": "lucheng_satisfaction_20240617_record_phone_no_worry"
  },
  {
    "suitWords": [
      ".*现在打的.*",
      ".*打的.*",
      ".*我的电话.*",
      ".*电话.*",
      ".*就打.*",
      ".*这个.*",
      ".*你看.*",
      ".*你大的.*",
      ".*你打.*",
      ".*你打的.*",
      ".*这个.*",
      ".*就这个.*",
      ".*号码.*",
      ".*打过来.*",
      ".*[0-9]{3,}.*"
    ],
    "flowNodeCode": "lucheng_satisfaction_20240617_tks"
  }
]
'
where code = 'lucheng_satisfaction_20240617_record_phone';


### 0703 amx

INSERT INTO ai_flow (name, code, first_flow_node_code, comment, created_time, updated_time, call_event_call_back_url,
                     not_receive_voice_input, record_path, wait_voice, context, voice_end_call_back_url)
VALUES ('alchemist_ai_inlet_guidance', 'alchemist_ai_inlet_guidance', 'alchemist_ai_inlet_guidance', null,
        '2024-02-04 11:02:44', '2024-04-30 10:30:34', 'http://gofer-web-api.linkup.k2.wacai.info/call/status', 1,
        '/data/program/counselor/ai/record/counselor-ai/biz/pharos/cus/',
        '/data/program/counselor/ai/record/counselor-ai/biz/pharos/voice/amc_test_wait.wav', '{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": false,
    "interval": 50,
    "times": 0
  }
}', 'http://gofer-web-api.linkup.k2.wacai.info/call/tts/end');
INSERT INTO ai_flow (name, code, first_flow_node_code, comment, created_time, updated_time, call_event_call_back_url,
                     not_receive_voice_input, record_path, wait_voice, context, voice_end_call_back_url)
VALUES ('alchemist_register_guidance', 'alchemist_register_guidance', 'alchemist_register_guidance', null,
        '2024-02-04 11:02:44', '2024-04-30 10:30:34', 'http://gofer-web-api.linkup.k2.wacai.info/call/status', 1,
        '/data/program/counselor/ai/record/counselor-ai/biz/pharos/cus/',
        '/data/program/counselor/ai/record/counselor-ai/biz/pharos/voice/amc_test_wait.wav', '{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": false,
    "interval": 50,
    "times": 0
  }
}', 'http://gofer-web-api.linkup.k2.wacai.info/call/tts/end');
INSERT INTO ai_flow (name, code, first_flow_node_code, comment, created_time, updated_time, call_event_call_back_url,
                     not_receive_voice_input, record_path, wait_voice, context, voice_end_call_back_url)
VALUES ('alchemist_ai_sms_guidance', 'alchemist_ai_sms_guidance', 'alchemist_ai_sms_guidance', null,
        '2024-02-04 11:02:44', '2024-04-30 10:30:34', 'http://gofer-web-api.linkup.k2.wacai.info/call/status', 1,
        '/data/program/counselor/ai/record/counselor-ai/biz/pharos/cus/',
        '/data/program/counselor/ai/record/counselor-ai/biz/pharos/voice/amc_test_wait.wav', '{
  "waitVoiceCfg": {
    "enable": true,
    "repeatSelf": false,
    "interval": 50,
    "times": 0
  }
}', 'http://gofer-web-api.linkup.k2.wacai.info/call/tts/end');

INSERT INTO ai_flow_node (name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path, semantics_map, remote_voice, context)
VALUES ('alchemist_ai_inlet_guidance', 'alchemist_ai_inlet_guidance', 'alchemist_ai_inlet_guidance', null, null, 0, 0,
        0, null, null, '2024-02-04 11:03:22', '2024-02-05 15:14:20', null, null, null,
        'biz/pharos/alchemist_ai_inlet_guidance', null, null, null);
INSERT INTO ai_flow_node (name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path, semantics_map, remote_voice, context)
VALUES ('alchemist_ai_sms_guidance', 'alchemist_ai_sms_guidance', 'alchemist_ai_sms_guidance', null, null, 0, 0, 0,
        null, null, '2024-02-04 11:03:22', '2024-02-05 15:14:20', null, null, null,
        'biz/pharos/alchemist_ai_sms_guidance', null, null, null);
INSERT INTO ai_flow_node (name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path, semantics_map, remote_voice, context)
VALUES ('alchemist_register_guidance', 'alchemist_register_guidance', 'alchemist_register_guidance', null, null, 0, 0,
        0, null, null, '2024-02-04 11:03:22', '2024-02-05 15:14:20', null, null, null,
        'biz/pharos/alchemist_register_guidance', null, null, null);

