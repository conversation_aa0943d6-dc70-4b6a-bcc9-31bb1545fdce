INSERT INTO ai_flow (id, name, code, first_flow_node_code, comment, created_time, updated_time)
VALUES (3, 'lucheng_satisfaction', 'lucheng_satisfaction', 'lucheng_satisfaction_reside_info', null,
        '2023-12-11 16:14:59', '2023-12-11 16:14:59');
INSERT INTO ai_flow (id, name, code, first_flow_node_code, comment, created_time, updated_time)
VALUES (4, 'lucheng_satisfaction_questionnaire_remind', 'lucheng_satisfaction_questionnaire_remind',
        'lucheng_satisfaction_questionnaire_remind', null, '2023-12-11 16:14:59', '2023-12-11 16:14:59');
INSERT INTO ai_flow (id, name, code, first_flow_node_code, comment, created_time, updated_time)
VALUES (5, 'lucheng_leaving', 'lucheng_leaving', 'lucheng_leaving_reside_info', null, '2023-12-11 16:15:25',
        '2023-12-11 16:15:25');

INSERT INTO ai_flow_node (id, name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path)
VALUES (18, '鹿城区居住询问', 'lucheng_leaving_reside_info', '鹿城区居住询问', null, 'function acqSuitTipFlowCode(input, mobile,
                            callContext, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");
  if (AiTipFlowHelper.suitSpecialWord(t)) {
    return "lucheng_leaving_personal_phone";
  }

  if (StringUtils.containsAny(t, AiTipFlowHelper.MAIN_ANSWER_N)) {
    return "lucheng_leaving_personal_phone";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.MAIN_ANSWER_Y)) {
    return "lucheng_leaving_tks";
  } else {
    return "lucheng_leaving_personal_phone";
  }
}', 0, 0, 0, null, null, '2023-12-11 15:09:54', '2023-12-11 16:01:36', null, null, null, null);
INSERT INTO ai_flow_node (id, name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path)
VALUES (19, '感谢您的配合', 'lucheng_leaving_tks', '感谢您的配合', null, null, 1, 0, 0, null, null, '2023-12-11 15:20:44',
        '2023-12-11 15:20:44', null, null, null, null);
INSERT INTO ai_flow_node (id, name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path)
VALUES (20, '手机号码', 'lucheng_leaving_personal_phone', '好的，请问这个手机号码是您本人的吗？', null, 'function acqSuitTipFlowCode(input, mobile,
                            callContext, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");

  if (StringUtils.containsAny(input, AiTipFlowHelper.MAIN_ANSWER_QUERY)) {
    return "lucheng_leaving_transfer";
  }

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (AiTipFlowHelper.suitSpecialWord(t)) {
    return "lucheng_leaving_transfer";
  }

  if (StringUtils.containsAny(t, AiTipFlowHelper.PHONE_N)) {
    return "lucheng_leaving_no_disturb";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.PHONE_Y)) {
    return "lucheng_leaving_authorization";
  } else {
    return "lucheng_leaving_transfer";
  }
}', 0, 0, 0, null, null, '2023-12-11 15:21:48', '2023-12-11 16:01:36', null, null, null, null);
INSERT INTO ai_flow_node (id, name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path)
VALUES (21, '号码授权', 'lucheng_leaving_authorization', '是否同意本机号码授权', null, 'function acqSuitTipFlowCode(input, mobile,
                            callContext, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");

  if (StringUtils.containsAny(input, AiTipFlowHelper.MAIN_ANSWER_QUERY)) {
    return "lucheng_leaving_transfer";
  }

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (AiTipFlowHelper.suitSpecialWord(t)) {
    return "lucheng_leaving_transfer";
  }

  if (StringUtils.containsAny(t, AiTipFlowHelper.MAIN_ANSWER_N)) {
    return "lucheng_leaving_transfer";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.MAIN_ANSWER_Y) || StringUtils.containsAny(t, AiTipFlowHelper.PHONE_AUTHORIZATION) ) {
    return "lucheng_leaving_tks";
  } else {
    return "lucheng_leaving_transfer";
  }

}', 0, 0, 0, null, null, '2023-12-11 15:22:19', '2023-12-11 16:01:36', null, null, null, null);
INSERT INTO ai_flow_node (id, name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path)
VALUES (22, '电话接通中', 'lucheng_leaving_transfer', '电话接通中', null, null, 0, 0, 0, null, null, '2023-12-11 15:23:31',
        '2023-12-11 15:23:31', null, null, '1190', null);
INSERT INTO ai_flow_node (id, name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path)
VALUES (23, '先不打扰', 'lucheng_leaving_no_disturb', '先不打扰', null, null, 1, 0, 0, null, null, '2023-12-11 15:24:25',
        '2023-12-11 15:24:25', null, null, null, null);
INSERT INTO ai_flow_node (id, name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path)
VALUES (24, '居住询问', 'lucheng_satisfaction_reside_info', '居住询问', null, 'function acqSuitTipFlowCode(input, mobile,
                            call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");
  if (AiTipFlowHelper.suitSpecialWord(t)) {
    return "lucheng_satisfaction_no_disturb";
  }

  if (StringUtils.containsAny(t, AiTipFlowHelper.MAIN_ANSWER_N)) {
    return "lucheng_satisfaction_no_disturb";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.MAIN_ANSWER_Y)) {
    return "lucheng_satisfaction_questionnaire";
  } else {
    return "lucheng_satisfaction_no_disturb";
  }

}', 0, 0, 0, null, null, '2023-12-11 15:41:53', '2023-12-11 15:57:00', null, null, null, null);
INSERT INTO ai_flow_node (id, name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path)
VALUES (25, '问卷', 'lucheng_satisfaction_questionnaire', '问卷', null, null, 1, 0, 0, null, null, '2023-12-11 15:41:53',
        '2023-12-11 15:41:53', null, null, null, null);
INSERT INTO ai_flow_node (id, name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path)
VALUES (26, '先不打扰', 'lucheng_satisfaction_no_disturb', '先不打扰', null, null, 1, 0, 0, null, null, '2023-12-11 15:41:53',
        '2023-12-11 15:41:53', null, null, null, null);
INSERT INTO ai_flow_node (id, name, code, tip, sms_ruler_id, script, end, trigger_input, receive_simple_dtmf, ext_info,
                          comment, created_time, updated_time, simple_next_flow_code, next_node_code_adapters,
                          transfer_queue_code, voice_path)
VALUES (27, '问卷提醒', 'lucheng_satisfaction_questionnaire_remind', '问卷提醒', null, null, 1, 0, 0, null, null,
        '2023-12-11 15:43:14', '2023-12-11 15:43:14', null, null, null, null);


