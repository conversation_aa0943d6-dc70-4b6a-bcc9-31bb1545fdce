CREATE TABLE `ai_flow` (
    `id`                   int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `name`                 varchar(128)              DEFAULT NULL COMMENT '名称',
    `code`                 varchar(64)      NOT NULL COMMENT '编码',

    `first_flow_node_code` varchar(64)               DEFAULT NULL COMMENT '流程首个节点',

    `comment`              varchar(255)              DEFAULT NULL COMMENT '备注',
    `created_time`         timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`         timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code`(`code`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8 COMMENT ='ai流程';

CREATE TABLE `ai_flow_to_node` (
    `id`                int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `ai_flow_code`      varchar(64)      NOT NULL COMMENT '流程编码',
    `ai_flow_node_code` varchar(64)      NOT NULL COMMENT '节点编码',
    `created_time`      timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time`      timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code`(`ai_flow_code`, ai_flow_node_code)
) ENGINE = InnoDB DEFAULT CHARSET = utf8 COMMENT ='ai流程到节点连接表';


