function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var strArrType = Java.type("java.lang.String[]")

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  var repeatTimes = call.getCallContext().get("repeatTimes");
  if (repeatTimes == null) {
    repeatTimes = 0;
  }

  if (repeatTimes < 1) {
    if (StringUtils.containsAny(input, Java.to(["不清楚", "不明白", "没听懂", "干嘛的", "怎么了", "你谁啊", "什么事"], strArrType))) {
      call.getCallContext().put("repeatTimes", ++repeatTimes);
      return "lucheng_satisfaction_reside_info";
    }
  }

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "lucheng_satisfaction_thx";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.YES)) {
    return "lucheng_satisfaction_record_phone";
  } else {
    return "lucheng_satisfaction_staff_contact";
  }

}