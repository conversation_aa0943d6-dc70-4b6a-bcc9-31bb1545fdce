function acqSuitTipFlowCode(input, mobile,
                            callContext, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");

  if (StringUtils.containsAny(input, AiTipFlowHelper.MAIN_ANSWER_QUERY)) {
    return AiTipFlowHelper.WENLING_2ED + "transfer";
  }

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (AiTipFlowHelper.suitSpecialWord(t)) {
    return AiTipFlowHelper.WENLING_2ED + "transfer";
  }

  if (StringUtils.containsAny(t, AiTipFlowHelper.MAIN_ANSWER_N)) {
    return AiTipFlowHelper.WENLING_2ED + "transfer";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.MAIN_ANSWER_Y) || StringUtils.containsAny(t, AiTipFlowHelper.PHONE_AUTHORIZATION)) {
    return AiTipFlowHelper.WENLING_2ED + "tks";
  } else {
    return AiTipFlowHelper.WENLING_2ED + "transfer";
  }

}