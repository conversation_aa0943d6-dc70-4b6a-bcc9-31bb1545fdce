function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var strArrType = Java.type("java.lang.String[]")

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "ruian_leaving_no_disturb";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.YES)) {
    return "ruian_leaving_tks";
  } else {
    return "ruian_leaving_other_end";
  }
}