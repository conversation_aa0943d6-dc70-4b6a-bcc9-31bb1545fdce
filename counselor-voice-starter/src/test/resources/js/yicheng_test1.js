function acqSuitTipFlowCode(input, mobile, call, scriptContext) {

  var Thread = Java.type("java.lang.Thread");

  try {
    Thread.sleep(500);
  } catch (e) {
    scriptContext.log.error("err", e);
  }

  if (input == "1") {
    return "yicheng_dtmf_1";
  } else if (input == "2") {

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");

    WebClientUtils.get("http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=BP86AX3P8C12&callerNumber=" + mobile);

    return "yicheng_dtmf_2";
  } else {
    return "yicheng_jianjie";
  }

}