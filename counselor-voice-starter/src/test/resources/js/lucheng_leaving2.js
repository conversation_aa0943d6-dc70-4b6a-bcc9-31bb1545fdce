function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var strArrType = Java.type("java.lang.String[]")

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "lucheng_leaving_b1_no_disturb";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.YES) || StringUtils.containsAny(Java.to(["满意"], strArrType))) {
    return "lucheng_leaving_b1_tks";
  } else {
    return "lucheng_leaving_b1_no_disturb";
  }
}