function acqSuitTipFlowCode(input, mobile, call, scriptContext) {

  var hasSendSms = call.getCallContext().get("hasSendSms");
  if (hasSendSms == null) {
    hasSendSms = false;
  }

  if (!hasSendSms) {
    var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");
    var url = "http://themis.tianjuhe.k2.xamc.info/themis/sms/inbound-sms?courtCode=NTDDQEFOOZ14&callerNumber=" + mobile
    var userData = AiTipFlowHelper.callUserDataCache.acquireUserData(call.getUuid());
    var kid = userData.getString("kaseId");
    if (kid != null) {
      url += "&kid=" + kid;
    }

    WebClientUtils.get(url);
    call.getCallContext().put("hasSendSms", true);
  }

  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else if (input == "2") {
    return "fssd_mediation_notice_ivr_2";
  } else {
    return "pd_default_1";
  }

}