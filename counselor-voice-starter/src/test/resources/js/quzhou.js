function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  var openAiChatService = Java.type("com.wacai.loan.counselor.services.openai.OpenAiChatService");
  var flag = openAiChatService.semanticQuZhouTravel(call.getUuid(), call.getAiTipFlow().getCode(), t, 2);
  var repeatTimes = call.getCallContext().get("repeatTimes");
  if (repeatTimes == null) {
    repeatTimes = 0;
  }
  if (repeatTimes >= 2) {
    call.getCallContext().put("repeatTimes", 0);
    return "quzhou_travel_20241011_zaijian";
  } else {
    call.getCallContext().put("repeatTimes", ++repeatTimes);
    if (flag == "clear") {
      return "quzhou_travel_20241011_qitafei";
    } else if (flag == "other") {
      call.getCallContext().put("repeatTimes", 0);
      return "quzhou_travel_20241011_zaijian";
    } else if (flag == "hate") {
      return "quzhou_travel_20241011_sorryzaijian";
    } else {
      return "quzhou_travel_20241011_qitafei";
    }
  }
}
 
function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");

  var openAiChatService = Java.type("com.wacai.loan.counselor.services.openai.OpenAiChatService");
  var flag = openAiChatService.semanticQuZhouTravel(call.getUuid(), call.getAiTipFlow().getCode(), t, 2);
  if (flag == "clear") {
    return "quzhou_travel_20241011_qitafei";
  } else if (flag == "other") {
    return "quzhou_travel_20241011_zaijian";
  } else if (flag == "hate") {
    return "quzhou_travel_20241011_zaijian";
  } else {
    return "quzhou_travel_20241011_qitafei";
  }

}