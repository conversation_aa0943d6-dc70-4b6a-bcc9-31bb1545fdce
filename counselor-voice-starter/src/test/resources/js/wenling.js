function acqSuitTipFlowCode(input, mobile,
                            call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");

  if (StringUtils.containsAny(input, AiTipFlowHelper.MAIN_ANSWER_QUERY)) {
    return AiTipFlowHelper.WENLING + "transfer";
  }

  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");
  if (AiTipFlowHelper.suitSpecialWord(t)) {
    return AiTipFlowHelper.WENLING + "transfer";
  }

  if (StringUtils.containsAny(t, AiTipFlowHelper.MAIN_ANSWER_REGISTERED)) {
    return AiTipFlowHelper.WENLING + "tks";
  }

  if (StringUtils.containsAny(t, AiTipFlowHelper.PHONE_N)) {
    return AiTipFlowHelper.WENLING + "register_way";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.PHONE_Y) || StringUtils.containsAny(t, AiTipFlowHelper.PHONE_AUTHORIZATION)) {
    return AiTipFlowHelper.WENLING + "sms_address";
  } else {
    return AiTipFlowHelper.WENLING + "register_way";
  }

}