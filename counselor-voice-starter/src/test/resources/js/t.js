function acqSuitTipFlowCode(input, mobile, call, scriptContext) {

  var waitVoiceCfg = new Java.type("com.wacai.loan.counselor.dataobject.bo.WaitVoiceCfg");

  waitVoiceCfg.setEnable(true);
  waitVoiceCfg.setRepeatSelf(true);
  waitVoiceCfg.setInterval(5);

  call.getCallContext().put("waitVoiceCfg", waitVoiceCfg);

  if (input == "1") {
    return "fssd_mediation_notice_ivr_1";
  } else if (input == "2") {
    return "qp_jdd_no_sms_ivr_main";
  } else {
    return "qp_jdd_no_sms_ivr_main";
  }

}