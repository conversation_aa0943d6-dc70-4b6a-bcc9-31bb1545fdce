function acqSuitTipFlowCode(input, mobile, call, scriptContext) {

  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");

  if (input == "1") {
    return "masterTest_ivr_interative_phone";
  } else if (input == "2") {

    var WebClientUtils = Java.type("com.wacai.loan.counselor.utils.WebClientUtils");

    var url = "http://themis.aliyun-sifa.wke-office.test.wacai.info/themis/sms/inbound-sms?courtCode=6NAQVZCCLE12&callerNumber=" + mobile;

    var userData = AiTipFlowHelper.callUserDataCache.acquireUserData(call.uuid);
    var kid = userData.getString("kid");
    if (kid != null) {
      url += "&kid=" + kid
    }

    WebClientUtils.get(url);

    return "masterTest_ivr_interative_view";
  } else {
    return "ivr_par_test";
  }

}