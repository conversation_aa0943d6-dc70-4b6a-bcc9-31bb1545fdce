function acqSuitTipFlowCode(input, mobile, call, scriptContext) {
  var log = scriptContext.log;
  var AiTipFlowHelper = Java.type("com.wacai.loan.counselor.ai.AiTipFlowHelper");
  var StringUtils = Java.type("org.apache.commons.lang3.StringUtils");
  var t = input.replaceAll(AiTipFlowHelper.FUHAO, "");


  if (AiTipFlowHelper.suitSpecialWord(t)) {
    log.info("f1");
    return "lucheng_satisfaction_nanhui_staff_contact";
  }
  if (StringUtils.containsAny(t, AiTipFlowHelper.OTHER)) {
    log.info("f2");
    return "lucheng_satisfaction_nanhui_staff_contact";
  }


  if (StringUtils.containsAny(t, AiTipFlowHelper.NO)) {
    return "lucheng_satisfaction_nanhui_thx";
  } else if (StringUtils.containsAny(t, AiTipFlowHelper.YES)) {
    return "lucheng_satisfaction_nanhui_record_phone";
  } else {
    log.info("f3");
    return "lucheng_satisfaction_nanhui_staff_contact";
  }

}