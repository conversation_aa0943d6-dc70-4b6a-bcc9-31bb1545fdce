###
POST http://link-campaign.linkup-test.wke-office.test.wacai.info/api/call/dialSourceList/push/e22716c10a8d468ab3e79e60e6737243
Content-Type: application/json

[
  {
    "batchNo": "29176",
    "phone": "15058795602",
    "userData": "%7B%22flowCode%22%3A%20%22amc_test%22%2C%20%22recordId%22%3A%20826%2C%20%22billId%22%3A%20%223269971%22%2C%20%22leadId%22%3A%20null%7D",
    "dailyFrom": "09",
    "dailyTill": "20",
    "prefixTag": "kuaidai",
    "domain": "alchemist"
  }
]


###

# curl --location 'link-agent-ops.linkup-test.wke-office.test.wacai.info/inbound/select-agent-from-queue'
#--header 'Content-Type: application/json'
#--data '{
#    "queueCode": "1120",
#    "caller": "12312341234"
#}'
POST link-agent-ops.linkup-test.wke-office.test.wacai.info/inbound/select-agent-from-queue
Content-Type: application/x-www-form-urlencoded

queueCode=1120&caller=15000000000

###

