###
GET http://counselor-voice-starter.linkup-test.wke-office.test.wacai.info/front/ai-flow/page?tenantCode=psb_AI&page=1&size=10
x-login-session: 238fa174-a450-4c40-bcc2-247f89af7612

###
GET http://counselor-voice-starter.linkup.k2.wacai.info/front/ai-flow/page?tenantCode=psb_AI&page=1&size=10
x-login-session: c48c0775-f318-4321-842a-2937ba7657e3

###
POST http://counselor-voice-starter.linkup-test.wke-office.test.wacai.info/front/ai-flow/add
Content-Type: application/json

{
  "name": "qj_default_addt1",
  "code": "qj_default_addt1",
  "firstFlowNodeCode": "qj_default_1",
  "comment": null,
  "callEventCallBackUrl": null,
  "notReceiveVoiceInput": true,
  "recordPath": null,
  "waitVoice": null,
  "voiceEndCallBackUrl": null,
  "context": "\n{\n  \"waitVoiceCfg\": {\n    \"enable\": true,\n    \"repeatSelf\": true,\n    \"interval\": 3,\n    \"times\": 2\n  }\n}",
  "type": null,
  "released": true,
  "campaignCode": null,
  "tenantCode": null,
  "createdBy": null,
  "uiData": null
}

###
POST http://counselor-voice-starter.linkup-test.wke-office.test.wacai.info/front/ai-flow/edit
Content-Type: application/json
x-login-session: 238fa174-a450-4c40-bcc2-247f89af7612

{
  "id": "42",
  "name": "qj_default_addt edit test1",
  "comment": "1"
}

###
GET http://counselor-voice-starter.linkup-test.wke-office.test.wacai.info/front/ai-flow-node/list?flowCode=wenling
x-login-session: 238fa174-a450-4c40-bcc2-247f89af7612

###
POST http://counselor-voice-starter.linkup-test.wke-office.test.wacai.info/front/ai-flow-node/add
x-login-session: 238fa174-a450-4c40-bcc2-247f89af7612
Content-Type: application/json

{
  "flowCode": "fc1",
  "name": "哪个镇或街道",
  "code": "wenling_where_you_inaddt",
  "tip": "那您居住在哪个镇或街道?",
  "smsRulerId": null,
  "script": null,
  "end": false,
  "triggerInput": false,
  "receiveSimpleDtmf": false,
  "extInfo": null,
  "comment": null,
  "simpleNextFlowCode": "wenling_tks",
  "transferQueueCode": null,
  "voicePath": "hao_de__nin_ju_zhu_na_ge_zhen_",
  "semanticsMap": null,
  "semanticsTransform": null,
  "remoteVoice": null,
  "context": null,
  "nodeAction": null
}

###
POST http://counselor-voice-starter.linkup-test.wke-office.test.wacai.info/front/ai-flow-node/edit
x-login-session: 238fa174-a450-4c40-bcc2-247f89af7612
Content-Type: application/json

{
  "name": "哪个镇或街道123",
  "code": "wenling_where_you_inaddt"
}


###
POST http://counselor-voice-starter.linkup-test.wke-office.test.wacai.info/front/ai-flow-node/del
x-login-session: 238fa174-a450-4c40-bcc2-247f89af7612
Content-Type: application/x-www-form-urlencoded

id=175

###
POST http://counselor-voice-starter.linkup-test.wke-office.test.wacai.info/front/ai-flow/copy
x-login-session: 238fa174-a450-4c40-bcc2-247f89af7612
Content-Type: application/x-www-form-urlencoded

id=44&newFlowCode=newFlowCode2
