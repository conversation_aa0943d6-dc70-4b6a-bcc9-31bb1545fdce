###
POST http://localhost:8081/ai/callBack
Content-Type: application/json

{
  "callId": "linkup304920844",
  "end": false,
  "text": "您好！这里是盖北派出所，想问下您当前是不是居住在盖北镇？",
  "function": "fake_data",
  "status": "fake_data"
}


###
POST https://linkup.test.wacai.info/campaign-server/dial/p2p
Content-Type: application/json
X-Login-Session: ac806166-2ebe-4c01-90d9-07e30fc65e37

{
  "account": "AI_newAgent4_psb_AI",
  "target": "***********",
  "type": "P2P",
  "trunk": "8181",
  "userData": "%7B%20%20%22phoneIsState%22%3A%201%2C%20%20%22flowCode%22%3A%22lucheng_leaving%22%7D"
}



###
POST http://link-state.linkup-test.wke-office.test.wacai.info/fs/ai/push
Content-Type: application/json

{
  "lists": [
    "***********"
  ],
  "trunk": "8181",
  "accounts": [
    "AI_newAgent3_psb_AI"
  ]
}



//"target": "***********",
//  "target": "***********",


###
POST link-agent-ops.linkup-test.wke-office.test.wacai.info/inbound/select-agent-from-queue
Content-Type: application/x-www-form-urlencoded

queueCode=1108&phone=***********

###
POST 10.0.19.150:8080/inbound/select-agent-from-queue
Content-Type: application/x-www-form-urlencoded

queueCode=1187&phone=***********

###
POST 172.30.130.117:8080/agent/status/701297/1

###     "userData": "%7B%20%20%22phoneIsState%22%3A%201%2C%20%20%22flowCode%22%3A%20%22lucheng_leaving%22%7D",
###
POST http://link-campaign.linkup-test.wke-office.test.wacai.info/api/call/dialSourceList/push/1386e49d6c7b48bbae367fdb3ee3ba7a
Content-Type: application/json

[
  {
    "phone": "***********",
    "dailyFrom": "08",
    "dailyTill": "23",
    "batchNo": "13505",
    "userData": "%7B%20%22flowCode%22%3A%20%22lucheng_satisfaction_20240617%22%2C%22voiceName%22%3A%22bj%22%7D",
    "prefixTag": ""
  }
]


### ai-voice
POST http://link-campaign.linkup-test.wke-office.test.wacai.info/api/call/dialSourceList/push/e22716c10a8d468ab3e79e60e6737243
Content-Type: application/json

[
  {
    "phone": "***********",
    "dailyFrom": "08",
    "dailyTill": "23",
    "batchNo": "13508",
    "userData": "%7B%20%22flowCode%22%3A%20%22amc_test%22%2C%22voiceName%22%3A%22bj%22%7D",
    "prefixTag": ""
  }
]



###
POST http://counselor-voice-starter.linkup-test.wke-office.test.wacai.info/ai-action/do-ai-action
Content-Type: application/json

{
  "message": "a是。",
  "sessionId": "linkup304927446",
  "phone": "***********",
  "flowCode": "lucheng_satisfaction_nanhui",
  "simpleNextFlowCode": "lucheng_satisfaction_nanhui_thx"
}



###
POST https://linkup.wacai.info/campaign-server/call/dialSourceList/push/318d52275c6147b8880764beb58dcd38
Content-Type: application/json

[
  {
    "phone": "***********",
    "dailyFrom": "08",
    "dailyTill": "23",
    "batchNo": "13509",
    "userData": "%7B%20%20%22phoneIsState%22%3A%201%2C%20%20%22flowCode%22%3A%22ruian_leaving%22%7D",
    "prefixTag": ""
  }
]

###
GET counselor-voice-starter.linkup-test.wke-office.test.wacai.info/test/factory


###
POST https://linkup.wacai.info/campaign-server/dial/p2p
Content-Type: application/json

{
  "account": "feixing_pharos",
  "target": "***********",
  "type": "P2P",
  "trunk": "8797",
  "userData": "%7B%20%20%22phoneIsState%22%3A%201%2C%20%20%22flowCode%22%3A%22lucheng_leaving%22%7D"
}



###
POST  http://link-opensips-fs.ops.linkup-test.wke-office.test.wacai.info/ac/checkout/700333?checkInWay=soft_phone&host=**************


###

# curl -X POST https://linkup.wacai.info/campaign-server/call/dialSourceList/push/318d52275c6147b8880764beb58dcd38 -H "Content-Type: application/json" -d '[{"phone": "***********", "dailyFrom": "08", "dailyTill": "23", "batchNo": "25543", "userData": "%7B%20%22flowCode%22%3A%22lucheng_satisfaction%22%7D", "prefixTag": "************"} ]
POST https://linkup.wacai.info/campaign-server/call/dialSourceList/push/318d52275c6147b8880764beb58dcd38
Content-Type: application/json

[
  {
    "phone": "***********",
    "dailyFrom": "08",
    "dailyTill": "23",
    "batchNo": "25543",
    "userData": "%7B%20%22flowCode%22%3A%20%22ruian_leaving%22%2C%22voiceName%22%3A%22bj%22%7D",
    "prefixTag": "************"
  }
]

###

#,
#  {
#    "phone": "13083966536",
#    "dailyFrom": "08",
#    "dailyTill": "23",
#    "batchNo": "25543",
#    "userData": "%7B%20%22flowCode%22%3A%20%22lucheng_leaving%22%7D",
#    "prefixTag": "************"
#  }


# curl -X POST http://link-campaign.linkup-test.wke-office.test.wacai.info/api/call/dialSourceList/push/1386e49d6c7b48bbae367fdb3ee3ba7a -H "Content-Type: application/json" -d '[{"phone": "***********", "dailyFrom": "08", "dailyTill": "24", "batchNo": "25543666", "userData": "%20%7B%20%22flowCode%22%3A%22yc_fasu%22%7D", "prefixTag": "************"} ]'
#1386e49d6c7b48bbae367fdb3ee3ba7a
POST http://link-campaign.linkup-test.wke-office.test.wacai.info/api/call/dialSourceList/push/fb2bf40e7c8c40d6a74396583e2793f1
Content-Type: application/json

[
  {
    "phone": "***********",
    "dailyFrom": "08",
    "dailyTill": "24",
    "batchNo": "25543669",
    "userData": "%20%7B%20%22flowCode%22%3A%22yc_test%22%7D",
    "prefixTag": "************"
  }
]

###

###
POST http://counselor-voice-starter.linkup-test.wke-office.test.wacai.info/ai-flow-node/invalid-cache-all


###
POST http://counselor-voice-starter.linkup-test.wke-office.test.wacai.info/ai-flow-node/cluster/invalid-cache-all

###
POST http://counselor-voice-starter.linkup-test.wke-office.test.wacai.info/ai-flow-node/cluster/invalid-cache
Content-Type: application/x-www-form-urlencoded

code=lucheng_satisfaction_transfer

###
POST http://counselor-voice-starter.linkup-test.wke-office.test.wacai.info/ai-flow-node/cluster/cache-preheating


### online
POST http://counselor-voice-starter.linkup.k2.wacai.info/ai-flow-node/cluster/cache-preheating

###
GET http://linkup.wacai.info/config-server/script-compose/wav/66ac46959b354e4a973e57a3%E5%A5%BD%E7%9A%84%EF%BC%8C%E6%84%9F%E8%B0%A2%E6%82%A8%E7%9A%84%E9%85%8D%E5%90%88%EF%BC%8C%E7%A5%9D%E6%82%A8%E7%94%9F%E6%B4%BB%E6%84%89%E5%BF%AB%EF%BC%8C%E5%86%8D%E8%A7%81.wav
