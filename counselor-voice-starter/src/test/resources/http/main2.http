###cache-preheating online
# curl -X POST --location "http://counselor-voice-starter.linkup.k2.wacai.info/ai-flow-node/cluster/cache-preheating"
POST http://counselor-voice-starter.linkup.k2.wacai.info/ai-flow-node/cluster/cache-preheating

###
POST link-agent-ops.linkup-test.wke-office.test.wacai.info/agent/hard-phone/checkin/701317
Content-Type: application/json

{}

### acm online test
POST https://linkup.wacai.info/campaign-server/call/dialSourceList/push/318d52275c6147b8880764beb58dcd38
Content-Type: application/json

[
  {
    "batchNo": "30648",
    "phone": "15058795602",
    "userData": "%7B%22flowCode%22%3A%20%22quzhou_travel_20240729%22%2C%20%22recordId%22%3A%20609078%7D",
    "dailyFrom": "09",
    "dailyTill": "20",
    "prefixTag": "057757192210"
  }
]


###
POST http://counselor-voice-starter.ai-voice.wke-office.test.wacai.info/ai-flow-node/cluster/cache-preheating


#

### front test
POST http://link-campaign.linkup-test.wke-office.test.wacai.info/api/call/dialSourceList/push/1386e49d6c7b48bbae367fdb3ee3ba7a
Content-Type: application/json

[
  {
    "phone": "15058795602",
    "dailyFrom": "08",
    "dailyTill": "23",
    "batchNo": "13505",
    "userData": "%7B%22flowCode%22%3A%22test_code_key_word%22%7D",
    "prefixTag": ""
  }
]