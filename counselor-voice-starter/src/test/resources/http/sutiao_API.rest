
### 测试环境推送活动名单(中原)
POST http://link-campaign.linkup-test.wke-office.test.wacai.info/api/call/dialSourceList/push/281e287cc9f141d08fa3e5c06e3c07a4
Content-Type : application/json

[
    {
        "phone": "13758171315",
        "dailyFrom": "08",
        "dailyTill": "23",
        "batchNo": "433",
        "userData": "%7B%22kaseId%22%3A611599%2C%22bizType%22%3A%22AI%22%2C%22flowCode%22%3A%22AI_PPCY%22%2C%22transferQueue%22%3A%221223%22%2C%22remoteVoiceParam%22%3A%7B%22jsonData%22%3A%7B%22courtName%22%3A%22%E9%87%8D%E5%BA%86%E5%B8%82%E9%BB%94%E6%B1%9F%E5%8C%BA%E4%BA%BA%E6%B0%91%E6%B3%95%E9%99%A2%22%2C%22gender%22%3A%22%E5%85%88%E7%94%9F%22%2C%22loanAmt%22%3A%22109.75%22%2C%22loanOverdueDays%22%3A26%2C%22loanPeriods%22%3A45%2C%22loanPlatformName%22%3A%22%E5%BF%AB%E8%B4%B7%22%2C%22loanRepayAmt%22%3A17275%2C%22loanTime%22%3A%222025%E5%B9%B41%E6%9C%883%E6%97%A5%22%2C%22orgName%22%3A%22%E9%87%8D%E5%BA%86%E7%BB%85%E5%90%88%E4%BC%81%E4%B8%9A%E7%AE%A1%E7%90%86%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%22%2C%22userName%22%3A%22%E5%A4%A9%E4%B8%8B%E6%AC%A3%22%7D%7D%2C%22batchId%22%3A730%2C%22needAssign%22%3Atrue%2C%22collectionId%22%3A611599%7D",
        "prefixTag": "VIFWMIBDEQ37",
        "listType":"AI"
    }
]

### 测试

POST http://link-campaign.linkup-test.wke-office.test.wacai.info/api/call/dialSourceList/push/ed4789f51ffd492fa69867fbacdd230b
Content-Type : application/json

[
    {
        "phone": "19357112067",
        "dailyFrom": "08",
        "dailyTill": "23",
        "batchNo": "44",
        "userData": "%7B%22recordId%22%3A%226233%22%2C%22loanTime%22%3A%222023-10-01%22%2C%22loanPeriods%22%3A%228%22%2C%22gender%22%3A%22%E5%85%88%E7%94%9F%22%2C%22loanSource%22%3A%22%E5%BF%AB%E8%B4%B7%22%2C%22name%22%3A%22%E5%88%98%E5%B0%8F%E7%BA%A2%22%2C%22loanRepayAmt%22%3A%22900.00%22%2C%22currentDueAmount%22%3A%22630.00%22%2C%22billDate%22%3A%222023-10-18%22%2C%22serialNo%22%3A%224%22%7D",
        "prefixTag": "VIFWMIBDEQ37",
        "listType":"AI"
    }
]

### 测试按键信息

POST http://link-campaign.linkup-test.wke-office.test.wacai.info/api/call/dialSourceList/push/f007b1cff3e34f01be07d825c00444f2
Content-Type : application/json

[
    {
        "phone": "13758171315",
        "dailyFrom": "08",
        "dailyTill": "23",
        "batchNo": "3322",
        "userData": "%7B%22kaseId%22%3A1406667%2C%22bizType%22%3A%22AUTOMATIC%22%2C%22flowCode%22%3A%22jc_ivr_3%22%2C%22remoteVoiceParam%22%3A%7B%22voiceFrom%22%3A%22aiqi%22%2C%22volume%22%3A%2251%22%2C%22intonation%22%3A%221%22%2C%22jsonData%22%3A%7B%22courtName%22%3A%22%E9%87%8D%E5%BA%86%E5%B8%82%E9%BB%94%E6%B1%9F%E5%8C%BA%E4%BA%BA%E6%B0%91%E6%B3%95%E9%99%A2%22%2C%22gender%22%3A%22%E5%85%88%E7%94%9F%22%2C%22loanAmt%22%3A%220%22%2C%22loanOverdueDays%22%3A4221%2C%22loanPlatformName%22%3A%22%E5%B9%B3%E5%AE%89%E4%BF%A1%E7%94%A8%E5%8D%A1%22%2C%22loanRepayAmt%22%3A0.00%2C%22loanTime%22%3A%222006%E5%B9%B43%E6%9C%8821%E6%97%A5%22%2C%22orgName%22%3A%22%E5%B9%B3%E5%AE%89%E9%93%B6%E8%A1%8C%E8%82%A1%E4%BB%BD%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%E9%87%8D%E5%BA%86%E5%A4%A7%E6%B8%A1%E5%8F%A3%E6%94%AF%E8%A1%8C%22%2C%22userName%22%3A%22%E7%8E%8B%E5%8B%87%22%7D%2C%22vocalSpeed%22%3A%221%22%7D%2C%22batchId%22%3A3108%2C%22collectionId%22%3A1406667%7D",
        "prefixTag": "VIFWMIBDEQ37"
    }
]
### 测试环境服务加载接口

POST http://counselor-voice-starter.linkup-test.wke-office.test.wacai.info/ai-flow-node/cluster/cache-preheating





###生产环境推送活动名单
POST  https://linkup.wacai.info/campaign-server/call/dialSourceList/push/6e62d3305b874a4aa735c37b80045e47
Content-Type: application/json

[
    {
        "phone": "19357112067",
        "dailyFrom": "08",
        "dailyTill": "24",
        "batchNo": "73",
        "userData": "%7B%22kaseId%22%3A1057893%2C%22bizType%22%3A%22AUTOMATIC%22%2C%22flowCode%22%3A%22jc_ivr_3%22%2C%22remoteVoiceParam%22%3A%7B%22voiceFrom%22%3A%22aiqi%22%2C%22volume%22%3A%2251%22%2C%22intonation%22%3A%221%22%2C%22jsonData%22%3A%7B%22courtName%22%3A%22%E9%87%8D%E5%BA%86%E5%B8%82%E9%BB%94%E6%B1%9F%E5%8C%BA%E4%BA%BA%E6%B0%91%E6%B3%95%E9%99%A2%22%2C%22gender%22%3A%22%E5%85%88%E7%94%9F%22%2C%22loanAmt%22%3A%2213200%22%2C%22loanOverdueDays%22%3A211%2C%22loanPeriods%22%3A12%2C%22loanPlatformName%22%3A%22%E6%96%B0%E7%BD%91%E5%93%88%E5%93%88%E8%A1%8C%22%2C%22loanRepayAmt%22%3A8806.96%2C%22loanTime%22%3A%222024%E5%B9%B47%E6%9C%8823%E6%97%A5%22%2C%22orgName%22%3A%22%E8%BE%BD%E4%BF%A1%E8%B5%84%E4%BA%A7%E7%AE%A1%E7%90%86%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%22%2C%22userName%22%3A%22%E4%BC%8D%E5%92%8C%22%7D%2C%22vocalSpeed%22%3A%221%22%7D%2C%22batchId%22%3A1781%2C%22collectionId%22%3A1057893%7D"
        
    }
]


### AI
POST  https://linkup.wacai.info/campaign-server/call/dialSourceList/push/623bad57eef14961ab9bddb23f72ddac
Content-Type: application/json

[
    {
        "phone": "13758171315",
        "dailyFrom": "08",
        "dailyTill": "24",
        "batchNo": "876",
        "userData": "%7B%22recordId%22%3A%22757217%22%2C%22loanTime%22%3A%222025-06-11%22%2C%22loanPeriods%22%3A%2212%22%2C%22gender%22%3A%22%E5%85%88%E7%94%9F%22%2C%22loanSource%22%3A%22%E4%B8%AD%E5%8E%9F%E6%B6%88%E8%B4%B9%E9%87%91%E8%9E%8D%22%2C%22name%22%3A%22%E6%B2%88%E4%BA%91%E6%99%95%22%2C%22loanRepayAmt%22%3A%22200%22%2C%22currentDueAmount%22%3A%22204.4%22%2C%22billDate%22%3A%222025-08-11%22%2C%22serialNo%22%3A%221%22%7D%09",
         "listType":"AI"
        
    }
]