pcm.!default {
    type asym
    playback.pcm "plug:dmix"
    capture.pcm "hw:0,0"
}

pcm.dmixer {
    type dmix
    ipc_key 1024
    slave {
        pcm "hw:0,0"
        period_time 0
        period_size 1024
        buffer_size 8192
        rate 8000
    }
    bindings {
        0 0
        1 1
    }
}

pcm.!capture {
    type hw
    card 0
    device 0
    rate 8000
}

pcm.!playback {
    type plug
    slave.pcm "dmixer"
    rate 8000
}

