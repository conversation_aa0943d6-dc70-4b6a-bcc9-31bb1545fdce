#!/bin/bash

# get real path of shell script
get_real_path() {
    local f="$1"
    while [ -h "$f" ]; do
        ls=`ls -ld "$f"`
        link=`expr "$ls" : '.*-> \(.*\)$'`
            if expr "$link" : '/.*' > /dev/null; then
            f="$link"
        else
            f=`dirname "$f"`/"$link"
        fi
    done
    echo "$f"
}

prg_path=$(get_real_path "$0")
echo "Script path [$prg_path]"
pushd $(dirname $prg_path)
WORK_DIR=$(pwd)
echo "Work dir [$WORK_DIR]"

case "${OSTYPE//[0-9.-]*/}" in
  darwin)
    echo "Platform: MacOS"
    JNI_LIB_DIR=$(cd ../src/main/resources/lib/MacOS; pwd)
    LIB_SUFFIX="dylib"
    JNI_LIB_SUFFIX="jnilib"
    ;;

  linux)
    echo "Platform: Linux"
    JNI_LIB_DIR=$(cd ../src/main/resources/lib/Linux; pwd)
    LIB_SUFFIX="so"
    JNI_LIB_SUFFIX="so"
    ;;
  *)

  echo "Unsupported Operating system $OSTYPE"
  exit 1
esac

tar_file=$(find . -maxdepth 1 -type f -name 'pjproject-*.tar.gz' -exec basename {} \;)

if [[ -f "$tar_file" ]]; then
    echo "PJ project source tar [$tar_file] found";
else
    echo "No PJ project source tar found";
    exit 1
fi

src_dir="$WORK_DIR/${tar_file%.tar.gz}"
echo "PJ project source dir [$src_dir]"

target="$WORK_DIR/target"
pjsua_package="org/pjsip/pjsua2"
pjsua_java_dir="$(dirname $WORK_DIR)/src/main/java/$pjsua_package"
jni_lib_name="libpjsua2-jni.$LIB_SUFFIX"
jni_lib_target="$JNI_LIB_DIR/$jni_lib_name"

tar xzvf $tar_file \
&& pushd "$src_dir" \
&& patch -d . -p 1 < $WORK_DIR/pjproject.patch \
&& ./configure CFLAGS='-O2 -fPIC' \
    --prefix=$target \
    --with-bcg729=$target \
    --disable-video \
    --disable-opus \
    --disable-ssl \
    --disable-darwin-ssl \
    --disable-libyuv \
    --disable-libwebrtc \
    --disable-opencore-amr \
    --disable-sdl \
    --disable-ffmpeg \
    --disable-v4l2 \
    --disable-openh264 \
    --disable-vpx \
&& echo '#define PJMEDIA_HAS_BCG729 1
#define PJSUA_MAX_CALLS 256
#define PJSUA_MAX_ACC 256
#define PJ_IOQUEUE_MAX_HANDLES 512
#define PJSUA_MAX_PLAYERS 512' > pjlib/include/pj/config_site.h \
&& make -j 16 dep \
&& make -j 16 \
&& mv pjsip-apps/bin/pjsua-* "$target/pjsua" \
&& pushd pjsip-apps/src/swig/java \
&& make \
&& mv "output/libpjsua2.$JNI_LIB_SUFFIX" "$jni_lib_target" \
&& rm -rf "$pjsua_java_dir"/*.java \
&& mv "output/$pjsua_package"/*.java "$pjsua_java_dir" \
&& ls -lh "$pjsua_java_dir" \
&& popd > /dev/null

LIBS=$(grep '^LIBS=' "$src_dir/config.log" |
sed 's/LIBS=//' |
sed "s/'//g" |
sed -E 's/[[:space:]]+/ /g')

STATIC_LIBS=$(make -pn | grep '^PJ_LIBXX_FILES *:=' | sort | uniq |
grep PJ_LIBXX_FILES | sed -e 's/^PJ_LIBXX_FILES *:= *//')

pj_lib_name="libpjproject.$LIB_SUFFIX"
pj_lib_target="$target/lib/$pj_lib_name"

if [[ $LIB_SUFFIX = "dylib" ]]; then
    g++ -fpic -shared \
    -L$target/lib \
    $LIBS \
    -Wl,-all_load \
    $STATIC_LIBS \
    -o $pj_lib_target

    echo "MacOS, update DyLib id."
    install_name_tool -id "@loader_path/$pj_lib_name" "$pj_lib_target"
    install_name_tool -id "@loader_path/$jni_lib_name" "$jni_lib_target"
else
    echo "Build LibPJProject on Linux"
    gcc -fpic -shared \
    -o $pj_lib_target \
    -L$target/lib \
    $LIBS \
    -Wl,--whole-archive \
    $STATIC_LIBS \
    -Wl,--no-whole-archive
fi

popd > /dev/null
echo "Remove pj project source [$src_dir]"
rm -rf "$src_dir"

popd > /dev/null

