FROM dockerhub.test.wacai.info/wse/centos-jdk-jemalloc:1.8.0-221
CMD ["bash"]
ENV TZ CST-8
RUN yum install -y gcc gcc-c++ make autoconf automake libtool pcre-devel alsa-lib-devel alsa-utils pulseaudio alsa-plugins-pulseaudio
RUN yum install -y patch
RUN mkdir -p /data/program/voice/pjproject
ADD pjproject-2.10.tar.gz /data/program/voice/pjproject
ADD swig-4.0.1.tar.gz /data/program/voice/swig
WORKDIR /data/program/voice/pjproject/pjproject-2.10
RUN ./configure
RUN make dep
RUN make
WORKDIR /data/program/voice/swig/swig-4.0.1
RUN ./configure
RUN make && make install
ENV JAVA_HOME=/bin
RUN yum -y install epel-release
RUN rpm --import https://li.nux.ro/download/nux/RPM-GPG-KEY-nux.ro
RUN rpm -Uvh https://li.nux.ro/download/nux/dextop/el7/x86_64/nux-dextop-release-0-1.el7.nux.noarch.rpm
RUN yum -y install ffmpeg ffmpeg-devel
RUN yum -y install wget
RUN mkdir -p /lib/linux
ADD libpjsua2-jni.so /lib/linux
RUN chmod -R 777 /lib/linux/libpjsua2-jni.so
RUN rm /etc/asound.conf
ADD asound.conf /etc/
RUN chmod -R 777 /etc/asound.conf
ENV JAVA_TOOL_OPTIONS="-Dfile.encoding=UTF8"
RUN mkdir -p /data/program/file/audio
RUN mkdir -p /data/program/file/recordings/temp
RUN mkdir -p /data/program/file/speech/temp


RUN mkdir -p /etc/voice/temp/
RUN mkdir -p /etc/voice/speech/temp/
RUN mkdir -p /etc/voice/template/temp/

RUN yum -y install jq  \
    procps \
    && yum -y clean all  && rm -rf /var/cache


COPY etc/voice/* /etc/voice/template/temp/

WORKDIR ${workDir}


#WORKDIR /data/program/voice/pjproject/pjproject-2.10/pjsip-apps/src/swig/java
#RUN make
#RUN make install
#CMD ["java","-version"]